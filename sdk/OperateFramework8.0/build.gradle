// Top-level build file where you can add configuration options common to all sub-projects/modules.

ext{
    useLib=false
}
buildscript {
    
    repositories {
        maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
        google()
//        jcenter()
        
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:3.6.3'
        

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
        google()
//        jcenter()
        
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
