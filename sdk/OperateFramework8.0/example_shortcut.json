{"bg": "", "contents": [{"bg": "", "contents": [{"extra": {"params": {"name": "龙岭谜窟", "age": "22"}}, "plugin_info": {"type": "Third", "packagename": "com.tianci.www", "category": "2", "params": {"a": "aaaa"}, "data": "{\"packagename\":\"com.tianci.movieplatform\",\"versioncode\":\"-1\",\"dowhat\":\"startActivity\",\"bywhat\":\"action\",\"byvalue\":\"coocaa.intent.vip.center\",\"params\":{\"source_id\":\"1\",\"business_type\":\"0\"},\"exception\":{\"name\":\"onclick_exception\",\"value\":{\"packagename\":\"com.tianci.appstore\",\"dowhat\":\"startActivity\",\"versioncode\":\"-1\",\"params\":{\"key\":\"id\",\"value\":\"com.tianci.movieplatform\"},\"byvalue\":\"coocaa.intent.action.APP_STORE_DETAIL\",\"bywhat\":\"action\"}}}"}, "width": 680, "x": 0, "y": 0, "focusable": 1, "id": "1023", "type": "Plugin", "height": 280, "parents": "Block"}, {"extra": {"block_content": {"imgs": {"corner_icons": [], "poster": {"images": ["http://img.sky.fs.skysrt.com/tvos6_imgs_master/20200608/20200608153907590837_560*322.jpg"]}}, "action": "{\"packagename\" :\"com.tianci.movieplatform\",\"versioncode\" : \"\",\"bywhat\" : \"action\",\"byvalue\" : \"coocaa.intent.movie.detailinfo\",\"dowhat\" : \"startActivity\",\"params\" : {\"id\" : \"_oqy_733185100\"},\"exception\" : \"\"}"}}, "width": 320, "x": 720, "y": 0, "focusable": 1, "id": "101929640", "type": "Block", "height": 280}, {"extra": {}, "width": 680, "x": 1080, "y": 0, "focusable": 1, "id": "101929641", "type": "Block", "height": 280}, {"extra": {}, "width": 1760, "x": 0, "y": 320, "focusable": 1, "id": "101929642", "type": "Block", "height": 120}, {"extra": {}, "width": 1760, "x": 0, "y": 480, "focusable": 1, "id": "101929643", "type": "Block", "height": 420}], "extra": {}, "focusable": 0, "height": 0, "id": "1024125", "parents": "", "type": "Panel", "width": 0, "x": 0, "y": 0}, {"bg": "", "contents": [{"extra": {}, "width": 680, "x": 0, "y": 0, "focusable": 1, "id": "1023", "type": "Plugin", "height": 280, "parents": "Block"}, {"extra": {}, "width": 320, "x": 720, "y": 0, "focusable": 1, "id": "101929640", "type": "Block", "height": 280}, {"extra": {}, "width": 680, "x": 1080, "y": 0, "focusable": 1, "id": "101929641", "type": "Block", "height": 280}, {"extra": {}, "width": 1760, "x": 0, "y": 320, "focusable": 1, "id": "101929642", "type": "Block", "height": 120}, {"extra": {}, "width": 1760, "x": 0, "y": 480, "focusable": 1, "id": "101929643", "type": "Block", "height": 420}], "extra": {}, "focusable": 0, "height": 0, "id": "1024125", "parents": "", "type": "Panel", "width": 0, "x": 0, "y": 0}], "extra": {}, "focusable": 0, "height": 0, "id": "", "parents": "", "type": "Expander", "width": 0, "x": 0, "y": 0}