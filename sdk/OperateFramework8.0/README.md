
## 新主页使用说明
### 概述

#### 此工程是新主页卡片的工程
### 使用方法 



#### 1、调用代码，解析jsonString，转成View
```
//jsonString是后台的数据，见同目录下的文件example.json
ContainerV8 c = JSON.parseObject(jsonString, ContainerV8.class);
SupportUtil.parseContent(c, id);//id是版面id，主页会传过来
IPresenterV8 mMainPresenter = PresenterFactory.getInstance().createPresenter(
           c.rawType, mContext);
//设置数据
mMainPresenter.setContainer(c);
//设置点击事件处理
mMainPresenter.setOnItemClickListener(this);
//实现边缘回调
mMainPresenter.setOnBoundaryListener(this);

//获取View
View view = mMainPresenter.getView()

```


#### 2、添加APP自定义组件

1）、APP写一个类实现IPresenterFactory接口，里面通过type类型返回对应的IPresenter对象，如

```
public class MyPresenterFactory implements IPresenterFactory {
    public MyPresenterFactory() {
         //在这里注册支持的type的类型，后面还会有介绍
         SupportUtilV8.addSupportType(TestMyBlockPresenter.TYPE, Plugin.class);
    }

    @Override
    public IPresenterV8 createPresenter(String type, Context context) {
        if(TestMyBlockPresenter.TYPE.equals(type)){
            return new TestMyBlockPresenter(context);
        }
        return null;
    }
}
```

​    在Application的onCreate中，添加注册，如是插件加载的话，可在自己的onInit中添加注册，如：

```
PresenterFactory.getInstance().addExtraFactory(new MyPresenterFactory());
```



2）、添加自定义Presenter实现

IPresenter对象可以选择继承自BlockPresenter，也可以自己实现，只要实现 IPresenter接口接口，如本示例中的：

```
public class TestMyBlockPresenter extends BlockPresenterV8 
    public final static String TYPE = "BLOCK_MY";//这个就是和json数据plugin中type保持一致的名字
    public TestMyBlockPresenter(Context context) {
        super(context);
    }

    @Override
    public BlockLayout makeView(Context context) {
        return new TestMyBlockLayout(context);//返回自定义的UI
    }
}
```



3）、添加实现自定义View，可以选择继承自BlockLayout，如本次示例中的TestMyBlockLayout

```
public class TestMyBlockLayout extends BlockLayoutV8 {
    private TextView textView;

    public TestMyBlockLayout(@NonNull Context context) {
        super(context);

        textView = new TextView(context);
        textView.setTextSize(Util.Dpi(40));
        textView.setTextColor(Color.WHITE);
        addView(textView, new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT, Gravity.CENTER));
    }
}
```

   

4）、处理数据参数

​       一般是在extends BlockLayout中使用解析后的数据，来构建和刷新UI，可以覆写setBlockData方法，通过container.parsePluginParams(MyData.class)方法反序列到json数据中的param数据，如本次示例中，example.json中的数据格式是：

1、 对于普通的Block

```
"extra":{
    "block_content": {
            "imgs": {
                "corner_icons": [],            
                "poster": {
                    "images": []    
                }
            },
            "action": "{\"packagename\" :\"com.tianci.movieplatform\",\"versioncode\" : \"\",\"bywhat\" : \"action\",\"byvalue\" : \"coocaa.intent.movie.detailinfo\",\"dowhat\" : \"startActivity\",\"params\" : {\"id\" : \"_oqy_733185100\"},\"exception\" : \"\"}"
        },
        "params": "{}"
}

```

  2、类型为Plugin类型

```
"plugin_info": {
          "type": "Third",
          "packagename": "com.tianci.www",
          "category": "2",
          "params": {
            "a": "aaaa"
          },
          "data": ""
        }
```



​     params对应的定义数据类是：

```
public class TestMyBlockData implements Serializable {
    public String name;
    public String age;
}
```

​     那么反序列化，加上处理数据，调用代码就是：

```
@Override
public void setBlockData(ContainerV8 container) {
   super.setBlockData(container);
   TestMyBlockData myBlockData = container.parsePluginParams(TestMyBlockData.class);
   textView.setText(myBlockData.name + " : " + myBlockData.age);
}
```

​    即可实现，数据刷新UI。

​    注意，每一种不同的plugin，params对应的数据结构很可能都是不一样的，需要自己处理；

​      可以通过makeFocusView方法，复写焦点框UI，如使用面落焦，或者自定义焦点框：

```
    @Override
    public View makeFocusView() {
        View view = new View(getContext());
        view.setBackgroundColor(Color.parseColor("#4000FF00"));
        return view;
    }
```

​       setFocus方法是焦点变化处理，如：

```
    @Override
    public void setFocus(boolean focus) {
        super.setFocus(focus);
        textView.setTextColor(focus ? Color.YELLOW : Color.WHITE);
    }
```



5)、注册新类型的Block插件

​      通过调用 SupportUtil.addSupportType(String type,Class<T> class)即可，在8.0图文框架中，后面的class默认传Plugin.class，key是自己的定义，本次示例中代码如下：

```
SupportUtilV8.addSupportType(TestMyBlockPresenter.TYPE, Plugin.class);
```

​      调用时机，需要在构建Container的parseContent之前，最好就像上面示例代码中，如在MyPresenterFactory的构造方法中把自己APP支持的type添加注册进来



6)、实现点击效果

​      对Presenter设置点击事件之后,在回调中，对生成的Intent添加自己需要的flag,然后跳转参考下列代码：

  ```
 Object mContentObject = t.contentObject;
 OnClickDataV8 mOnClickData =null;
 //根据自己的类型选中Block或者Plugin
 if (mContentObject instanceof BlockV8){
     ((BlockV8) mContentObject).block_content.parseAction();
     mOnClickData =   ((BlockV8) mContentObject).block_content.parsedAction;
 }
 if (mContentObject instanceof Plugin){
     ((Plugin) mContentObject).parseAction();
     mOnClickData =((Plugin) mContentObject).parsedAction;
}
Intent mIntent = mOnClickData.buildIntent(pluginContext);
  ```



#### 


