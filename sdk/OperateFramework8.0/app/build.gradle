//if(rootProject.ext.useLib){
    apply plugin: 'com.android.library'
//}else{
//    apply plugin: 'com.android.application'
//}

android {
    compileSdkVersion COMPILE_SDK_VERSION
    buildToolsVersion BUILDTOOLS_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
//    libraryVariants.all {
//        varinat ->
//            varinat.outputs.all {
//                outputFileName = 'ui.aar'
//            }
//    }

}

dependencies {
    implementation  'com.android.support:appcompat-v7:23.2.0'
    implementation  'com.android.support:recyclerview-v7:23.2.0'
    implementation 'com.alibaba:fastjson:1.1.54.android'
}
