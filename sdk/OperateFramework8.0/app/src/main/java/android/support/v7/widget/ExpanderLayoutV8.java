/**
 * **************** Copyright (C) *****************
 * <p/>
 * Version       Date            Author
 * ─────────────────────────────────────
 * V1.0        2017/1/4         yellowlgx
 * <p/>
 * **************** End of Head *******************
 */
package android.support.v7.widget;

import android.content.Context;
import android.os.SystemClock;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;

import com.coocaa.uisdk.expander.scroll.BaseLinearLayoutManagerV8;
import com.coocaa.uisdk.listener.IBlockFocusLightView;
import com.coocaa.uisdk.listener.IView;
import com.coocaa.uisdk.listener.OnBoundaryListenerV8;
import com.coocaa.uisdk.model.ContainerV8;
import com.coocaa.uisdk.presenter.ExpanderPresenterV8;
import com.coocaa.uisdk.utils.UtilV8;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class ExpanderLayoutV8 extends RecyclerView implements IView {
    private boolean mDropKey = false;
    private static final int DROP_TIME = 150;
    private long mLastTime = 0;
    private BaseLinearLayoutManagerV8 mLayoutManager;
    private int mMinSlop;

    public ExpanderLayoutV8(Context context) {
        super(context);
        setPadding(UtilV8.Div(80), 0, UtilV8.Div(80), 0);
        setItemAnimator(null);//去除Item加载动画，解决由于刷新导致的焦点混乱问题
        setOverScrollMode(OVER_SCROLL_NEVER);
        setClipToPadding(false);
        setClipChildren(false);
        setDescendantFocusability(FOCUS_AFTER_DESCENDANTS);
        ViewConfiguration config = ViewConfiguration.get(context);
        mMinSlop = config.getScaledDoubleTapSlop();
    }

    @Override
    public void setLayoutManager(LayoutManager layout) {
        super.setLayoutManager(layout);
        mLayoutManager = (BaseLinearLayoutManagerV8) layout;
        if (((BaseLinearLayoutManagerV8) layout).getOrientation() == OrientationHelper.HORIZONTAL) {
            setClipToPadding(false);
            setClipChildren(false);
        }
    }

    public void dropKeyOneTime() {
        mDropKey = true;
        mLastTime = SystemClock.uptimeMillis();
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (mDropKey && (SystemClock.uptimeMillis() - mLastTime) > DROP_TIME) {
            mDropKey = false;
        }
        if (mDropKey) {
            return true;
        }
        return super.dispatchKeyEvent(event);
    }



    float downX, downY;

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        int action = event.getAction();
        switch (action) {
            case MotionEvent.ACTION_DOWN:
            case MotionEvent.ACTION_MOVE:
                downX = event.getX();
                downY = event.getY();
                break;
            case MotionEvent.ACTION_UP:
                float upX = event.getX();
                float upY = event.getY();
                float diffX = upX - downX;
                float diffY = upY - downY;
                Log.e(ExpanderPresenterV8.TAG, "diffX : "+ diffX + " ,diffY : "+diffY);
                if (Math.abs(diffX) >= Math.abs(diffY)) {

                } else {
                    if (Math.abs(diffY) < mMinSlop){
                        Log.d(ExpanderPresenterV8.TAG, "Does not meet the conditions ..." );
                        return false;
                    }
                    if (!canScrollVertically(-1) && diffY > 0) {
                        Log.d(ExpanderPresenterV8.TAG, "ExpanderLayout onTopBoundary.");
                        if (mListener != null){
                            mListener.onTopBoundary(this, null, 0);
                        }
                    }
                    if (!canScrollVertically(1) && diffY < 0) {
                        Log.d(ExpanderPresenterV8.TAG, "ExpanderLayout onDownBoundary.");
                        if (mListener != null){
                            mListener.onDownBoundary(this, null, 0);
                        }
                    }
                }
                break;
            default:
            case MotionEvent.ACTION_CANCEL:
                break;
        }

        return super.dispatchTouchEvent(event);
    }

    @Override
    public View focusSearch(View focused, int direction) {
        View result = getLayoutManager().onInterceptFocusSearch(focused, direction);
        if (result != null) {
            return result;
        }
        final FocusFinderV8 ff = FocusFinderV8.getInstance();
        result = ff.findNextFocus(this, focused, direction);
        if (result == null && getAdapter() != null && getLayoutManager() != null && !isComputingLayout()
                && !isLayoutFrozen()) {
            eatRequestLayout();
            result = getLayoutManager().onFocusSearchFailed(focused, direction, mRecycler, mState);
            resumeRequestLayout(false);
        }
        if (result != null && result.getWidth() != 0) {
            return result;
        }
        if (mLayoutManager.getOrientation() != OrientationHelper.HORIZONTAL) {
            if (focused != null && focused.getWidth() != 0) {
                dropKeyOneTime();
                return focused;
            }
        }

        if (result == null) {
            result = super.focusSearch(focused, direction);
        }
        return result;
    }


    @Override
    public void setFocus(boolean focus) {
    }

    @Override
    public void onClick() {

    }

    @Override
    public void refreshUI() {
    }

    @Override
    public IBlockFocusLightView makeBlockFocusLightView() {
        return null;
    }

    @Override
    public boolean obtainFocus() {
        return false;
    }

    @Override
    public View makeFocusView() {
        return null;
    }

    @Override
    public View makeContentView() {
        return null;
    }

    @Override
    public int getBorderWidth() {
        return 0;
    }

    @Override
    public int getBorderRadius() {
        return 0;
    }

    @Override
    public boolean isSquare() {
        return false;
    }

    @Override
    public void setSize(int w, int h) {

    }

    @Override
    public void setBlockData(ContainerV8 o) {

    }


    public List<ViewHolder> getHolders(String name) {
        try {
            Field recyclerField = RecyclerView.class.getDeclaredField("mRecycler");
            recyclerField.setAccessible(true);
            Recycler recycler = (Recycler) recyclerField.get(this);
            Field field = Recycler.class.getDeclaredField(name);
            field.setAccessible(true);
            return (List<ViewHolder>) field.get(recycler);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ArrayList<ViewHolder>();
    }

    @Override
    public String getPosterUrl() {
        return null;
    }

    private OnBoundaryListenerV8 mListener;
    public void setOnBoundaryListener(OnBoundaryListenerV8 listener) {
        this.mListener = listener;
    }
}
