package android.support.v7.widget;

import android.content.Context;
import android.graphics.Color;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.coocaa.uisdk.R;
import com.coocaa.uisdk.listener.IBlockFocusLightView;
import com.coocaa.uisdk.listener.IView;
import com.coocaa.uisdk.model.ContainerV8;
import com.coocaa.uisdk.model.PanelTitleInfo;
import com.coocaa.uisdk.utils.UtilV8;

public class ExtensionExpandLayout extends FrameLayout implements IView {
    private int mColor;
    private TextView mTitleTextView;
    private static final int DEFAULT_TITLE_TEXT_SIZE = 48;
    private int mTitleH;
    private LayoutParams mTitleParams, mContentParams;
    protected ExpanderLayoutV8 mExpanderLayoutV8;
    public static final int SPACE = UtilV8.Div(40);

    public ExtensionExpandLayout(@NonNull Context context) {
        super(context);
        setClipChildren(false);
        setClipToPadding(false);
        mTitleTextView = new TextView(context);
        mTitleTextView.setIncludeFontPadding(false);
        mTitleTextView.setFocusable(false);
        mTitleTextView.setFocusableInTouchMode(false);
        mTitleTextView.setTextSize(UtilV8.Dpi(DEFAULT_TITLE_TEXT_SIZE));
        mTitleTextView.setVisibility(GONE);
        mTitleTextView.setSingleLine(true);
        mTitleTextView.setPadding(UtilV8.Div(80),0, UtilV8.Div(80),0);
        mColor = getResources().getColor(R.color.default_panel_title);
        mTitleTextView.setTextColor(mColor);
        mTitleParams = new LayoutParams(LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        addView(mTitleTextView, mTitleParams);

        mExpanderLayoutV8 = new ExpanderLayoutV8(context);
        mContentParams = new LayoutParams(LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        addView(mExpanderLayoutV8, mContentParams);
    }

    public void setTitle(PanelTitleInfo titleInfo) {
        if (titleInfo == null) return;
        int size = titleInfo.size;
        if (size != DEFAULT_TITLE_TEXT_SIZE) {
            mTitleTextView.setTextSize(UtilV8.Dpi(size));
        }
        try {
            int color = Color.parseColor(titleInfo.title_color);
            if (mColor != color) {
                mTitleTextView.setTextColor(color);
                mColor = color;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        String title = titleInfo.text;
        if (!TextUtils.isEmpty(title)) {
            mTitleTextView.setVisibility(VISIBLE);
            mTitleTextView.setText(title);
        }
        updateLayoutParams();
    }

    private void updateLayoutParams() {
        if (mTitleTextView.getVisibility() == GONE) {
            mContentParams.topMargin = 0;
            mExpanderLayoutV8.setLayoutParams(mContentParams);
        } else {
            mTitleTextView.measure(0, 0);
            mTitleH = mTitleTextView.getMeasuredHeight();
            if (mContentParams.topMargin != mTitleH + SPACE) {
                mContentParams.topMargin = mTitleH + SPACE;
                mExpanderLayoutV8.setLayoutParams(mContentParams);
            }
        }

    }


    public TextView getTitleTextView() {
        return mTitleTextView;
    }

    public ExpanderLayoutV8 getExpanderLayout() {
        return mExpanderLayoutV8;
    }

    @Override
    public String getPosterUrl() {
        return null;
    }

    @Override
    public void setFocus(boolean focus) {

    }

    @Override
    public void onClick() {

    }

    @Override
    public boolean obtainFocus() {
        return false;
    }

    @Override
    public View makeFocusView() {
        return null;
    }

    @Override
    public View makeContentView() {
        return null;
    }

    @Override
    public int getBorderWidth() {
        return 0;
    }

    @Override
    public int getBorderRadius() {
        return 0;
    }

    @Override
    public boolean isSquare() {
        return false;
    }

    @Override
    public void setSize(int w, int h) {

    }

    @Override
    public void setBlockData(ContainerV8 o) {

    }

    @Override
    public void refreshUI() {

    }

    @Override
    public IBlockFocusLightView makeBlockFocusLightView() {
        return null;
    }
}
