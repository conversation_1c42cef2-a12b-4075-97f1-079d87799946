package com.coocaa.uisdk.view;

import android.content.Context;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.view.animation.Interpolator;
import android.view.animation.LayoutAnimationController;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.coocaa.uisdk.R;
import com.coocaa.uisdk.model.PageDirection;
import com.coocaa.uisdk.utils.UtilV8;


public class BasePanelLayout extends FrameLayout {
    private int contentTopMargin = UtilV8.Div(20);//30-10可能要剪掉文字间隙
    private int contentTopOffset = 0;
    protected FrameLayout mContentLayout;
    protected TextView mTitleView;
    protected LayoutParams content_p;
    protected FrameLayout mTitleLayout;


    private static final int DEFAULT_TITLE_TEXT_SIZE = 48;
    private int mColor;
    static final String TAG = "BasePanelLayout";
    private int mTitle_h;
    private LayoutAnimationController upAnimation; //mRightAnimation,mLeftAnimation,

    public BasePanelLayout(Context context) {
        super(context);
        setClipChildren(false);
        setClipToPadding(false);
        setDescendantFocusability(FOCUS_AFTER_DESCENDANTS);
        mTitleView = new TextView(context);
        mTitleLayout = new FrameLayout(context);
        mColor = getResources().getColor(R.color.default_panel_title);
        mTitleView.setTextColor(mColor);
        mTitleView.setTextSize(UtilV8.Dpi(DEFAULT_TITLE_TEXT_SIZE));
        mTitleView.setIncludeFontPadding(false);
        mTitleView.getPaint().setFakeBoldText(true);
        mTitleView.setGravity(Gravity.BOTTOM | Gravity.START);
        mTitleView.setSingleLine(true);
        LayoutParams title_p = new LayoutParams(LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        this.addView(mTitleLayout, title_p);
        mTitleLayout.addView(mTitleView, new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT));
        mTitleLayout.setClipToPadding(false);
        mTitleLayout.setClipChildren(false);
        mTitleLayout.setVisibility(View.GONE);
        mTitleView.measure(0, 0);
        mTitle_h = mTitleView.getMeasuredHeight();
        contentTopOffset = getTopOffset();
        mContentLayout = new FrameLayout(context);
        mContentLayout.setClipChildren(false);
        mContentLayout.setClipToPadding(false);
        content_p = new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
        content_p.topMargin = contentTopMargin + contentTopOffset + mTitle_h;
        this.addView(mContentLayout, content_p);

//        mRightAnimation = AnimationUtils.loadLayoutAnimation(context, R.anim.layout_right_anim);
//        mRightAnimation.setInterpolator( new Interpolator() {
//            @Override
//            public float getInterpolation(float input) {
//                return input * input * (3 - 2*input);
//            }
//        });
//        mLeftAnimation = AnimationUtils.loadLayoutAnimation(context, R.anim.layout_left_anim);
//        mLeftAnimation.setInterpolator( new Interpolator() {
//            @Override
//            public float getInterpolation(float input) {
//                return input * input * (3 - 2*input);
//            }
//        });

        upAnimation = AnimationUtils.loadLayoutAnimation(context, R.anim.layout_up_anim);
        upAnimation.setInterpolator( new Interpolator() {
            @Override
            public float getInterpolation(float input) {
                return input * input * (3 - 2*input);
            }
        });
    }


    public void doScheduleLayoutAnimation(@PageDirection int direction) {
        if(upAnimation.getAnimation() != null)
            upAnimation.getAnimation().cancel();
        if(direction != PageDirection.NONE) {
            mContentLayout.setLayoutAnimation(upAnimation);
            mContentLayout.postInvalidateOnAnimation();
        }
    }

    protected int getTopOffset() {
        return 0;
    }

    public BasePanelLayout setTitleColor(int color) {
        if (color != mColor) {
            mColor = color;
        }
        mTitleView.setTextColor(color);
        return this;
    }

    public BasePanelLayout setTitleSize(int size) {
        mTitleView.setTextSize(size);
        mTitleView.measure(0, 0);
        mTitle_h = mTitleView.getMeasuredHeight();

        return this;
    }

    @Deprecated
    public BasePanelLayout setTitleHeight(int height) {
        ViewGroup.LayoutParams params = mTitleLayout.getLayoutParams();
        params.height = height;
        mTitleLayout.setLayoutParams(params);
        return this;
    }

    public BasePanelLayout setTitle(String title) {
        if (title != null) {
            mTitleView.setText(title);
            mTitleView.setVisibility(VISIBLE);
        }
        updateTitleVisible();
        return this;
    }

    /**
     * 需要实现 {@link #isTitleVisible() 方法}
     */
    public void updateTitleVisible() {
        int visible = isTitleVisible() ? View.VISIBLE : View.GONE;
        if (mTitleLayout != null) {
            if (visible == View.GONE) {
                content_p.topMargin = contentTopOffset;
                mContentLayout.setLayoutParams(content_p);
            } else {
                if (content_p.topMargin != contentTopMargin + contentTopOffset + mTitle_h) {
                    content_p.topMargin = contentTopMargin + contentTopOffset + mTitle_h;
                    mContentLayout.setLayoutParams(content_p);
                }
            }
            mTitleLayout.setVisibility(visible);
        }
    }

    public void destroy() {
        mContentLayout.removeAllViews();
    }


    protected boolean isTitleVisible() {
        return true;
    }

    public FrameLayout getPanelContentLayout() {
        return mContentLayout;
    }

}
