package com.coocaa.uisdk.view.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorFilter;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.Animatable;
import android.graphics.drawable.Drawable;
import android.os.SystemClock;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.coocaa.uisdk.utils.UtilV8;

import java.util.Arrays;

/**
 * 8.0 通用焦点框
 * @Author: yuzhan
 */
public class BlockFocusDrawableV8 extends Drawable implements Animatable, Runnable {

    private Path borderPath;
    private Paint borderPaint;
    private float[] borderRadius;
    private RectF borderRect;
    private boolean borderVisible = true;

    private boolean asCircle;

    //for anim.
    private final static int ANIM_STEP = 15;
    private final static int ANIM_FROM_ALPHA = 255;
    private final static int ANIM_TO_ALPHA = (int) (255 * 0.2f);
    private final static int ANIM_DURATION = 82;
    private int curAlpha = 255;
    private boolean reverse = false;
    private boolean stay = false;
    private boolean isAnimRunning = false;
    
    public final static float DEFAULT_STROKE_WIDTH = UtilV8.Div(3);
    public final static int CORNER_RADIUS_SIZE = UtilV8.Div(8);
    public final static int SOLID_BORDER_RADIUS_OFFSET = UtilV8.Div(2);//边框和内容弧度偏差


    public BlockFocusDrawableV8(Context context) {
        this(context, false);
    }

    public BlockFocusDrawableV8(Context context, boolean asCircle) {
        this.asCircle = asCircle;
        initBorderPaint();
        borderRect = new RectF();
    }

    /**
     * 设置边框颜色
     * @param color
     * @return
     */
    public BlockFocusDrawableV8 setBorderColor(int color) {
        borderPaint.setColor(color);
        return this;
    }

    /**
     * 设置边框粗细
     * @param width
     * @return
     */
    public BlockFocusDrawableV8 setBorderWidth(float width) {
        borderPaint.setStrokeWidth(width);
        return this;
    }

    /**
     * 设置填充圆角弧度
     */
    public BlockFocusDrawableV8 setRadius(float radius) {
        if(!asCircle) {
            initRadius();
            Arrays.fill(borderRadius, radius + SOLID_BORDER_RADIUS_OFFSET);
        }
        return this;
    }

    /**
     * 精细化设置四个角的弧度，
     * @param radii 弧度数组，需要8个值，如左上角和右下角弧度20，可以传入new float[]{20f, 20f, 0, 0, 20f, 20f, 0, 0}
     * @return
     */
    public BlockFocusDrawableV8 setRadius(float[] radii) {
        if(!asCircle && radii != null && radii.length == CORNER_RADIUS_SIZE) {
            initRadius();
            for(int i=0; i<CORNER_RADIUS_SIZE; i++) {
                borderRadius[i] = radii[i] == 0 ? 0 : radii[i] + SOLID_BORDER_RADIUS_OFFSET;
            }
        }
        return this;
    }

    public void refresh() {
        invalidateSelf();
    }

    @Override
    public void draw(@NonNull Canvas canvas) {
        if(borderVisible) {
            borderPaint.setAlpha(curAlpha);
            canvas.drawPath(borderPath, borderPaint);
        }
    }

    @Override
    public void setAlpha(int i) {
        borderPaint.setAlpha(i);
    }

    @Override
    public void setColorFilter(@Nullable ColorFilter colorFilter) {
        borderPaint.setColorFilter(colorFilter);
    }

    @Override
    public int getOpacity() {
        return PixelFormat.TRANSLUCENT;
    }

    @Override
    public boolean setVisible(boolean visible, boolean restart) {
        if(!visible) {
            if(isAnimRunning) {
                stop();
            }
        }
        return super.setVisible(visible, restart);
    }

    @Override
    public void unscheduleSelf(@NonNull Runnable what) {
        super.unscheduleSelf(what);
    }

    @Override
    protected void onBoundsChange(Rect bounds) {
        super.onBoundsChange(bounds);
        borderRect.set(borderPaint.getStrokeWidth()/2, borderPaint.getStrokeWidth()/2,
                bounds.right - borderPaint.getStrokeWidth()/2, bounds.bottom - borderPaint.getStrokeWidth()/2);

        borderPath.reset();
        if(asCircle) {
            borderPath.addOval(borderRect, Path.Direction.CW);
        } else {
            if(borderRadius != null) {
                borderPath.addRoundRect(borderRect, borderRadius, Path.Direction.CW);
            } else {
                borderPath.addRect(borderRect, Path.Direction.CW);
            }
        }
    }

    private void initBorderPaint() {
        borderPaint = new Paint();
        borderPaint.setColor(Color.WHITE);
        borderPaint.setStrokeWidth(DEFAULT_STROKE_WIDTH);
        borderPaint.setStyle(Paint.Style.STROKE);
        borderPaint.setAntiAlias(true);
        borderPaint.setFilterBitmap(true);

        borderPath = new Path();
    }

    private void initRadius() {
        if(borderRadius == null) {
            borderRadius = new float[8];
        }
    }

    @Override
    public void start() {
        delayStart(250);
    }

    public void delayStart(long delay) {
        resetAnimValues();
        isAnimRunning = true;
        reverse = true;
        unscheduleSelf(this);
        nextAlpha(250);
    }

    @Override
    public void stop() {
        resetAnimValues();
        unscheduleSelf(this);
        invalidateSelf();
    }

    private void nextAlpha(long delay) {
        if(isAnimRunning) {
            invalidateSelf();
            unscheduleSelf(this);

            if(reverse) {
                curAlpha += ANIM_STEP;
            } else {
                curAlpha -= ANIM_STEP;
            }
            if(curAlpha < ANIM_TO_ALPHA) {
                curAlpha = ANIM_TO_ALPHA;
                reverse = true;
                stay = true;
            }
            if(curAlpha > ANIM_FROM_ALPHA) {
                curAlpha = ANIM_FROM_ALPHA;
                reverse = false;
                stay = true;
            }
            if(stay) {
                stay = false;
                scheduleSelf(this, SystemClock.uptimeMillis() + delay + (reverse ? 300 : 800));//停留
            } else {
                scheduleSelf(this, SystemClock.uptimeMillis() + delay);
            }
        }
    }

    @Override
    public boolean isRunning() {
        return isAnimRunning;
    }

    private void resetAnimValues() {
        isAnimRunning = false;
        reverse = false;
        stay = false;
        curAlpha = ANIM_FROM_ALPHA;
    }

    @Override
    public void run() {
        if(isAnimRunning) {
            nextAlpha(ANIM_DURATION);
        } else {
            invalidateSelf();
        }
    }
}
