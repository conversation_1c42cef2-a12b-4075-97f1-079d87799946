package com.coocaa.uisdk.view.widget;

import android.content.Context;
import android.view.View;

/**
 * 8.0UI焦点框View
 * @Author: yuzhan
 */
public class BlockFocusViewV8 extends View {

    protected BlockFocusDrawableV8 focusDrawable;

    public BlockFocusViewV8(Context context) {
        this(context, false);
    }

    public BlockFocusViewV8(Context context, boolean asCircle) {
        super(context);
        focusDrawable = new BlockFocusDrawableV8(context, asCircle);
        setBackground(focusDrawable);
        setLayerType(LAYER_TYPE_HARDWARE, null);
    }

    /**
     * 设置边框颜色
     * @param color
     * @return
     */
    public BlockFocusViewV8 setBorderColor(int color) {
        focusDrawable.setBorderColor(color);
        return this;
    }

    /**
     * 设置边框粗细
     * @param width
     * @return
     */
    public BlockFocusViewV8 setBorderWidth(float width) {
        focusDrawable.setBorderWidth(width);
        return this;
    }

    /**
     * 设置填充圆角弧度
     */
    public BlockFocusViewV8 setRadius(float radius) {
        focusDrawable.setRadius(radius);
        return this;
    }

    /**
     * 精细化设置四个角的弧度，
     * @param radii 弧度数组，需要8个值，如左上角和右下角弧度20，可以传入new float[]{20f, 20f, 0, 0, 20f, 20f, 0, 0}
     * @return
     */
    public BlockFocusViewV8 setRadius(float[] radii) {
        focusDrawable.setRadius(radii);
        return this;
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
    }


    @Override
    protected void onDetachedFromWindow() {
        stopAnim();
        super.onDetachedFromWindow();
    }

    public void startAnim() {
        focusDrawable.start();
    }

    public void setAnimDelayed(long delay) {
        focusDrawable.delayStart(delay);
    }

    public void stopAnim() {
        focusDrawable.stop();
    }

    public boolean isAnimRunning() {
        return focusDrawable.isRunning();
    }
}
