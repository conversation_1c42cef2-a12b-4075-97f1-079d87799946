package com.coocaa.uisdk.model;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import android.text.TextUtils;

import java.io.Serializable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by lu on 16-3-2.
 */
public class OnClickDataV8 implements Serializable {
    public static final String ATTR_NAME = "onclick";

    public static final String DOWHAT_START_ACTIVITY = "startActivity";
    public static final String DOWHAT_START_SERVICE = "startService";
    public static final String DOWHAT_SEND_BROADCAST = "sendBroadcast";
    public static final String DOWHAT_SEND_INTERNALBROADCAST = "sendInternalBroadcast";

    public static final String BYWHAT_ACTION = "action";
    public static final String BYWHAT_CLASS = "class";
    public static final String BYWHAT_URI = "uri";

    public String packagename;
    public int versioncode;
    public String dowhat;
    public String bywhat;
    public String byvalue;
    public String data;
    public Map<String, String> params;
    public ExceptionV8<OnClickDataV8> exception;

    public OnClickDataV8 setPackagename(String packagename) {
        this.packagename = packagename;
        return this;
    }

    public OnClickDataV8 setParams(Map<String, String> params) {
        this.params = params;
        return this;
    }

    public OnClickDataV8 setVersioncode(int versioncode) {
        this.versioncode = versioncode;
        return this;
    }

    public OnClickDataV8 setDowhat(String dowhat) {
        this.dowhat = dowhat;
        return this;
    }

    public OnClickDataV8 setByvalue(String byvalue) {
        this.byvalue = byvalue;
        return this;
    }

    public OnClickDataV8 setBywhat(String bywhat) {
        this.bywhat = bywhat;
        return this;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public static OnClickDataV8 create(String packageName) {
        OnClickDataV8 data = new OnClickDataV8();
        data.setPackagename(packageName);
        return data;
    }

    public String getDowhat() {
        return dowhat;
    }

    public String getBywhat() {
        return bywhat;
    }

    public String getByvalue() {
        return byvalue;
    }

    public Map<String, String> getParams() {
        return params;
    }


    public int getVersioncode() {
        return versioncode;
    }

    public String getPackagename() {
        return packagename;
    }

    public OnClickDataV8 build() {

        return this;
    }


    public Intent buildIntent(Context c) {
        Intent intent = null;
        if (bywhat != null && !bywhat.equals("") && !bywhat.equals("null")) {
            intent = new Intent();
            if (!TextUtils.isEmpty(packagename)) {
                intent.setPackage(packagename);
            }
            if (bywhat.equals(BYWHAT_ACTION) && !TextUtils.isEmpty(byvalue)) {
                intent.setAction(byvalue);
                if (!TextUtils.isEmpty(data))
                    intent.setData(Uri.parse(data));
            } else if (bywhat.equals(BYWHAT_CLASS) && !TextUtils.isEmpty(byvalue)) {
                intent.setClassName(packagename, byvalue);
                if (!TextUtils.isEmpty(data))
                    intent.setData(Uri.parse(data));
            } else if (bywhat.equals(BYWHAT_URI) && !TextUtils.isEmpty(byvalue)) {
                intent.setData(Uri.parse(byvalue));
            } else{
                byvalue = getLauncherActivity(c, packagename);
                if (!TextUtils.isEmpty(byvalue))
                    intent.setClassName(packagename, byvalue);
            }
        }else if (!TextUtils.isEmpty(packagename)){
            intent = new Intent();
            intent.setPackage(packagename);
            byvalue = getLauncherActivity(c, packagename);
            if (!TextUtils.isEmpty(byvalue))
                intent.setClassName(packagename, byvalue);
        }
        if (intent != null && params != null && params.size() > 0) {
            Iterator iterator = params.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry entry = (Map.Entry) iterator.next();
                intent.putExtra((String) entry.getKey(), (String) entry.getValue());
            }
        }
        return intent;
    }

    private static String getLauncherActivity(Context context, String packageName) {
        PackageManager pm = context.getPackageManager();
        Intent intent = new Intent(Intent.ACTION_MAIN, null);
        intent.addCategory(Intent.CATEGORY_LAUNCHER);
        intent.setPackage(packageName);
        List<ResolveInfo> resolveInfo = pm.queryIntentActivities(intent, PackageManager.GET_DISABLED_COMPONENTS);
        if (resolveInfo != null && resolveInfo.size() > 0) {
            ResolveInfo info = resolveInfo.get(0);
            return info.activityInfo.name;
        }
        return "";
    }
    public static class ExceptionV8<T> implements Serializable{
        public String name;
        public T value;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public T getValue() {
            return value;
        }

        public void setValue(T value) {
            this.value = value;
        }
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("OnClickData={ packagename=");
        sb.append(packagename);
        sb.append(", bywhat=");
        sb.append(bywhat);
        sb.append(", byvalue=");
        sb.append(byvalue);
        sb.append(", dowhat=");
        sb.append(dowhat);
        sb.append(", versioncode=");
        sb.append(versioncode);
        sb.append(", params=");
        sb.append(params);
        sb.append(" } ");
        return sb.toString();
    }
}
