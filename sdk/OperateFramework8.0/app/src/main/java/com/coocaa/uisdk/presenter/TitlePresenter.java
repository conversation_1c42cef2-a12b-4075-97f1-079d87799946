package com.coocaa.uisdk.presenter;


import android.content.Context;

import com.coocaa.uisdk.model.ContainerV8;
import com.coocaa.uisdk.view.TitleLayout;

public class TitlePresenter extends PresenterV8 {

    private TitleLayout mTitleLayout;
    public TitlePresenter(Context context) {
        super(context);
        mTitleLayout = makeView(context);
    }

    @Override
    public void onResume() {

    }

    @Override
    public void onPause() {

    }

    @Override
    public void onStop() {

    }

    @Override
    public void onDestroy() {

    }

    @Override
    public TitleLayout makeView(Context context) {
        return new TitleLayout(context);
    }

    @Override
    public TitleLayout getView() {
        return mTitleLayout;
    }

    @Override
    public void setContainer(ContainerV8 container) {
        container.focusable = 0;
        super.setContainer(container);
    }



}
