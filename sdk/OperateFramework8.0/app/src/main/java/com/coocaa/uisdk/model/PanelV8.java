package com.coocaa.uisdk.model;

import java.io.Serializable;


public class PanelV8 implements Serializable {
    public String panel_version = "";
    public PanelTitleInfo panel_title ;
    public String panel_name = ""; //板块名称
    public String focus_shape;//0-线落焦，1-面落焦
    public String panel_id;
    public String rec_stream_id;


    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("PanelV8{");
        sb.append("panel_version='").append(panel_version);
        sb.append(", panel_title=").append(panel_title);
        sb.append(", panel_name='").append(panel_name);
        sb.append(", focus_shape='").append(focus_shape);
        sb.append(", panel_id='").append(panel_id);
        sb.append('}');
        return sb.toString();
    }
}
