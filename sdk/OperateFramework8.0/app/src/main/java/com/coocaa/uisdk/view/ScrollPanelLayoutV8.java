package com.coocaa.uisdk.view;

import android.content.Context;
import android.widget.FrameLayout;


public class ScrollPanelLayoutV8 extends PanelLayoutV8
{

    private  HorizontalScrollView mHorizontalScrollView;

    public ScrollPanelLayoutV8(Context context) {
        super(context);
        if (mContentLayout !=null && mContentLayout.getParent() !=null){
            removeView(mContentLayout);
        }
        mHorizontalScrollView = new HorizontalScrollView(context);
        mHorizontalScrollView.setOverScrollMode(OVER_SCROLL_NEVER);
        mHorizontalScrollView.setScrollInMiddle(true);
        mHorizontalScrollView.setClipChildren(false);
        mHorizontalScrollView.setClipToPadding(false);
        this.addView(mHorizontalScrollView, new FrameLayout.LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT));
        mHorizontalScrollView.removeAllViews();
        mHorizontalScrollView.addView(mContentLayout, content_p);
    }

    public void addOnScrollChangeListener(HorizontalScrollView.OnScrollChangedListener listener) {
        if (mHorizontalScrollView != null){
            mHorizontalScrollView.setOnScrollChangedListener(listener);
        }
    }

}
