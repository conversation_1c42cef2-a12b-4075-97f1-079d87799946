package com.coocaa.uisdk.listener;

import android.content.Context;
import android.graphics.ColorFilter;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.widget.ImageView;

import com.coocaa.uisdk.utils.CircleParamsV8;

public interface IImageLoaderV8 {
    /**
     * 初始化
     * @param context
     */
    void init(Context context);

    /**
     * 开启图片加载打印信息
     * @param debugMode
     */
    void setDebugMode(boolean debugMode);

    /**
     * 消毁
     */
    void destroy();

    /**
     * 获取view
     * @param context
     * @return
     */
    View getView(Context context);

    /**
     * 获取view
     * @param context
     * @param original   使用fresco源码，默认是使用精简版的fresco view，设置original为true使用完整版fresco view
     * @return
     */
    View getView(Context context, boolean original);

    /**
     * 传入上下文context,在需要加载图片时必须最先调用
     * @param context 上下文
     * @return
     */
    IImageLoaderV8 with(Context context);

    /**
     * 设置占位图
     * @param id 占位图id
     * @return
     */
    IImageLoaderV8 setPlaceHolder(int id);

    /**
     * 设置占位图
     * @param drawable
     * @return
     */
    IImageLoaderV8 setPlaceHolder(Drawable drawable);



    /**
     * 设置需要加载图片的url地址
     * @param url
     * @return
     */
    IImageLoaderV8 load(String url);


    IImageLoaderV8 circle(CircleParamsV8 params);

    /**
     * 需要图片的大小
     * @param width
     * @param height
     * @return
     */
    IImageLoaderV8 resize(int width, int height);


    IImageLoaderV8 showAnimation(boolean show);
    IImageLoaderV8 showFade(boolean fade);
    IImageLoaderV8 showFade(boolean fade, int duration);

    /**
     * 设置需要显示图片的view，在加载图片时必须最后调用
     * @param view
     * @return
     */
    void into(View view);

    /**
     * 设置需要显示的scaleType
     * @param scaleType
     * @return
     */
    IImageLoaderV8 setScaleType(ImageView.ScaleType scaleType);


    IImageLoaderV8 wrapContent();

    IImageLoaderV8 finalCallback(OnFinalCallback callback);

    void getBitmap(OnBitmapLoadListenerV8 listener);

    void startAnimation(View view);

    void stopAnimation(View view);

    IImageLoaderV8 setLeftTopCorner(float corner);

    IImageLoaderV8 setLeftBottomCorner(float corner);

    IImageLoaderV8 setRightTopCorner(float corner);

    IImageLoaderV8 setRightBottomCorner(float corner);

    IImageLoaderV8 setColorFilter(ColorFilter cf);

    void clearCacheFromMemory(String url);

    void reset(View view);

    void clearCache(String url);

    void updateThreadPriority(int priopity);

    IImageLoaderV8 setAnimLoopCount(int count);

    Context getContext();
}
