package com.coocaa.uisdk.expander;

import android.support.v7.widget.RecyclerView;
import android.view.View;

import com.coocaa.uisdk.listener.IPresenterV8;
import com.coocaa.uisdk.listener.IView;


public class NewRecyclerViewHolder extends RecyclerView.ViewHolder{
    private IPresenterV8 mPresenter = null;

    public NewRecyclerViewHolder(View itemView) {
        super(itemView);
    }

    public void setPresenter(IPresenterV8 presenter){
        this.mPresenter = presenter;
    }

    public void refreshUI(){
        if(itemView instanceof IView)
            ((IView)itemView).refreshUI();
    }
    public IPresenterV8 getPresenter(){
        return mPresenter;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("NewRecyclerViewHolder{");
        sb.append("itemView=").append(itemView);
        sb.append('}');
        return sb.toString();
    }
}
