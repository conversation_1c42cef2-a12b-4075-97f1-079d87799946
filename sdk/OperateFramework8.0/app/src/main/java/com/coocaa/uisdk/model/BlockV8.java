package com.coocaa.uisdk.model;


import android.text.TextUtils;

import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;
import java.util.List;

public class BlockV8 implements Serializable {
    public BlockContent block_content;
    public BlockTitle block_title;
    public BlockTypeInfo block_type_info;

    /**
     * 推荐流强插的索引
     */
    public int  block_seq ;

    public static class BlockTypeInfo implements Serializable {
        public int lucency_flag;
    }

    public static class BlockContent implements Serializable {

        public String title;
        /**
         * 点击事件
         */
        public String action;

        public OnClickDataV8 parsedAction;
        /**
         *
         */
        public String params;
        /**
         * 海报图
         */
        public BlockImages imgs;

        public void parseAction() {
            if (TextUtils.isEmpty(action)) {
                parsedAction = new OnClickDataV8();
            } else {
                try {
                    parsedAction = JSONObject.parseObject(action, OnClickDataV8.class);
                } catch (Exception e) {
                    parsedAction = new OnClickDataV8();
                    e.printStackTrace();
                }
            }
        }

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("BlockContent{");
            sb.append("title='").append(title).append('\'');
            sb.append(", action='").append(action).append('\'');
            sb.append(", parsedAction=").append(parsedAction);
            sb.append(", params='").append(params).append('\'');
            sb.append(", imgs=").append(imgs);
            sb.append('}');
            return sb.toString();
        }
    }


    public static class BlockTitle implements Serializable {
        public BlockTitleInfo sub_title;
        public BlockTitleInfo title;

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("BlockTitle{");
            sb.append("sub_title=").append(sub_title);
            sb.append(", title=").append(title);
            sb.append('}');
            return sb.toString();
        }
    }

    public static class BlockTitleInfo implements Serializable {
        public int show;
        public String text;

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("BlockTitleInfo{");
            sb.append("show=").append(show);
            sb.append(", text='").append(text).append('\'');
            sb.append('}');
            return sb.toString();
        }
    }

    public static class BlockImages implements Serializable {
        public BlockPoster poster = null;
        public String focus_img_url = null;

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("BlockImages{");
            sb.append("poster=").append(poster);
            sb.append(", focus_img_url='").append(focus_img_url).append('\'');
            sb.append('}');
            return sb.toString();
        }
    }

    public static class BlockPoster implements Serializable {
        public List<String> images = null;

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("BlockPoster{");
            sb.append("images=").append(images);
            sb.append('}');
            return sb.toString();
        }
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("BlockV8{");
        sb.append("block_content=").append(block_content);
        sb.append(", block_title=").append(block_title);
        sb.append(", block_type_info=").append(block_type_info);
        sb.append('}');
        return sb.toString();
    }
}
