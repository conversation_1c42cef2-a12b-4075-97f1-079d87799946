package com.coocaa.uisdk.view;

import android.content.Context;
import android.graphics.Color;
import android.support.v7.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.util.SparseArray;
import android.view.View;

import com.coocaa.uisdk.listener.IBlockFocusLightView;
import com.coocaa.uisdk.listener.IView;
import com.coocaa.uisdk.model.ContainerV8;
import com.coocaa.uisdk.model.PageDirection;
import com.coocaa.uisdk.model.PanelV8;
import com.coocaa.uisdk.utils.BlockCacheUtil;
import com.coocaa.uisdk.utils.SupportUtilV8;
import com.coocaa.uisdk.utils.UtilV8;

import java.util.ArrayList;
import java.util.List;


public class PanelLayoutV8 extends BasePanelLayout implements IView
{
    private RecyclerView.Adapter mAdapter = null;
    private  SparseArray<List<RecyclerView.ViewHolder>> mUsedType = new SparseArray<List<RecyclerView
            .ViewHolder>>();
    private boolean isLayoutTitle = false;
    private PanelV8 mPanel;

    private static final int DEFAULT_TITLE_TEXT_SIZE = 48;

    public PanelLayoutV8(Context context)
    {
        super(context);
    }

    public void setTitleValue(PanelV8 panel)
    {
        mPanel = panel;
        if (panel !=null){
            Log.e("CCLPanel","panel : " +panel.toString());
        }
        if (!isLayoutTitle)
            updateTitleVisible();
        if (panel!=null && panel.panel_title != null && !TextUtils.isEmpty(panel.panel_title.text))
        {
            if (panel.panel_title.size != 0 && panel.panel_title.size != DEFAULT_TITLE_TEXT_SIZE){
                setTitleSize(UtilV8.Dpi(panel.panel_title.size));
            }
            if (mTitleLayout.getVisibility() != View.VISIBLE)
                mTitleLayout.setVisibility(View.VISIBLE);
            setTitle(panel.panel_title.text);
        }
        if (mTitleLayout.getVisibility() == View.VISIBLE)
        {
            if(panel!=null &&panel.panel_title!= null && !TextUtils.isEmpty(panel.panel_title.title_color)){
                setTitleColor(Color.parseColor(panel.panel_title.title_color));
            }
        }

    }

    public void setAdapter(RecyclerView.Adapter adapter)
    {
        this.mAdapter = adapter;
        mAdapter.registerAdapterDataObserver(mObserver);
    }

    public RecyclerView.Adapter getAdapter()
    {
        return mAdapter;
    }

    RecyclerView.AdapterDataObserver mObserver = new RecyclerView.AdapterDataObserver()
    {
        @Override
        public void onChanged()
        {
            mContentLayout.removeAllViews();
            int count = mAdapter.getItemCount();
            for (int i = 0; i < count; i++)
            {
                int type = mAdapter.getItemViewType(i);
                RecyclerView.ViewHolder holder = null;
                if (type == SupportUtilV8.getSupportViewType(SupportUtilV8.BLOCK)){
                    if (mUsedType.get(type) == null)
                        mUsedType.put(type, new ArrayList<RecyclerView.ViewHolder>());
                    if (BlockCacheUtil.size(getContext(),type) == 0)
                    {
                        holder = mAdapter.onCreateViewHolder(PanelLayoutV8.this, type);
                    } else
                    {
                        holder = BlockCacheUtil.get(getContext(),type);
                    }
                    mUsedType.get(type).add(holder);
                }else{
                    holder = mAdapter.onCreateViewHolder(PanelLayoutV8.this, type);
                }
                mAdapter.onBindViewHolder(holder, i);
                mContentLayout.addView(holder.itemView);
                mAdapter.onViewAttachedToWindow(holder);
            }
        }
    };



    int mPosition = -1;
    int mModel = 0;
    public void setPosition(int position)
    {
        this.mPosition = position;
        if (!isLayoutTitle)
            updateTitleVisible();
    }

    @Override
    public void updateTitleVisible() {
        super.updateTitleVisible();
        isLayoutTitle = true;
    }

    public void setModel(int model){
        this.mModel = model;
    }

    public void destroy()
    {
        mContentLayout.removeAllViews();
        moveUsedToCache();
        isLayoutTitle = false;
//        isShow = false;
    }

    private void moveUsedToCache()
    {
        int count = mUsedType.size();
        for (int i = 0; i < count; i++)
        {
            List<RecyclerView.ViewHolder> holders = mUsedType.valueAt(i);
            for (RecyclerView.ViewHolder holder : holders)
            {
                mAdapter.onViewDetachedFromWindow(holder);
                mAdapter.onViewRecycled(holder);
                BlockCacheUtil.put(getContext(),mUsedType.keyAt(i),holder);
            }
        }
        mUsedType.clear();
    }



    @Override
    public void setFocus(boolean focus) {

    }

    @Override
    public void onClick() {

    }

    @Override
    public IBlockFocusLightView makeBlockFocusLightView() {
        return null;
    }

    @Override
    public View makeFocusView() {
        return null;
    }

    @Override
    public View makeContentView() {
        return null;
    }

    @Override
    public int getBorderWidth() {
        return 0;
    }

    @Override
    public int getBorderRadius() {
        return 0;
    }

    @Override
    public boolean isSquare() {
        return false;
    }

    @Override
    public void setSize(int w, int h) {

    }

    @Override
    public String getPosterUrl() {
        return null;
    }

    @Override
    public void setBlockData(ContainerV8 o) {

    }

//    private boolean isShow = false;
    @Override
    public void refreshUI()
    {
        int size = mUsedType.size();
        for (int i = 0; i < size; i++)
        {
            List<RecyclerView.ViewHolder> holders = mUsedType.valueAt(i);
            if (holders != null && holders.size() > 0)
            {
                for (RecyclerView.ViewHolder holder : holders)
                {
                    if (holder.itemView instanceof IView)
                        ((IView) holder.itemView).refreshUI();
                }
            }
        }
//        if (!isShow)
//        doScheduleLayoutAnimation();
//        isShow = true;
    }

    @Override
    public boolean obtainFocus() {
        return false;
    }

    @Override
    protected boolean isTitleVisible() {
//        if(mPosition == 0 )
//            return false;
        if (mPanel == null || mPanel.panel_title == null || TextUtils.isEmpty(mPanel.panel_title.text))
            return false;
        return true;
    }

    public int getPosition() {
        return mPosition;
    }

    public void showAnim(boolean b, @PageDirection int direction) {
        if (b)
            doScheduleLayoutAnimation(direction);
    }
}
