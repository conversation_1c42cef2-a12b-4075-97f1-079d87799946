package com.coocaa.uisdk.presenter;

import android.content.Context;
import android.content.Intent;
import android.support.v7.widget.RecyclerView;
import android.util.Log;
import android.view.View;

import com.coocaa.uisdk.expander.NewRecyclerAdapter;
import com.coocaa.uisdk.expander.NewRecyclerViewHolder;
import com.coocaa.uisdk.listener.IViewRecycled;
import com.coocaa.uisdk.model.ContainerV8;
import com.coocaa.uisdk.model.PageDirection;
import com.coocaa.uisdk.model.PanelV8;
import com.coocaa.uisdk.model.Plugin;
import com.coocaa.uisdk.utils.UtilV8;
import com.coocaa.uisdk.view.PanelLayoutV8;

import java.util.List;


public class PanelPresenterV8 extends PresenterV8 {
    protected PanelLayoutV8 mNewPanelLayout = null;
    protected NewRecyclerAdapter mAdapter = null;
    private View mFirstView = null;
    private final String TAG = "CCLPanleV8";
    private boolean needNotifyChildScrollState = false;

    public PanelPresenterV8(Context context) {
        super(context);
        mNewPanelLayout = makeView(context);
    }

    @Override
    public void onResume() {
        if (debug)
            Log.d(TAG, "onResume=" + ", panel=" + this.toString());
        if (mAdapter != null) {
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                if (holder.getPresenter() instanceof ExpanderPresenterV8) {
                    //出现横向滚动布局时，需要继续往内层传递，修复海报图不显示的问题，XHY-821
                    ((ExpanderPresenterV8) holder.getPresenter()).onResume(true);
                } else {
                    if (debug)
                        Log.d(TAG, "onResume, holder.getPresenter()=" + holder.getPresenter());
                    holder.getPresenter().onResume();
                }
            }
        }
        mNewPanelLayout.removeCallbacks(exposure);
        mNewPanelLayout.postDelayed(exposure,1500);
    }

    @Override
    public void onPause() {
        if (debug)
            Log.d(TAG, "onPause=" + ", panel=" + this.toString());
        if (mAdapter != null) {
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().onPause();
            }
        }
    }

    @Override
    public void onStop() {
        if (debug)
            Log.d(TAG, "onStop=" + ", panel=" + this.toString());
        if (mAdapter != null) {
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().onStop();
            }
        }
    }

    @Override
    public void onViewRecycled() {
        if (debug)
            Log.d(TAG, "onViewRecycled=" + ", panel=" + this.toString());
        mFirstView = null;
        if (mAdapter != null) {
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                if (holder.getPresenter() instanceof IViewRecycled) {
                    holder.getPresenter().onViewRecycled();
                } else {
                    holder.getPresenter().onDestroy();
                }
            }
            mAdapter.getViewHolders().clear();
        }
        ((PanelLayoutV8) getView()).destroy();
    }

    @Override
    public void onIdleShow() {
        super.onIdleShow();
        if ( mAdapter != null) {
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().onIdleShow();
            }
        }
    }

    @Override
    public void onAttachedToWindow() {
        if (debug)
            Log.d(TAG, "onAttachedToWindow=" + ", panel=" + this.toString());
        onResume();
        resetDefaultScrollState();
    }

    @Override
    public void onDetachedFromWindow() {
        if (debug)
            Log.d(TAG, "onDetachedFromWindow=" + ", panel=" + this.toString());
        setVisible(false);
        if (mAdapter != null) {
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().onDetachedFromWindow();
            }
        }
    }

    @Override
    public void onDestroy() {
        if (debug)
            Log.d(TAG, "onDestroy=" + ", panel=" + this.toString());
        mFirstView = null;
        if (mAdapter != null) {
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().onDestroy();
            }
            mAdapter.getViewHolders().clear();
        }
        ((PanelLayoutV8) getView()).destroy();
    }

    @Override
    public void onNewIntent(Intent intent) {
        if (mAdapter != null) {
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().onNewIntent(intent);
            }
        }
    }

    @Override
    public View getTopFirstView() {
//        if(debug)
//            Log.d(TAG, "getTopFirstView, mFirstView=" + mFirstView +  ", panel=" + this.toString());
        if (mFirstView != null)
            return mFirstView;
        if (mAdapter.getViewHolders().size() == 0)
            return null;
        int count = mAdapter.getViewHolders().size();
        for (int i = 0; i < count; i++) {
            View view = mAdapter.getViewHolders().get(i)
                    .getPresenter().getTopFirstView();
            if (view != null && view.isFocusable()) {
                mFirstView = view;
                break;
            }
        }
        return mFirstView;
    }

    @Override
    public boolean obtainFocus() {
        if (debug)
            Log.d(TAG, "obtainFocus" + ", panel=" + this.toString());
        final View view = getTopFirstView();
        if (view != null) {
            return view.requestFocus();
        }
        return false;
    }

    @Override
    public void resetDefaultScrollState() {
        if (debug)
            Log.d(TAG, "resetDefaultScrollState" + ", panel=" + this.toString());
        if (mAdapter.getViewHolders().size() > 0) {
            if (hasRecyclerView())
                mFirstView = null;
            for (NewRecyclerViewHolder holder : mAdapter.getViewHolders())
                holder.getPresenter().resetDefaultScrollState();
        }
    }


    private boolean hasRecyclerView() {
        int size = mAdapter.getViewHolders().size();
        for (int i = 0; i < size; i++) {
            if (mAdapter.getViewHolders().get(i).itemView instanceof RecyclerView)
                return true;
        }
        return false;
    }

    @Override
    public PanelLayoutV8 makeView(Context context) {
        if (mNewPanelLayout == null)
            mNewPanelLayout = new PanelLayoutV8(context);
        return mNewPanelLayout;
    }

    @Override
    public View getView() {
        return mNewPanelLayout;
    }


    @Override
    public void onLayoutHide(boolean isPause) {
        if (debug)
            Log.d(TAG, "onLayoutHide, isPause=" + isPause + ", panel=" + this.toString());
        super.onLayoutHide(isPause);
        mNewPanelLayout.showAnim(false,direction);
        if (mAdapter != null) {
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().onLayoutHide(isPause);
            }
        }
    }

    @Override
    public void setVisible(boolean visible) {
        getView().removeCallbacks(exposure);
        if (visible)
            getView().postDelayed(exposure, 1500);
    }

    @Override
    public void onLayoutShow() {
        if (debug)
            Log.d(TAG, "onLayoutShow, panel=" + this.toString());
        super.onLayoutShow();
        mNewPanelLayout.showAnim(true,direction);
        direction = PageDirection.NONE;//使用后清除
        if (mAdapter != null) {
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().onLayoutShow();
            }
        }

        mNewPanelLayout.removeCallbacks(exposure);
        mNewPanelLayout.postDelayed(exposure,1500);
    }

    @Override
    public void setContainer(ContainerV8 t) {
        super.setContainer(t);
        if (((PanelLayoutV8) getView()).getAdapter() == null) {
            mAdapter = new NewRecyclerAdapter();
            mAdapter.setOnItemClickListener(this);
            mAdapter.setOnBoundaryListener(this);
            ((PanelLayoutV8) getView()).setAdapter(mAdapter);
        }
        mFirstView = null;
        ((NewRecyclerAdapter) ((PanelLayoutV8) getView()).getAdapter()).refreshUI(t.contents);
        setPanelTitle(t);
        needNotifyChildScrollState = false;
        List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
        for (NewRecyclerViewHolder holder : holders) {
            if (holder.getPresenter().needNotifyScrollState()) {
                Log.d(TAG, "need NotifyScrollState : " + holder.getPresenter() + ", panel=" + this.toString());
                needNotifyChildScrollState = true;
            }
        }
    }

    public Runnable exposure = new Runnable() {
        @Override
        public void run() {
            exposure(mContainer);
        }
    };

    private void exposure(ContainerV8 container) {
        if (mOnPanelExposureListener != null)
        {
            mOnPanelExposureListener.panelExposureListener(container);
        }
    }


    private void setPanelTitle(ContainerV8 t) {
        Object obj = t.contentObject;
        if (obj instanceof PanelV8) {
            ((PanelLayoutV8) getView()).setTitleValue((PanelV8) obj);
        } else if (obj instanceof Plugin) {
            try {
                ((PanelLayoutV8) getView()).setTitleValue((PanelV8) t.parseExtraPluginInfo(PanelV8.class));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    @Override
    public boolean onLeftBoundary(View leaveView, ContainerV8 container, int position) {
        if (mBoundaryListener != null && (container.x == 0 || mAdapter.getItemCount() == 1))
            return mBoundaryListener.onLeftBoundary(leaveView, mContainer, mPosition);
        return false;
    }

    @Override
    public boolean onTopBoundary(View leaveView, ContainerV8 container, int position) {
        if (debug)
            Log.d(TAG, "onTopBoundary, leaveView=" + leaveView + ", panel=" + this.toString());
        if (mBoundaryListener != null) {
            if (container.y == 0 || (mPosition < 2 && (position == 0 || !isPreChildsFocus(position))))
            return mBoundaryListener.onTopBoundary(leaveView, mContainer, mPosition);
        }
        return false;
    }

    private boolean isPreChildsFocus(int position) {
        for (int i = 0; i < position; i++) {
            if (isChildFocusable(i))
                return true;
        }
        return false;
    }

    private boolean isChildFocusable(int index) {
        if (mAdapter != null) {
            final List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            if (holders != null && holders.size() > index) {
                View v = holders.get(index).itemView;
                if (v != null)
                    return v.isFocusable();
            }
        }
        return false;
    }

    int[] pos = new int[2];
    int[] parentPos = new int[2];

    @Override
    public boolean onDownBoundary(View leaveView, ContainerV8 container, int position) {
        if (mBoundaryListener != null) {
            if (mAdapter.getItemCount() == 1)
                return mBoundaryListener.onDownBoundary(leaveView, mContainer, mPosition);
            leaveView.getLocationOnScreen(pos);
            ((PanelLayoutV8) getView()).getLocationOnScreen(parentPos);
            if (Math.abs((pos[1] + leaveView.getHeight()) - (parentPos[1] + ((PanelLayoutV8) getView()).getHeight())) < 80)
                return mBoundaryListener.onDownBoundary(leaveView, mContainer, mPosition);
        }
        return false;
    }


    @Override
    public boolean onRightBoundary(View leaveView, ContainerV8 container, int position) {
        if (mBoundaryListener != null) {
            if (mAdapter.getItemCount() == 1 /*|| (mAdapter.getItemCount() == 2 && !isPreChildsFocus(1))*/)
                return mBoundaryListener.onRightBoundary(leaveView, mContainer, mPosition);
            leaveView.getLocationOnScreen(pos);
            ((PanelLayoutV8) getView()).getLocationOnScreen(parentPos);
            if (Math.abs((pos[0] + leaveView.getWidth()) - (parentPos[0] + ((PanelLayoutV8) getView()).getWidth())) < UtilV8.Div(80))
                return mBoundaryListener.onRightBoundary(leaveView, mContainer, mPosition);
        }
        return false;
    }


    @Override
    public void setPosition(int position) {
        if (((PanelLayoutV8) getView()) != null)
            ((PanelLayoutV8) getView()).setPosition(position);
        super.setPosition(position);
        if (debug)
            Log.d(TAG, "setPosition : " + position + ", panel=" + this.toString());
    }


    @Override
    public void setScrollState(int state) {
        if (mAdapter != null && needNotifyChildScrollState) {
//            Log.d(TAG, "setScrollState : " + state + ", panel=" + this.toString());
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().setScrollState(state);
            }
        }
    }

    @Override
    public void onScrolled(int dx, int dy) {
        if (dx == 0 && dy == 0)
            return;
        if (mAdapter != null && needNotifyChildScrollState) {
//            Log.d(TAG, "onScrolled : dx=" + dx + ", dy=" + dy + ", panel=" + this.toString());
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().onScrolled(dx, dy);
            }
        }
    }

    @Override
    public boolean onBackPressed() {
        Log.e("uisdk", "Panel onBackPressed ");
        return mBoundaryListener != null && mBoundaryListener.onBackPressed();
    }
}
