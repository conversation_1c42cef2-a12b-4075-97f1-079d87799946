package com.coocaa.uisdk.view;

import android.content.Context;
import android.support.annotation.NonNull;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.coocaa.uisdk.R;
import com.coocaa.uisdk.listener.IBlockFocusLightView;
import com.coocaa.uisdk.listener.ICardView;
import com.coocaa.uisdk.listener.OnBoundaryListenerV8;
import com.coocaa.uisdk.listener.OnItemClickListenerV8;
import com.coocaa.uisdk.model.ContainerV8;
import com.coocaa.uisdk.utils.ImageLoaderV8;
import com.coocaa.uisdk.utils.SupportUtilV8;
import com.coocaa.uisdk.utils.UtilV8;
import com.coocaa.uisdk.view.widget.BlockFocusDrawableV8;


/**
 *  建议使用BlockLayoutV8
 */
@Deprecated
public class CardLayout extends FrameLayout implements ICardView {

    protected View mFocusView, mPosterView;
    protected LayoutParams mFocusParams, mPosterParams, parentParams;
    public int border, borderRadius;
    static final String TAG = "BaseCard";
    protected OnBoundaryListenerV8 mOnBoundaryListener;
    protected OnItemClickListenerV8 mOnItemClickListener;
    protected int position;
    protected ContainerV8 mContainer;
    protected Context mContext;
    protected IBlockFocusLightView focusLightView;
    protected BlockFocusDrawableV8 focusDrawable;

    public CardLayout(@NonNull Context context) {
        super(context);
        mContext = context;

        mFocusView = makeFocusView();
        focusDrawable = new BlockFocusDrawableV8(context);
        boolean isSquare = isSquare();
        focusDrawable.setRadius(isSquare ? 3 : UtilV8.Div(18)).setBorderColor(SupportUtilV8.blockFocusColor);
        if (mFocusView == null) {
            mFocusView = new View(context);
            mFocusView.setBackground(focusDrawable);
        }
        if (getBorderWidth() < 0)
            border = getResources().getDimensionPixelSize(R.dimen.common_border_width);
        else
            border = getBorderWidth();
        if (getBorderRadius() < 0)
            borderRadius = getResources().getDimensionPixelSize(R.dimen.common_border_round_radius);
        else
            borderRadius = getBorderRadius();
        mPosterView = makeContentView();
        if (mPosterView == null)
        {
            mPosterView = new View(context);
            mPosterView.setBackgroundResource(R.drawable.block_default_bg);
        }
        mFocusParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        mPosterParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        parentParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);

        mFocusView.setVisibility(GONE);
        addView(mFocusView, mFocusParams);
        addView(mPosterView, mPosterParams);
        if(SupportUtilV8.isSupportFocusLight) //高端机支持落焦闪光
        {
            focusLightView = makeBlockFocusLightView();
            if (focusLightView == null){
                focusLightView = new SimpleFocusLightView(getContext());
            }
        }

    }


    @Override
    public String getPosterUrl() {
        return null;
    }

    public void onDestroy() {
    }

    public void setSize(int w, int h) {
        Log.d(TAG, "size w:" + w + " h" + h);
        mFocusParams.width = w + 2 * border;
        mFocusParams.height = h + 2 * border;
        mFocusParams.gravity = Gravity.CENTER;
//        mFocusParams.topMargin = -border;
//        mFocusParams.leftMargin = -border;
        mFocusView.setLayoutParams(mFocusParams);

        mPosterParams.width = w;
        mPosterParams.height = h;
        mPosterParams.gravity=Gravity.CENTER;
        mPosterView.setLayoutParams(mPosterParams);

    }

    @Override
    public void setPosition(int position) {
        this.position = position;
    }

    @Override
    public void setBlockData(ContainerV8 o) {
        this.mContainer = o;
        addFocusLight(o.width,o.height);
    }

    public void setClipState(boolean clip) {
        setClipToPadding(clip);
        setClipChildren(clip);
    }


    private void addFocusLight(int width, int height) {
        if(focusLightView != null){
            focusLightView.setSize(width,height);
            View mView = focusLightView.getView();
            if (mView != null && mView.getParent() != null)
            {
                removeView(mView);
            }
            addView(mView);
        }
    }

    public void disableListener() {
        setOnFocusChangeListener(null);
        setOnClickListener(null);
        setOnKeyListener(null);
    }

    @Override
    public void setFocus(boolean focus) {
        if (mFocusView != null) {
            mFocusView.setVisibility(focus ? VISIBLE : GONE);
        }
        startLightAnim(focus);
        UtilV8.focusAnimate(this, focus);
        if(focus) {
            focusDrawable.start();
        } else {
            focusDrawable.stop();
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        focusDrawable.stop();
    }

    protected void startLightAnim(boolean focus){
        if (focusLightView != null){
            focusLightView.startFocusLightAnim(focus);
        }
    }

    @Override
    public void onClick() {
    }

    @Override
    public void refreshUI() {

    }

    @Override
    public IBlockFocusLightView makeBlockFocusLightView() {
        return null;
    }

    @Override
    public View makeContentView() {
        return ImageLoaderV8.getLoader().getView(getContext());
    }

    @Override
    public View makeFocusView() {
        return null;
    }

    @Override
    public int getBorderWidth() {
        return -1;
    }

    @Override
    public int getBorderRadius() {
        return UtilV8.Div(16);
    }

    @Override
    public boolean isSquare() {
        return !SupportUtilV8.isSupportRounded;
    }


    @Override
    public void setOnBoundaryListener(OnBoundaryListenerV8 listener) {
        this.mOnBoundaryListener = listener;
    }

    @Override
    public void setOnItemClickListener(OnItemClickListenerV8 listener) {
        mOnItemClickListener = listener;
    }

    @Override
    public boolean obtainFocus() {
        if (isFocusable())
            return this.requestFocus();
        int count = getChildCount();
        View focus = null;
        for (int i = 0; i < count; i++) {
            View v = getChildAt(i);
            if (v != null && v.isFocusable()) {
                focus = v;
                break;
            }
        }
        return focus != null && focus.requestFocus();
    }
}
