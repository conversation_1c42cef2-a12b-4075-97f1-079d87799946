package com.coocaa.uisdk.model;

import java.io.Serializable;

public class ExpanderV8 implements Serializable {
    public int orientation;
    public int space = 40;
    public ExpanderTitle title;



    public static class ExpanderTitle implements Serializable {
        public String text;
        public int size;

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("ExpanderTitle{");
            sb.append("text='").append(text);
            sb.append(", size=").append(size);
            sb.append('}');
            return sb.toString();
        }
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ExpanderV8{");
        sb.append("orientation=").append(orientation);
        sb.append(", space=").append(space);
        sb.append(", title=").append(title);
        sb.append('}');
        return sb.toString();
    }
}
