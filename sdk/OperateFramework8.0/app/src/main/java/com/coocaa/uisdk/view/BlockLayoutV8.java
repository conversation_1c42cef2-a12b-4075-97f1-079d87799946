package com.coocaa.uisdk.view;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.coocaa.uisdk.R;
import com.coocaa.uisdk.listener.IBlockFocusLightView;
import com.coocaa.uisdk.listener.IView;
import com.coocaa.uisdk.listener.OnFinalCallback;
import com.coocaa.uisdk.model.BlockV8;
import com.coocaa.uisdk.model.ContainerV8;
import com.coocaa.uisdk.utils.BlockPosterHelperV8;
import com.coocaa.uisdk.utils.HighPerformanceBlockPosterHelperV8;
import com.coocaa.uisdk.utils.ImageLoaderV8;
import com.coocaa.uisdk.utils.SupportUtilV8;
import com.coocaa.uisdk.utils.UtilV8;
import com.coocaa.uisdk.view.widget.BlockFocusDrawableV8;


public class BlockLayoutV8 extends FrameLayout implements IView {

    protected IBlockFocusLightView focusLightView;
    protected View mFocusView, mPosterView;
    protected LayoutParams mFocusParams, mPosterParams, parentParams;
    public int border, gap, borderRadius;
    protected BlockPosterHelperV8 mPosterHelper;
    protected String mPosterUrl;
    private BlockV8 mBlock;
    public static Handler mMainThread = new Handler(Looper.getMainLooper());
    private boolean isResume = false;
    protected Context mContext;
    private boolean lastResume;
    protected BlockFocusDrawableV8 focusDrawable;

    //        private TextView mTest;
    public BlockLayoutV8(@NonNull Context context) {
        super(context);
        setClipChildren(true);

        mContext = context;
        mFocusView = makeFocusView();
        focusDrawable = new BlockFocusDrawableV8(context);
        boolean isSquare = isSquare();
        focusDrawable.setRadius(isSquare ?  3: UtilV8.Div(18)).setBorderColor(SupportUtilV8.blockFocusColor);
        if (mFocusView == null) {
            mFocusView = new View(context);
            mFocusView.setBackground(focusDrawable);
        }
        border = getBorderWidth();
        if (border < 0)
            border = UtilV8.Div(3);//默认边框宽度是3
        gap = getFocusGap();
        if(gap < 0) {
            gap = UtilV8.Div(2);//默认间隙是2
        }

        borderRadius = getBorderRadius();
        if (borderRadius < 0)
            borderRadius = getResources().getDimensionPixelSize(R.dimen.common_border_round_radius);

        mPosterView = makeContentView();
        if (mPosterView == null) {
            mPosterView = new View(context);
        }
        if (needPosterBg())
            mPosterView.setBackgroundResource(R.drawable.block_default_bg);
        mFocusParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        mPosterParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        parentParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);

        mFocusView.setVisibility(GONE);
        addView(mPosterView, mPosterParams);
        addView(mFocusView, mFocusParams);
//        mPosterHelper = SupportUtilV8.isHighPerformance ?
//                new HighPerformanceBlockPosterHelperV8(context,mPosterView):new BlockPosterHelperV8(context, mPosterView);
        mPosterHelper =new HighPerformanceBlockPosterHelperV8(context,mPosterView);

        if(isSquare) {
            mPosterHelper.setPosterCircleParams(null);//不支持圆角
        }


        if(SupportUtilV8.isSupportFocusLight) //高端机支持落焦闪光
        {
            focusLightView = makeBlockFocusLightView();
            if (focusLightView == null){
                focusLightView = new SimpleFocusLightView(getContext());
            }
        }

//        mTest = new TextView(context);
//        mTest.setTextColor(Color.WHITE);
//        mTest.setTextSize(UtilV8.Dpi(30));
//        mTest.setGravity(Gravity.CENTER);
//        mTest.setBackgroundColor(Color.BLUE);
//        addView(mTest,new FrameLayout.LayoutParams(200,200));
    }


    public void setSize(int w, int h) {
        mFocusParams.width = w + 2*(border + gap);
        mFocusParams.height = h + 2*(border + gap);
        mFocusParams.gravity = Gravity.CENTER;
//        mFocusParams.topMargin = -border;
//        mFocusParams.leftMargin = -border;
        mFocusView.setLayoutParams(mFocusParams);

        mPosterParams.width = w;
        mPosterParams.height = h;
        mPosterParams.gravity=Gravity.CENTER;
        mPosterView.setLayoutParams(mPosterParams);
        mPosterHelper.setPosterLayoutParams(mPosterParams);
    }

//    String id = "";
    @Override
    public void setBlockData(ContainerV8 container) {
        if (container.contentObject instanceof BlockV8) {
            mBlock = (BlockV8) container.contentObject;
            if (mBlock.block_content != null && mBlock.block_content.imgs != null && mBlock.block_content.imgs.poster != null) {
                mPosterUrl = mPosterHelper.getPosterImg(mBlock.block_content.imgs.poster);
                mPosterHelper.setPosterUrl(mPosterUrl);
                if(!TextUtils.isEmpty(mPosterUrl) && SupportUtilV8.isSupportWebp && isWebp(mPosterUrl))
                {
                    focusDrawable.setRadius(3);
                }else{
                    boolean isSquare = isSquare();
                    focusDrawable.setRadius(isSquare ?  3: UtilV8.Div(18)).setBorderColor(SupportUtilV8.blockFocusColor);
                }
            }
        }
//        id = container.id;
//        mTest.setText(container.id + " ");
        addFocusLight(container.width,container.height);
    }

    protected boolean isWebp(String posterUrl) {
        return posterUrl.endsWith("webp") || posterUrl.endsWith("gif") ;
    }

    protected void addFocusLight(int width, int height) {
        if(focusLightView != null){
            focusLightView.setSize(width,height);
            View mView = focusLightView.getView();
            if (mView != null && mView.getParent() != null)
            {
                removeView(mView);
            }
            addView(mView);
        }
    }


    public void disableListener() {
        setOnFocusChangeListener(null);
        setOnClickListener(null);
        setOnKeyListener(null);
    }

    @Override
    public String getPosterUrl() {
        return mPosterUrl;
    }

    @Override
    public void setFocus(boolean focus) {
        if (mFocusView != null) {
            mFocusView.setVisibility(focus ? VISIBLE : GONE);
        }
        startLightAnim(focus);
        UtilV8.focusAnimate(this, focus);
        if(focus && focusDrawable != null && mFocusView != null && focusDrawable == mFocusView.getBackground() && needFocusAnim() && SupportUtilV8.isSupportFocusAnim) {
            focusDrawable.start();
        } else {
            if(focusDrawable != null) {
                focusDrawable.stop();
            }
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if(focusDrawable != null) {
            focusDrawable.stop();
        }
        if(mPosterHelper != null) {
            mPosterHelper.stopAnim();
        }
    }

    protected void startLightAnim(boolean focus){
        if (focusLightView != null){
            focusLightView.startFocusLightAnim(focus);
        }
    }
    @Override
    public void onClick() {

    }

    @Override
    public void refreshUI() {
        if (lastResume )
            return;
        isResume = true;
        lastResume = true;
        try {
            if(mBlock != null && mBlock.block_content != null && mBlock.block_content.imgs != null) {
                mPosterUrl = mPosterHelper.getPosterImg(mBlock.block_content.imgs.poster);
            }
            mPosterHelper.setPosterUrl(mPosterUrl);
            loadPostOneImageView();
        }catch (Exception e){
            e.printStackTrace();
        }
    }


    @Override
    public boolean obtainFocus() {
        return false;
    }

    @Override
    public View makeContentView() {
        return ImageLoaderV8.getLoader().getView(getContext());
    }

    @Override
    public IBlockFocusLightView makeBlockFocusLightView() {
        return null;
    }

    @Override
    public View makeFocusView() {
        return null;
    }

    @Override
    public int getBorderWidth() {
        return -1;
    }

    public int getFocusGap() {
        return -1;
    }

    @Override
    public int getBorderRadius() {
        return UtilV8.Div(16);
    }

    @Override
    public boolean isSquare() {
        return !SupportUtilV8.isSupportRounded;
    }

    public void onDestroy() {
    }

    public boolean needPosterBg() {
        return true;
    }

    public void resumePostImage(boolean resume) {
       /* Log.e("kkk","resumePostImage :: " +resume);
        if (isResume == resume) return;
        isResume = resume;
        if (!resume) {
            mMainThread.removeCallbacks(delayResumePoster);
            mPosterHelper.stopAnim();
        }
        if (mPosterView != null) {
            if (resume) {
                mMainThread.postDelayed(delayResumePoster, 120);
            } else {
                mPosterView.setBackgroundResource(R.drawable.block_default_bg);
                clearImg();
            }
        }*/

        isResume = resume;
        if(!resume) {
            mMainThread.removeCallbacks(delayResumePoster);
            mPosterHelper.stopAnim();
        }
        if (mPosterView != null) {
            if (resume) {
                if (!lastResume) {
                    mMainThread.postDelayed(delayResumePoster, 120);
                }
            } else if (lastResume) {
                clearImg();
                mPosterView.setBackgroundResource(R.drawable.block_default_bg);
            }
        }
        lastResume = isResume;

    }

    private Runnable delayResumePoster = new Runnable() {
        @Override
        public void run() {
            loadPostOneImageView();
        }
    };

    protected void loadPostOneImageView() {
        mPosterHelper.loadPostOneImageView(new OnFinalCallback() {
            @Override
            public void onFinal(String id, int width, int height) {

                if (width != 0 && height != 0) {
                    if (isResume) {
                        mPosterHelper.startAnim();
                    } else {
                        mPosterHelper.clear();
                    }
                } else {
                    mPosterHelper.clear();
                }
            }

            @Override
            public void onFailed(String id, Throwable throwable) {
                mPosterHelper.clear();
            }
        });
    }

    private void clearImg() {
        mPosterHelper.clear();
    }

    protected boolean needFocusAnim() {
        return true;
    }
}
