package com.coocaa.uisdk.utils;

import android.content.Context;
import android.content.Intent;
import android.support.v4.content.LocalBroadcastManager;
import android.text.TextUtils;
import android.util.Log;

import com.coocaa.uisdk.model.OnClickDataV8;

import java.util.Map;

public class HomeClickUtilV8 {

    private final static String TAG = "HomeClick";

    public static boolean simpleStartIntent(Context c, OnClickDataV8 data)
    {
        if(TextUtils.isEmpty(data.getDowhat()))
            return false;
        Intent intent = data.buildIntent(c);
        if(intent == null)
            return false;
        if(!startIntent(c, data, intent, null)) { //exception
            if (data.exception != null && data.exception.getValue() != null) {
                return simpleStartIntent(c, data.exception.getValue());
            }
            if (!TextUtils.isEmpty(data.packagename)){
//                StartApi.startAppDetail(c, data.packagename);
                return true;
            }
        }
        return true;
    }


    public static boolean startIntent(Context c, OnClickDataV8 data, final Intent intent, Map<String, String> extraParams) {
        if (data.getVersioncode() <= 0){
            try {
                if (data.getDowhat().equals(OnClickDataV8.DOWHAT_START_ACTIVITY)) {

                    Log.e("mj","startActivity。。。"+intent);
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                    intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
                    c.startActivity(intent);
                    return true;
                } else if (data.getDowhat().equals(OnClickDataV8.DOWHAT_START_SERVICE)) {
                    c.startService(intent);
                    return true;
                } else if (data.getDowhat().equals(OnClickDataV8.DOWHAT_SEND_BROADCAST)) {
                    c.sendBroadcast(intent);
                    return true;
                } else if (data.getDowhat().equals(OnClickDataV8.DOWHAT_SEND_INTERNALBROADCAST)) {
                    LocalBroadcastManager.getInstance(c).sendBroadcast(intent);
                    return true;
                }
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }
        }else{
            //TODO
        }
        return true;
    }
}
