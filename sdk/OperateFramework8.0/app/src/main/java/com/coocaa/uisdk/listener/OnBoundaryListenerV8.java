package com.coocaa.uisdk.listener;

import android.view.View;

import com.coocaa.uisdk.model.ContainerV8;


public interface OnBoundaryListenerV8 {
    boolean onLeftBoundary(View leaveView, ContainerV8 c, int position);

    boolean onTopBoundary(View leaveView, ContainerV8 c, int position);

    boolean onDownBoundary(View leaveView, ContainerV8 c, int position);

    boolean onRightBoundary(View leaveView, ContainerV8 c, int position);

    boolean onBackPressed();
}
