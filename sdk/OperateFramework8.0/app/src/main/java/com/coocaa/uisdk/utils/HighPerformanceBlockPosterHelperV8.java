package com.coocaa.uisdk.utils;

import android.content.Context;
import android.view.View;

import com.coocaa.uisdk.listener.IImageLoaderV8;

import java.util.List;

public class HighPerformanceBlockPosterHelperV8 extends BlockPosterHelperV8{


    public HighPerformanceBlockPosterHelperV8(Context context, View view) {
        super(context, view);
    }

    @Override
    public void stopAnim() {
        if (ImageLoaderV8.getLoader() != null)
        ImageLoaderV8.getLoader().stopAnimation(mPostOneImageView);
        mMainThread.removeCallbacks(startAnimRunnable);
    }

    @Override
    public void startAnim() {
        mMainThread.postDelayed(startAnimRunnable, 500);
    }

    @Override
    public String getPosterImg(List<String> images) {
        if(images == null || images.size() == 0)
            return null;
        if(images.size() == 1)
            return images.get(0);
//        if(SystemUtil.isHighPerformance()) {//高端机优先使用动态图
//
//        }
        for(String s : images) {
            if(s.endsWith("webp")) {
                return s;
            }
        }
        return images.get(0);
    }


    //
    private Runnable startAnimRunnable = new Runnable() {
        @Override
        public void run() {
            IImageLoaderV8 mLoader = ImageLoaderV8.getLoader();
            if (mLoader != null)
            {
                mLoader.startAnimation(mPostOneImageView);
            }

        }
    };
}
