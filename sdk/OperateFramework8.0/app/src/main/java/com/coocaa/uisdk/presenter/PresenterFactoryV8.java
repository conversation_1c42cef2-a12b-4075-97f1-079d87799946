package com.coocaa.uisdk.presenter;

import android.content.Context;

import com.coocaa.uisdk.listener.IPresenterFactoryV8;
import com.coocaa.uisdk.listener.IPresenterV8;
import com.coocaa.uisdk.utils.CollectionUtil;
import com.coocaa.uisdk.utils.SupportUtilV8;

import java.util.List;


public class PresenterFactoryV8 implements IPresenterFactoryV8 {
    private List<IPresenterFactoryV8> mExtraFactory = CollectionUtil.arrayList();

    public static PresenterFactoryV8 getInstance(){
        return Holder.mInstance;
    }

    private static class Holder{
        public static final PresenterFactoryV8 mInstance = new PresenterFactoryV8();
    }

    public void addExtraFactory(IPresenterFactoryV8 factory){
        if (!mExtraFactory.contains(factory))
            mExtraFactory.add(factory);
    }

    /**
     * 根据type创建Presenter，目前支持的type见SupportUtil中定义
     * @param type
     * @param context
     * @return
     */
    @Override
    public IPresenterV8 createPresenter(String type, Context context) {
//        UtilV8.instence(context.getApplicationContext());
        IPresenterV8 presenter = null;
        if (mExtraFactory != null)
            presenter = createPresenterFromExtraFactory(type,context);
        if (presenter == null){
          switch (type)
          {
              case SupportUtilV8.EXPANDER:
                  presenter =  new ExpanderPresenterV8(context);
                  break;
              case SupportUtilV8.PANEL:
                  presenter =new PanelPresenterV8(context);
                  break;
              case SupportUtilV8.SCROLLPANEL:
                  presenter =new ScrollPanelPresenterV8(context);
                  break;
              case SupportUtilV8.REFERENCE:
                  presenter =new ReferencePresenterV8(context);
                  break;
              case SupportUtilV8.CARD:
                  presenter = new CardPresenter(context);
                  break;
              default:
              case SupportUtilV8.BLOCK:
                  presenter = new BlockPresenterV8(context);
                  break;
              case SupportUtilV8.TITLE:
                  presenter = new TitlePresenter(context);
                  break;
          }
        }
        return presenter;
    }

    private IPresenterV8 createPresenterFromExtraFactory(String type, Context context){
        if (mExtraFactory.size() > 0){
            for (IPresenterFactoryV8 iPresenterFactory : mExtraFactory) {
                IPresenterV8 presenter = iPresenterFactory.createPresenter(type,context);
                if (presenter != null)
                    return presenter;
            }
        }
        return null;
    }
}
