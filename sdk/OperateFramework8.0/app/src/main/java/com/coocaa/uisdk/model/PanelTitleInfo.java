package com.coocaa.uisdk.model;

import java.io.Serializable;

public class PanelTitleInfo implements Serializable {
    public String title_color = null;
    /**
     * 标题内容
     */
    public String text;

    /**
     * 标题大小
     */
    public int size = 0;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("PanelTitleInfo{");
        sb.append("title_color='").append(title_color);
        sb.append(", text='").append(text);
        sb.append(", size=").append(size);
        sb.append('}');
        return sb.toString();
    }
}