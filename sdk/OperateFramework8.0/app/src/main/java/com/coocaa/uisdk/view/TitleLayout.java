package com.coocaa.uisdk.view;

import android.content.Context;
import android.graphics.Color;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.coocaa.uisdk.R;
import com.coocaa.uisdk.listener.IBlockFocusLightView;
import com.coocaa.uisdk.listener.IView;
import com.coocaa.uisdk.model.ContainerV8;
import com.coocaa.uisdk.model.PanelTitleInfo;
import com.coocaa.uisdk.model.PanelV8;
import com.coocaa.uisdk.utils.UtilV8;

public class TitleLayout extends FrameLayout implements IView {

    private LayoutParams mTitleParams;
    private TextView mTitleTextView;
    private static final int DEFAULT_TITLE_TEXT_SIZE = 48;
    int mColor;

    public TitleLayout(@NonNull Context context) {
        super(context);
        mTitleTextView = new TextView(context);
        mTitleTextView.setIncludeFontPadding(false);
        mTitleTextView.setFocusable(false);
        mTitleTextView.setFocusableInTouchMode(false);
        mTitleTextView.setTextSize(UtilV8.Dpi(DEFAULT_TITLE_TEXT_SIZE));
        mTitleTextView.setVisibility(GONE);
        mTitleTextView.setSingleLine(true);
//        mTitleTextView.setPadding(0,0, UtilV8.Div(80),0);
        mColor = getResources().getColor(R.color.default_panel_title);
        mTitleTextView.setTextColor(mColor);
        mTitleTextView.getPaint().setFakeBoldText(true);
        mTitleParams = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        addView(mTitleTextView, mTitleParams);
    }

    @Override
    public void setFocus(boolean focus) {

    }

    @Override
    public void onClick() {

    }

    @Override
    public boolean obtainFocus() {
        return false;
    }

    @Override
    public View makeFocusView() {
        return null;
    }

    @Override
    public View makeContentView() {
        return null;
    }

    @Override
    public int getBorderWidth() {
        return 0;
    }

    @Override
    public int getBorderRadius() {
        return 0;
    }

    @Override
    public boolean isSquare() {
        return false;
    }

    @Override
    public String getPosterUrl() {
        return null;
    }

    @Override
    public void setSize(int w, int h) {

    }

    @Override
    public void setBlockData(ContainerV8 o) {
        Object obj = o.contentObject;
        if (obj instanceof PanelV8){
            setTitle(((PanelV8) obj).panel_title);
        }
    }

    public void setTitle(PanelTitleInfo titleInfo) {
        if (titleInfo == null) return;
        int size = titleInfo.size;
        if (size != DEFAULT_TITLE_TEXT_SIZE && size != 0) {
            mTitleTextView.setTextSize(UtilV8.Dpi(size));
        }
        try {
            if(!TextUtils.isEmpty(titleInfo.title_color)) {
                int color = Color.parseColor(titleInfo.title_color);
                if (mColor != color) {
                    mTitleTextView.setTextColor(color);
                    mColor = color;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        String title = titleInfo.text;
        if (!TextUtils.isEmpty(title)) {
            mTitleTextView.setVisibility(VISIBLE);
            mTitleTextView.setText(title);
        }
    }


    @Override
    public void refreshUI() {

    }

    @Override
    public IBlockFocusLightView makeBlockFocusLightView() {
        return null;
    }
}
