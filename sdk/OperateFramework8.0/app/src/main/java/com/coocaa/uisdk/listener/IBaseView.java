package com.coocaa.uisdk.listener;

import android.view.View;

import com.coocaa.uisdk.model.ContainerV8;

public interface IBaseView {

    View makeFocusView();

    View makeContentView();

    int getBorderWidth();

    int getBorderRadius();

    boolean isSquare();

    void setSize(int w, int h);

    void setBlockData(ContainerV8 o);

    void refreshUI();

    IBlockFocusLightView makeBlockFocusLightView();

    String getPosterUrl();
}
