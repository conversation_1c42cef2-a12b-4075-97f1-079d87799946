package com.coocaa.uisdk.utils;

import android.app.Application;
import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.ArrayMap;
import android.util.SparseArray;

import com.coocaa.uisdk.model.BlockV8;
import com.coocaa.uisdk.model.ContainerV8;
import com.coocaa.uisdk.model.ExpanderV8;
import com.coocaa.uisdk.model.PanelTitleInfo;
import com.coocaa.uisdk.model.PanelV8;
import com.coocaa.uisdk.model.Plugin;
import com.coocaa.uisdk.model.ReferenceV8;
import com.coocaa.uisdk.model.SupportConfigV8;

import java.util.Iterator;
import java.util.List;

/**
 * Created by luwei on 16-12-31.
 */

public class SupportUtilV8 {
    public static ArrayMap<String, SupportConfigV8> mSupportMap = new ArrayMap<String, SupportConfigV8>();
    private static SparseArray<String> mViewPresenterMap = new SparseArray<String>();

    private static int mId = 1;
    public static boolean isSupportRounded = true;
    public static boolean isSupportFocusLight = true;
    public static int blockFocusColor = Color.WHITE;
    public static boolean isHighPerformance ;
    public static boolean isSupportWebp = true;
    public static boolean isSupportFocusAnim = true;

    public static final String EXPANDER = "Expander";
    public static final String BLOCK = "Block";
    public static final String REFERENCE = "Reference";
    public static final String CARD = "Card";
    public static final String PANEL = "Panel";
    public static final String PLUGIN = "Plugin";
    public static final String SCROLLPANEL = "ScrollPanel";
    public static final String TITLE = "Title";


    static {
        addSupportType(EXPANDER, ExpanderV8.class);
        addSupportType(BLOCK, BlockV8.class);
        addSupportType(CARD, BlockV8.class);
        addSupportType(REFERENCE, ReferenceV8.class);
        addSupportType(PANEL, PanelV8.class);
        addSupportType(SCROLLPANEL, PanelV8.class);
        addSupportType(PLUGIN, Plugin.class);
        addSupportType(TITLE, PanelV8.class);
    }

    public static void setContext(Context context) {
      if (context instanceof Application){
          UtilV8.instence(context);
      }else {
          UtilV8.instence(context.getApplicationContext());
      }
    }

    public static void setIsSupportRounded(boolean support){
        isSupportRounded = support;
    }

    public static void setIsSupportFocusLight(boolean support) {
       isSupportFocusLight = support;
    }

    public static void setBlockFocusColor(int color) {
        blockFocusColor = color;
    }

    public static void setSupportWebp(boolean support){
        isSupportWebp = support;
    }

    public static void setSupportFocusAnim(boolean focusAnim) {
        isSupportFocusAnim = focusAnim;
    }

    public static void addSupportType(String type, Class clazz) {
        if (clazz == null)
            throw new RuntimeException("the class is null,please check out!");
        if (!mSupportMap.containsKey(type)) {
            mSupportMap.put(type, new SupportConfigV8(clazz, type, mId));
            mViewPresenterMap.put(mId, type);
            mId++;
        }
    }


    public static int getSupportViewType(String type) {
        if (!mSupportMap.containsKey(type)) {
            type = SupportUtilV8.BLOCK;
        }
        SupportConfigV8 config = mSupportMap.get(type);
        if (config != null)
            return config.viewType;
        return 0;
    }

    public static String getPresenterTypeByViewType(int viewType) {
        return mViewPresenterMap.get(viewType);
    }

    public static Class getSupportModel(String type) {
        SupportConfigV8 config = mSupportMap.get(type);
        if (config != null)
            return config.modelClazz;
        return null;
    }

    public static boolean isSupport(String type) {
        return mSupportMap.get(type) != null;
    }


    public static void parseContent(ContainerV8 container, int tag_id) {
        parseContent(null,container, tag_id);
    }

//    public static void parseContent(Container container, int tag_id, AppendData appendData) {
//        parseContent(null, container, tag_id, appendData, config, container.id);
//    }

//    public static void parseContent(ContainerV8 parentContainer, ContainerV8 container, int tag_id) {
//        parseContent(parentContainer, container, tag_id,   container.id);
//    }

    private static void parseContent(ContainerV8 parentContainer,ContainerV8 container, int tag_id) {
        if (container == null) {
            return;
        }

        container.parseContent();
        if (parentContainer != null)
            container.parentContainer = parentContainer;

        if (TextUtils.equals(container.type, SupportUtilV8.EXPANDER)) {
            judgeExpanderTitle(container);
        }
        if (container.contents != null && container.contents.size() > 0) {
            Iterator<ContainerV8> it = container.contents.iterator();
            int position = 0;
            while (it.hasNext()) {
                ContainerV8 temp = it.next();
                temp.position = position;
                temp.height = UtilV8.Div(temp.height);
                temp.width = UtilV8.Div(temp.width);
                temp.x = UtilV8.Div(temp.x);
                temp.y = UtilV8.Div(temp.y);
                if (SupportUtilV8.EXPANDER.equals(container.getConfirmType())) {
                    temp.x = 0;
                    temp.y = 0;
                }
                position ++;
                temp.parseContent();
                if (temp.canParse())
                    parseContent(container, temp,tag_id);
                else
                    it.remove();
            }
        }
    }

    private static void  judgeExpanderTitle(ContainerV8 container) {
        try {
            ExpanderV8 mExpander = (ExpanderV8) container.contentObject;
            if (mExpander == null)return;
            boolean hasTitle =mExpander.title !=null && !TextUtils.isEmpty(mExpander.title.text);
            if (hasTitle){
                ContainerV8 titleContainer = new ContainerV8();
                titleContainer.position = 0;
                titleContainer.type = SupportUtilV8.TITLE;
                titleContainer.parseContent();
                PanelV8 p = new PanelV8();
                p.panel_title = new PanelTitleInfo();
                p.panel_title.text = mExpander.title.text;
                if (mExpander.title.size !=0)
                    p.panel_title.size = mExpander.title.size;
                titleContainer.contentObject = p;
                List<ContainerV8> mContents = container.contents;

                if (mContents != null && mContents.size() > 0) {
                    container.contents.add(0, titleContainer);
                }


            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

}
