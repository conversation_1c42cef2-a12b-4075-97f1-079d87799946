package com.coocaa.uisdk.listener;


import android.support.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

import static com.coocaa.uisdk.listener.ResultCode.NO_NET;
import static com.coocaa.uisdk.listener.ResultCode.REQ_FAILED;
import static com.coocaa.uisdk.listener.ResultCode.UNKNOWN;

@IntDef({UNKNOWN, NO_NET, REQ_FAILED})
@Retention(RetentionPolicy.SOURCE)
public @interface ResultCode {
    int UNKNOWN = 0;
    int NO_NET = 1;
    int REQ_FAILED = 2;
}
