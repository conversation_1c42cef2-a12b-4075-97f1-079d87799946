package com.coocaa.uisdk.listener;

import android.content.Context;
import android.content.Intent;
import android.view.View;

import com.coocaa.uisdk.model.ContainerV8;


public interface IPresenterV8<T extends View> {

    /**
     * 在activity onResume时候调用
     */
    void onResume();


    /**
     * 在activity onPause 时候调用
     */
    void onPause();

    /**
     * 在activity onStop 时候调用
     */
    void onStop();

    void onDetachedFromWindow();

    void onAttachedToWindow();

    /**
     * 在activity onDestroy 时候调用
     */
    void onDestroy();

    void onViewRecycled();

    /**
     * 在activity onNewIntent时候调用
     *
     * @param intent
     */
    void onNewIntent(Intent intent);

    T makeView(Context context);

    /**
     * 在调用 setContainer 方法后可以通过此方法拿到生成的view，然后add到自己的Layout中
     */
    View getView();

    /**
     * 设置解析好的数据结构到框架中
     */
    void setContainer(ContainerV8 container);

    /**
     * view 点击事件的回调
     *
     * @param listener
     */
    void setOnItemClickListener(OnItemClickListenerV8 listener);

    /**
     * 整个模块边界的回调
     *
     * @param listener
     */
    void setOnBoundaryListener(OnBoundaryListenerV8 listener);


    /**
     * 板块曝光
     * @param listener
     */
     void setOnPanelExposureListener(OnPanelExposureListenerV8 listener);

    void setPosition(int position);

    int getPosition();

    /**
     * 获取第一个拿焦点的view,一般在addView后需要post一下调用此方法
     *
     * @return
     */
    View getTopFirstView();


    /**
     * 获取焦点
     *
     * @return 获取焦点成功or失败
     */
    boolean obtainFocus();

    /**
     * 显示当前PluginLayout，用于各个插件在恢复显示时的处理
     */
    void onLayoutShow();

    void onIdleShow();

    void setSize(int w, int h);

    /**
     * 当前PluginLayout移出屏幕外，用于各个插件在不显示时的处理
     *
     * @param isPause 说明是否为activity pause状态
     */
    void onLayoutHide(boolean isPause);


    View holdFocusedView();

    void restoreFocus();


    void setVisible(boolean visible);

    /**
     * 还原当前pluginlayou横向滚动状态为最初，具体由各个基类实现，一般在调此方法后需要把焦点重新设置到第一个view,这个过程需要post
     */
    void resetDefaultScrollState();

    boolean needNotifyScrollState();

    /**
     * 设置当前滚动状态参考
     * state 0为停止滚动，1是拖动，2是滚动
     */
    void setScrollState(int state);

    void onScrolled(int dx, int dy);

    void setDebug(boolean debug);


    /**
     * 页面切换方向
     * 在onLayoutShow 之前调用
     * @param i
     */
    void onPageChanedDirection(int i);

    void setSupportLayoutAnim(boolean support);
}
