package com.coocaa.uisdk.utils;

import android.animation.AnimatorSet;
import android.animation.Keyframe;
import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Color;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.View;
import android.view.animation.DecelerateInterpolator;
import android.widget.TextView;

public class UtilV8
{
    private static UtilV8 mUtil;

    public static final String TAG = "CCLUISDK";
    private static DisplayMetrics dm;

    private static float mDiv = 1.0f;
    private static float mDpi = 1.0f;

    private static float mBaseScreenWidth = 1920.0f;
    private static float mBaseScreenHeigh = 1080.0f;

    private static boolean needFocusAnim = true;//部分应用需要统一屏蔽逻辑放大，改APP里面代码太分散了，所以希望加个统一控制入口

    private UtilV8(){}
     static UtilV8 instence(Context context)
    {
        if (mUtil == null) {
            if(context.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE){
                //横屏模式
                mBaseScreenWidth = 1920.0f;
                mBaseScreenHeigh = 1080.0f;
            }else {
                //竖屏模式
                mBaseScreenWidth = 1080.0f;
                mBaseScreenHeigh = 1920.0f;
            }
            if(context instanceof Activity){
                context = context.getApplicationContext();
            }
            mUtil = new UtilV8();
            if (dm == null)
                dm = context.getResources().getDisplayMetrics();
            Log.v(TAG, "dm-->" + dm.toString());
            mDiv = (float) dm.widthPixels / mBaseScreenWidth;
            Log.v(TAG, "mDiv-->" + mDiv);
            mDpi = mDiv / dm.density;
            Log.v(TAG, "mDpi-->" + mDpi);
        }
        return mUtil;
    }

//     static UtilV8 instance(){
//        return mUtil;
//    }

    //横竖屏切换时，需要重新获取以及初始化
    public static void configurationChanged(Context context, Configuration newConfig) {
        dm = context.getResources().getDisplayMetrics();
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            //横屏模式
            mBaseScreenWidth = 1920.0f;
            mBaseScreenHeigh = 1080.0f;
        }else {
            //竖屏模式
            mBaseScreenWidth = 1080.0f;
            mBaseScreenHeigh = 1920.0f;
        }
        Log.v(TAG, "configurationChanged--dm-->" + dm.toString());
        mDiv = (float) dm.widthPixels / mBaseScreenWidth;
        Log.v(TAG, "configurationChanged--mDiv-->" + mDiv);
        mDpi = mDiv / dm.density;
        Log.v(TAG, "configurationChanged--mDpi-->" + mDpi);
    }

    public int getStatusBarHeight(Context context)
    {
        int result = 0;
        int resourceId = context.getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (resourceId > 0)
        {
            result = context.getResources().getDimensionPixelSize(resourceId);
        }
        return result;
    }

    public DisplayMetrics getDm()
    {
        return dm;
    }

    /**
     * 概述：得到屏幕的宽度<br/>
     *
     * @return int
     * @date 2013-10-22
     */
     static int getDisplayWidth()
    {
        if (dm == null) {
            return (int) mBaseScreenWidth;
        }
        return dm.widthPixels;
    }

    /**
     * 概述：得到屏幕的高度<br/>
     *
     * @return int
     * @date 2013-10-22
     */
     static int getDisplayHeight()
    {
        if(dm == null) {
            return (int) mBaseScreenHeigh;
        }
        return dm.heightPixels;
    }

    /**
     * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
     */
     static int dip2px(float dpValue)
    {
        final float scale = dm.density;
        return (int) (dpValue * scale + 0.5f);//最后的0.5f只是为了四舍五入
    }

    /**
     * 根据手机的分辨率从 px(像素) 的单
     * 位 转成为 dp
     */
     static int px2dip(float pxValue)
    {
        final float scale = dm.density;
        return (int) (pxValue / scale + 0.5f);//最后的0.5f只是为了四舍五入
    }

     public static int Div(int x) {
        return (int) (x * mDiv + 0.5f);
    }

     public static int Dpi(int x) {
        return (int) (x * mDpi + 0.5f);
    }

    /**
     * findViewById方法，取消强制转换步骤
     */
     <T extends View> T $(Context context, int id)
    {
        return (T) ((Activity) context).findViewById(id);
    }

    /**
     * findViewById方法，取消强制转换步骤
     */
     <T extends View> T $(View view, int id)
    {
        return (T) view.findViewById(id);
    }

    /**
     * 独立应用通用焦点放大动画
     * 以宽度 560 为界限，w<=560的放大110%，1000>w>560的放大105%, w>=1000的放大102%
     */
     static float getScaleRatio(int w){
//        return w <= Div(500) ? 1.1F : (float)((double)(w + Div(50)) * 1.0D / (double)w);
        float scale;
        if(w > Div(560)) {
            scale = 1.05F;
            if(w >= Div(1000)) {
                scale = 1.02F;
            }
        } else {
            scale = 1.1f;
        }
        return scale;
    }

     static void setFocusAnimSwitch(boolean flag) {
        needFocusAnim = flag;
    }

     public static void focusAnimate(View view, boolean hasFocus){
        if(!needFocusAnim) {
            return ;
        }
        forceFocusAnim(view, hasFocus);
    }

     static void forceFocusAnim(View view, boolean hasFocus){
        if(view != null){
            float scale;
            if(hasFocus){
                if(view.animate() != null)
                    view.animate().cancel();
                scale = getScaleRatio(Div(view.getWidth()));
                if (Build.VERSION.SDK_INT == Build.VERSION_CODES.JELLY_BEAN_MR1){
                    AnimatorSet set = new AnimatorSet();
                    ObjectAnimator a1 = ObjectAnimator.ofFloat(view,"scaleX",1,scale);
                    ObjectAnimator a2 = ObjectAnimator.ofFloat(view,"scaleY",1,scale);
                    set.setDuration(320);
                    set.setInterpolator(new DecelerateInterpolator());
                    set.playTogether(a1,a2);
                    set.start();
                    a1.addUpdateListener(new UiCompat.CompatAnimatorUpdateListener());
                }else{
                    view.animate().scaleX(scale).scaleY(scale).setDuration(320).setInterpolator(new DecelerateInterpolator());
                }
            }else {
                scale = 1.0F;
                if(view.animate() != null)
                    view.animate().cancel();
                view.animate().scaleX(scale).scaleY(scale).setDuration(280).setInterpolator(new DecelerateInterpolator());
            }
        }
    }

     public static ObjectAnimator nope_X(View view)
    {
        int delta = Div(6);
        Keyframe kf0 = Keyframe.ofFloat(0f, 0);
        Keyframe kf1 = Keyframe.ofFloat(.1f, -delta);
        Keyframe kf2 = Keyframe.ofFloat(.2f, 0);
        Keyframe kf3 = Keyframe.ofFloat(.3f, delta);
        Keyframe kf4 = Keyframe.ofFloat(.4f, 0);
        Keyframe kf5 = Keyframe.ofFloat(.5f, -delta);
        Keyframe kf6 = Keyframe.ofFloat(.6f, 0);
        Keyframe kf7 = Keyframe.ofFloat(.7f, delta);
        Keyframe kf8 = Keyframe.ofFloat(.8f, 0);
        Keyframe kf9 = Keyframe.ofFloat(.9f, -delta);
        Keyframe kf10 = Keyframe.ofFloat(1f, 0);

        PropertyValuesHolder pvhTranslateX = PropertyValuesHolder.ofKeyframe(View.TRANSLATION_X,
                kf0, kf1, kf2, kf3, kf4, kf5, kf6, kf7, kf8, kf9, kf10);

        ObjectAnimator animator = ObjectAnimator.ofPropertyValuesHolder(view, pvhTranslateX).setDuration(500);
        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.JELLY_BEAN_MR1)
            animator.addUpdateListener(new UiCompat.CompatAnimatorUpdateListener());
        return animator;
    }

    public static ObjectAnimator nope_Y(View view)
    {
        int delta = Div(6);

        Keyframe kf0 = Keyframe.ofFloat(0f, 0);
        Keyframe kf1 = Keyframe.ofFloat(.1f, -delta);
        Keyframe kf2 = Keyframe.ofFloat(.2f, 0);
        Keyframe kf3 = Keyframe.ofFloat(.3f, delta);
        Keyframe kf4 = Keyframe.ofFloat(.4f, 0);
        Keyframe kf5 = Keyframe.ofFloat(.5f, -delta);
        Keyframe kf6 = Keyframe.ofFloat(.6f, 0);
        Keyframe kf7 = Keyframe.ofFloat(.7f, delta);
        Keyframe kf8 = Keyframe.ofFloat(.8f, 0);
        Keyframe kf9 = Keyframe.ofFloat(.9f, -delta);
        Keyframe kf10 = Keyframe.ofFloat(1f, 0);

        PropertyValuesHolder pvhTranslateY = PropertyValuesHolder.ofKeyframe(View.TRANSLATION_Y,
                kf0, kf1, kf2, kf3, kf4, kf5, kf6, kf7, kf8, kf9, kf10);

        ObjectAnimator animator = ObjectAnimator.ofPropertyValuesHolder(view, pvhTranslateY).setDuration(500);
        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.JELLY_BEAN_MR1)
            animator.addUpdateListener(new UiCompat.CompatAnimatorUpdateListener());
        return animator;
    }

    /**
     * 设置文字透明度
     *
     * @param view
     * @param alpha
     * @param color
     * @return TextView
     * @date 2015-2-13
     */
     TextView settextAlpha(TextView view, int alpha, String color)
    {
        String r = color.substring(0, 2);
        String g = color.substring(2, 4);
        String b = color.substring(4, 6);
        view.setTextColor(Color.argb(alpha, Integer.valueOf(r, 16), Integer.valueOf(g, 16),
                Integer.valueOf(b, 16)));
        return view;
    }

     static float getDiv()
    {
        return mDiv;
    }

     static float getDpi()
    {
        return mDpi;
    }

     static boolean isNetConnected(Context context){
        ConnectivityManager connectivityManager = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo info = connectivityManager.getActiveNetworkInfo();
        if (info != null && info.isAvailable() && info.isConnected()) {
            return true;
        } else
            return false;
    }
}
