package com.coocaa.uisdk.presenter;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.support.v7.widget.ExpanderLayoutV8;
import android.support.v7.widget.OrientationHelper;
import android.support.v7.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.coocaa.uisdk.expander.NewRecyclerAdapter;
import com.coocaa.uisdk.expander.NewRecyclerViewHolder;
import com.coocaa.uisdk.expander.SpaceItemDecoration;
import com.coocaa.uisdk.expander.scroll.BaseLinearLayoutManagerV8;
import com.coocaa.uisdk.expander.scroll.HorizontalLayoutManagerV8;
import com.coocaa.uisdk.expander.scroll.VerticalLayoutManagerV8;
import com.coocaa.uisdk.listener.OnBoundaryListenerV8;
import com.coocaa.uisdk.listener.OnLoadMoreListener;
import com.coocaa.uisdk.listener.ResultCode;
import com.coocaa.uisdk.model.ContainerV8;
import com.coocaa.uisdk.model.ExpanderV8;
import com.coocaa.uisdk.utils.BlockCacheUtil;
import com.coocaa.uisdk.utils.CollectionUtil;
import com.coocaa.uisdk.utils.SupportUtilV8;
import com.coocaa.uisdk.utils.UiCompat;
import com.coocaa.uisdk.utils.UtilV8;

import java.util.List;


public class ExpanderPresenterV8 extends PresenterV8  {
    private ExpanderLayoutV8 mRecycleView = null;
    private BaseLinearLayoutManagerV8 mLayoutManager = null;

    public static final String TAG = "CCLExpander";
    private NewRecyclerAdapter mAdapter = null;
    private boolean mBackKey = false;
    private boolean mFirstShow = true;
    private final static int RESET_TO_TOP_HEIGHT = UtilV8.Div(100);
    private boolean isVertical = false;

    private final static int LOAD_NEXT_LIMIT = 4;
    private boolean mHasTitle;
    //    private ExtensionExpandLayout mExtensionExpandLayout;


    public ExpanderPresenterV8(Context context) {
        super(context);
        mRecycleView = makeView(context);
//        mRecycleView = mExtensionExpandLayout.getExpanderLayout();
//        mRecycleView.getRecycledViewPool().setMaxRecycledViews(SupportUtilV8.getSupportViewType("Panel"), 0);
//        mRecycleView.setItemViewCacheSize(200);

        BlockCacheUtil.createCache(mContext);
    }

    @Override
    public void onResume() {
        if (debug)
            Log.d("MyExp", "onResume");
        if (isLayoutShow && mAdapter != null) {
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().onResume();
            }
        }
        int first = mLayoutManager.findFirstVisibleItemPosition();
        if (debug)
            Log.d("MyExp", "first=" + first);
        if (first == -1) {
            mRecycleView.post(new Runnable() {
                @Override
                public void run() {
                    refreshChildLayout();
                }
            });
            if (!refreshChildLayout()) {
                mRecycleView.post(new Runnable() {
                    @Override
                    public void run() {
                        refreshChildLayout();
                    }
                });
            }
        }
    }

    public void onResume(boolean forceRefresh) {
        if ((isLayoutShow || forceRefresh) && mAdapter != null) {//出现横向滚动布局时，需要继续往内层传递，修复海报图不显示的问题，XHY-821
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().onResume();
            }
        }
    }

    @Override
    public void onPause() {
        if (mAdapter != null) {
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().onPause();
            }
        }
    }

    @Override
    public void onStop() {
        if (mAdapter != null) {
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().onStop();
            }
        }
        if (mContext instanceof Activity) {
            if (((Activity) mContext).isFinishing())
                BlockCacheUtil.destroyCache(mContext);
        }
    }

    @Override
    public void onDestroy() {
        if (Looper.myLooper() != Looper.getMainLooper()) {
            mMainThread.post(new Runnable() {
                @Override
                public void run() {
                    doOnDestroy();
                }
            });
        } else {
            doOnDestroy();
        }
    }

    private void doOnDestroy() {
        if (mAdapter != null) {
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().onDestroy();
            }
            mAdapter.getViewHolders().clear();
            mRecycleView.removeOnScrollListener(scrollListener);
        }
        mItemClickListener = null;
        mBoundaryListener = null;
        mOnPanelExposureListener = null;
    }

    @Override
    public void onNewIntent(Intent intent) {
//        if (isLayoutShow && mAdapter != null){
        if (mAdapter != null) {//只会回调到当前版面，不需要判断isLayoutShow
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().onNewIntent(intent);
            }
        }
    }

    @Override
    public View getView() {
        return mRecycleView;
    }


    // TODO 遍历 找第一个可落焦
    @Override
    public View getTopFirstView() {
        if (mRecycleView.getChildCount() <= 0)
            return null;
        List<ContainerV8> mContents = mContainer.contents;
        if (!CollectionUtil.isEmpty(mContents)) {
            int size = mContents.size();
            ContainerV8 mContainerV8 = mContents.get(0);
            if (mContainerV8.type.equals(SupportUtilV8.TITLE)) {
                if (size > 1) {
                    NewRecyclerViewHolder holder = (NewRecyclerViewHolder) mRecycleView.getChildViewHolder(mRecycleView.getChildAt(1));
                    return holder.getPresenter().getTopFirstView();
                }
            } else {
                NewRecyclerViewHolder holder = (NewRecyclerViewHolder) mRecycleView.getChildViewHolder(mRecycleView.getChildAt(0));
                return holder.getPresenter().getTopFirstView();
            }
        }
        return null;
    }

    public void setNeedScroll(boolean b, String tag) {
        if (mLayoutManager != null)
            mLayoutManager.setNeedScroll(b, tag);
    }

    @Override
    public void resetDefaultScrollState() {
        int count = mRecycleView.getChildCount();
        for (int i = 0; i < count; i++) {
            NewRecyclerViewHolder holder = (NewRecyclerViewHolder) mRecycleView.getChildViewHolder(mRecycleView.getChildAt(i));
            if (holder != null) {
                holder.getPresenter().resetDefaultScrollState();
            }
        }
        mRecycleView.scrollToPosition(0);
        if (mBackKey) {
            mRecycleView.post(new Runnable() {
                @Override
                public void run() {
                    scrollListener.onScrollStateChanged(mRecycleView, 0);
                    if (mLayoutManager != null) {
                        //走一次曝光刷新逻辑
                        int first = mLayoutManager.findFirstVisibleItemPosition();
                        int last = mLayoutManager.findLastVisibleItemPosition();
                        if (debug)
                            Log.d("MyExp", "resetDefaultScrollState, first=" + first + ", last=" + last);
                        for (int i = first; i <= last; i++) {
                            NewRecyclerViewHolder holder = (NewRecyclerViewHolder) mRecycleView.findViewHolderForAdapterPosition(i);
                            if (holder != null && holder.getPresenter() != null) {
                                holder.getPresenter().setVisible(true);
                                holder.refreshUI();
                            }
                        }
                    }
                }
            });
            mBackKey = false;
        }
    }

    @Override
    public void onLayoutHide(boolean isPause) {
        super.onLayoutHide(isPause);
        isLayoutShow = false;
        if (mLayoutManager == null)
            return;
        int first = mLayoutManager.findFirstVisibleItemPosition();
        int last = mLayoutManager.findLastVisibleItemPosition();
        for (int i = first; i <= last; i++) {
            NewRecyclerViewHolder holder = (NewRecyclerViewHolder) mRecycleView.findViewHolderForAdapterPosition(i);
            if (holder != null) {
                holder.getPresenter().onLayoutHide(isPause);
            }
        }
        List<RecyclerView.ViewHolder> mAttachedScrap = mRecycleView.getHolders("mAttachedScrap");
        List<RecyclerView.ViewHolder> mChangedScrap = mRecycleView.getHolders("mChangedScrap");
        List<RecyclerView.ViewHolder> mCachedViews = mRecycleView.getHolders("mCachedViews");
        recycle(mAttachedScrap, isPause);
        recycle(mChangedScrap, isPause);
        recycle(mCachedViews, isPause);
    }

    private void recycle(List<RecyclerView.ViewHolder> holders, boolean isPause) {
        if (holders != null) {
            for (RecyclerView.ViewHolder holder : holders) {
                ((NewRecyclerViewHolder) holder).getPresenter().onLayoutHide(isPause);
            }
        }
    }


    @Override
    public boolean obtainFocus() {
        View v = getTopFirstView();
        if (v != null) {
            if (mLayoutManager != null) {
                mLayoutManager.setIsFirstScroll(true);
            }
            return v.requestFocus();

        }
        return false;
    }

    @Override
    public boolean isShow() {
        return isLayoutShow;
    }

    @Override
    public void onLayoutShow() {
        if (debug)
            Log.d("MyExp", "onLayoutShow...");
        super.onLayoutShow();
        isLayoutShow = true;
        if (mLayoutManager == null)
            return;
        int first = mLayoutManager.findFirstVisibleItemPosition();
        if (debug)
            Log.d("MyExp", "first=" + first);
        if (first == -1) {
            mRecycleView.post(new Runnable() {
                @Override
                public void run() {
                    refreshChildLayout();
                }
            });
        } else {
            if (!refreshChildLayout()) {
                mRecycleView.post(new Runnable() {
                    @Override
                    public void run() {
                        refreshChildLayout();
                    }
                });
            }
        }
    }

    @Override
    public void onIdleShow() {
        if (mAdapter != null) {
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().onIdleShow();
            }
        }
    }

    private boolean refreshChildLayout() {
        int first = mLayoutManager.findFirstVisibleItemPosition();
        int last = mLayoutManager.findLastVisibleItemPosition();
        last++;
        if (debug)
            Log.d("MyExp", "refreshChildLayout, first=" + first + ", last=" + last + ", mFirstShow=" + mFirstShow);
        boolean findHolder = false;
        for (int i = first; i <= last; i++) {
            NewRecyclerViewHolder holder = (NewRecyclerViewHolder) mRecycleView.findViewHolderForAdapterPosition(i);
            if (holder != null) {
                if (debug)
                    Log.d("MyExp", "holder.getPresenter()=" + holder.getPresenter());
                findHolder = true;
                holder.getPresenter().setVisible(true);
                holder.getPresenter().onLayoutShow();
            }
        }
        mFirstShow = false;
        return findHolder;
    }


    int start = 0;

    public void insert(ContainerV8 container, int index) {
        Log.e("lll", "insert data");
        if (mAdapter != null) {
            mAdapter.insertData(container, index);
            mRecycleView.post(new Runnable() {
                @Override
                public void run() {
                    if (mRecycleView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE) {
                        for (int i = start; i < start + 1; i++) {
                            NewRecyclerViewHolder holder =
                                    (NewRecyclerViewHolder) mRecycleView.findViewHolderForAdapterPosition(i);
                            if (holder != null) {
                                holder.refreshUI();
                            }
                        }
                    }
                }
            });
            start++;
        }
    }


    public void insertList(List<ContainerV8> container) {
        if (mAdapter != null)
            mAdapter.insertList(container);
    }

    public void remove(ContainerV8 container) {
        if (mAdapter != null)
            mAdapter.remove(container);
    }

    public void remove(int index) {
        if (mAdapter != null)
            mAdapter.remove(index);
    }

    private boolean hasTitle(ContainerV8 container) {
        return container.contents != null && container.contents.size() > 0 && TextUtils.equals(container.contents.get(0).type, SupportUtilV8.TITLE);
    }


    @Override
    public void setContainer(ContainerV8 container) {
        super.setContainer(container);
        mAdapter = new NewRecyclerAdapter();
        mAdapter.setDebugMode(debug);
        ExpanderV8 mExpander = (ExpanderV8) container.contentObject;
        int space = 40;
        int oritention = SpaceItemDecoration.HORIZONTAL;
        mHasTitle = hasTitle(container);
        if (mExpander != null) {
            oritention = mExpander.orientation;
            mLayoutManager = oritention == SpaceItemDecoration.HORIZONTAL ?
                    new HorizontalLayoutManagerV8(mContext) : new VerticalLayoutManagerV8(mContext);
            space = mExpander.space;
//            if (mHasTitle) {
//                ContainerV8 titleContainer = new ContainerV8();
//                titleContainer.type = SupportUtilV8.TITLE;
//                titleContainer.parseContent();
//                PanelV8 p = new PanelV8();
//                p.panel_title = mExpander.panel_title;
//                titleContainer.contentObject = p;
//                List<ContainerV8> mContents = container.contents;
//                if (mContents != null && mContents.size() > 0) {
//                    container.contents.add(0, titleContainer);
//                }
//            }
        }

        isVertical = mExpander == null || oritention == 1;
        if (mLayoutManager == null)
            mLayoutManager = new HorizontalLayoutManagerV8(mContext);
        mRecycleView.setChildDrawingOrderCallback(drawingOrderCallback);
        mRecycleView.setOnScrollListener(scrollListener);
        mRecycleView.setNestedScrollingEnabled(true);
        mRecycleView.setHasFixedSize(true);
        mRecycleView.setLayoutManager(mLayoutManager);
        mRecycleView.addItemDecoration(new SpaceItemDecoration(space).setOritention(oritention));
        mAdapter.setOnItemClickListener(this);
        mAdapter.setOnBoundaryListener(this);
        mAdapter.setOnPanelExposureListener(this);
        mRecycleView.setAdapter(mAdapter);
        List<ContainerV8> mContents = container.contents;

        if (mContents == null || mContents.isEmpty()) return;
        mAdapter.refreshUI(mContents);
        start = mContents.size();
    }

    @Override
    public void setOnBoundaryListener(OnBoundaryListenerV8 listener) {
        super.setOnBoundaryListener(listener);
        if (mRecycleView != null){
            mRecycleView.setOnBoundaryListener(listener);
        }
    }

    public static Handler mMainThread = new Handler(Looper.getMainLooper());
    private Runnable delayRefreshUIRunnable = new Runnable() {
        @Override
        public void run() {
            int first = mLayoutManager.findFirstVisibleItemPosition();
            int last = mLayoutManager.findLastVisibleItemPosition();
            if (debug)
                Log.d("MyExp", "delayRefreshUIRunnable, fist=" + first + ", last=" + last);
            for (int i = first; i <= last; i++) {
                NewRecyclerViewHolder holder = (NewRecyclerViewHolder) mRecycleView.findViewHolderForAdapterPosition(i);
                if (holder != null) {
                    holder.getPresenter().setVisible(true);
                    holder.refreshUI();
                }
            }
            if (Build.VERSION_CODES.JELLY_BEAN_MR1 == Build.VERSION.SDK_INT) {
                UiCompat.getInstance().forceRefresh(null);
            }
        }
    };

    private boolean isSlidingUpward = false;
    RecyclerView.OnScrollListener scrollListener = new RecyclerView.OnScrollListener() {
        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            if (mLayoutManager == null)
                return;
            int first = mLayoutManager.findFirstVisibleItemPosition();
            int last = mLayoutManager.findLastVisibleItemPosition();
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                mMainThread.removeCallbacks(delayRefreshUIRunnable);
                if (isVertical && mBoundaryListener != null) {
                    if (!mRecycleView.canScrollVertically(-1)) {
                        //TODO
//                            mBoundaryListener.onTopBoundary(mRecycleView,mContainer,0);
                        if (isVertical && mRecycleView.computeVerticalScrollOffset() < RESET_TO_TOP_HEIGHT) {//如果recycleView的绝对滑动高度小于量值，就滚动到最顶端
                            resetDefaultScrollState();
                        }
                    } else {
//                        mBoundaryListener.onTopItemFocus(false);
                    }
                }
                mMainThread.postDelayed(delayRefreshUIRunnable, 120);
            } else {
                mMainThread.removeCallbacks(delayRefreshUIRunnable);
            }
            if (isVertical) {
                //垂直滚动
                for (int i = first; i <= last; i++) {
                    NewRecyclerViewHolder holder = (NewRecyclerViewHolder) mRecycleView.findViewHolderForAdapterPosition(i);
                    if (holder != null) {
                        holder.getPresenter().setScrollState(newState);
                    }
                }
            }

        }

        @Override
        public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
            mMainThread.removeCallbacks(delayRefreshUIRunnable);

            int first = mLayoutManager.findFirstVisibleItemPosition();
            int last = mLayoutManager.findLastVisibleItemPosition();
            for (int i = first; i <= last; i++) {
                NewRecyclerViewHolder holder = (NewRecyclerViewHolder) mRecycleView.findViewHolderForAdapterPosition(i);
                if (holder != null) {
                    holder.getPresenter().onScrolled(dx, dy);
                }
            }
            super.onScrolled(recyclerView, dx, dy);
            isSlidingUpward = dy > 0;
        }
    };


    RecyclerView.ChildDrawingOrderCallback drawingOrderCallback = new RecyclerView.ChildDrawingOrderCallback() {
        @Override
        public int onGetChildDrawingOrder(int childCount, int i) {
            View focusedChild = mRecycleView.getFocusedChild();
            int focusViewIndex = mRecycleView.indexOfChild(focusedChild);
            if (focusViewIndex == -1) {
                return i;
            }
            if (focusViewIndex == i) {
                return childCount - 1;
            } else if (i == childCount - 1) {
                return focusViewIndex;
            } else {
                return i;
            }
        }
    };


    @Override
    public boolean onLeftBoundary(View leaveView, ContainerV8 c, int position) {
        if (mBoundaryListener != null) {
            if (mLayoutManager.getOrientation() == OrientationHelper.VERTICAL) {
                return mBoundaryListener.onLeftBoundary(leaveView, c, position);
            } else {
                if (position == 0)
                    return mBoundaryListener.onLeftBoundary(leaveView, c, position);
            }
        }
        return false;
    }

    @Override
    public boolean onTopBoundary(View leaveView, ContainerV8 c, int position) {
        if (mBoundaryListener != null) {
            if (mLayoutManager.getOrientation() == OrientationHelper.HORIZONTAL)
                return mBoundaryListener.onTopBoundary(leaveView, c, position);
            else if (position == 0 || (position == 1 && mHasTitle))
                return mBoundaryListener.onTopBoundary(leaveView, c, position);
        }
        return false;
    }

    @Override
    public boolean onDownBoundary(View leaveView, ContainerV8 c, int position) {
        if (mLayoutManager.getOrientation() == OrientationHelper.VERTICAL) {
            if (position == mContainer.contents.size() - 1) {
                loadError(ResultCode.NO_NET);
            }
            if (position >= mContainer.contents.size() - LOAD_NEXT_LIMIT) {
                loadNext(position == mContainer.contents.size() - 1);
            }
        }

        if (mBoundaryListener != null) {
            if (mLayoutManager.getOrientation() == OrientationHelper.VERTICAL) {
                int mItemCount = mRecycleView.getAdapter().getItemCount();
                //增加竖向只有一排判断
                if (position == mItemCount - 1 || mItemCount == 1) {
                    return mBoundaryListener.onDownBoundary(leaveView, c, position);
                }

            } else {
                return mBoundaryListener.onDownBoundary(leaveView, c, position);
            }
        }
        return false;
    }

    @Override
    public boolean onRightBoundary(View leaveView, ContainerV8 c, int position) {

        if (mLayoutManager.getOrientation() == OrientationHelper.HORIZONTAL) {
            if (position == mContainer.contents.size() - 1) {
                loadError(ResultCode.NO_NET);
            }
            if (position > mContainer.contents.size() - LOAD_NEXT_LIMIT) {
                loadNext(position == mContainer.contents.size() - 1);
            }
        }

        if (mBoundaryListener != null) {
            if (mLayoutManager.getOrientation() == OrientationHelper.HORIZONTAL) {
                if (/*( position == (mRecycleView.getAdapter().getItemCount() - 2))
                        ||*/ position == (mRecycleView.getAdapter().getItemCount() - 1)) {
                    return mBoundaryListener.onRightBoundary(leaveView, c, position);
                }
            } else
                return mBoundaryListener.onRightBoundary(leaveView, c, position);
        }
        return false;
    }

    private boolean isLoading = false;

    private void loadNext(boolean needScroll) {
//        if (!isLoading && UtilV8.isNetConnected(mContext)){
//            isLoading = true;
//            //TODO
//        }
//        if (mOnLoadMoreListener != null){
//           mOnLoadMoreListener.loadMore(new OnLoadMoreListener.LoadMoreCallback() {
//               @Override
//               public void onLoad(List<ContainerV8> containers) {
//                   insertList(containers);
//               }
//           });
//        }
    }

    private void loadError(@ResultCode int code) {
        if (!isLoading) {
            if (mOnLoadMoreListener != null) {
                mOnLoadMoreListener.loadError(code);
            }
        }
    }

    @Override
    public View holdFocusedView() {
        return null;
    }

    @Override
    public void restoreFocus() {

    }

    @Override
    public ExpanderLayoutV8 makeView(Context context) {
        return new ExpanderLayoutV8(mContext);
    }

    @Override
    public boolean onBackPressed() {
        Log.e("uisdk", "Expander onBackPressed ");
        mBackKey = true;
        return mBoundaryListener != null && mBoundaryListener.onBackPressed();
    }

    private OnLoadMoreListener mOnLoadMoreListener;

    public void setOnLoadMoreListener(OnLoadMoreListener listener) {
        this.mOnLoadMoreListener = listener;
    }

    @Override
    public void onPageChanedDirection(int direction) {
        super.onPageChanedDirection(direction);
        Log.d(TAG, "supportLayoutAnim = " + supportLayoutAnim);
        if (supportLayoutAnim) {
            if (mAdapter != null) {
                List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
                for (NewRecyclerViewHolder holder : holders) {
                    holder.getPresenter().onPageChanedDirection(direction);
                }
            }
        }
    }

}
