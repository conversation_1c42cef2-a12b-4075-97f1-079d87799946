package com.coocaa.uisdk.view;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.BitmapShader;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.Shader;
import android.util.Log;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.FrameLayout;

import com.coocaa.uisdk.R;
import com.coocaa.uisdk.listener.IBlockFocusLightView;
import com.coocaa.uisdk.utils.UtilV8;

public class SimpleFocusLightView extends View implements IBlockFocusLightView, Animator.AnimatorListener, ValueAnimator.AnimatorUpdateListener {
    private int mViewWidth,mViewHeight;
    private int mTranSize = 0;
    private long mTranDuration = 600;
    private static final int littleWidth = UtilV8.Div(160);
    private static final int transDurDiv = UtilV8.Div(284);
    private int offsetX,offsetY;
    private int length;
    private ValueAnimator mValueAnimator;
    private Rect mRect = new Rect();
    private Paint p = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Matrix mMatrix = new Matrix();
    private  BitmapShader mShader;

    public SimpleFocusLightView(Context context) {
        super(context);
        setVisibility(GONE);

    }

    static Bitmap bit;
    @Override
    public void setSize(int w, int h) {
        if (w > h) {//宽度>高度，横状块块
            mViewWidth = h <= littleWidth ? (int)(h * 2.5f) : (int) (h * 1.8f);
            mViewHeight = (w*1f/h >2.5f) ? h * 4: h * 3;
        } else {//竖状块块
            mViewWidth = w <= littleWidth ? (int)(w * 2.5f) : (int) (w * 1.8f);
            mViewHeight = w * ((h*1f/w >2.5f) ? 4:3);
        }
        offsetX= -mViewWidth;
        offsetY = -(mViewHeight - h)/2;
        mRect.set(0,0,mViewWidth,mViewHeight);
        mTranDuration = (w * 600) / transDurDiv;
        if (mTranDuration > 1500)
            mTranDuration = 1500;
        if (mTranDuration < 1000)
            mTranDuration = 1000;
        mTranSize = Math.max(w, mViewWidth);


        if (mShader == null){
            if(bit == null)
                bit  = BitmapFactory.decodeResource(getResources(), R.drawable.focus_light);
            mShader = new BitmapShader(bit, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP);
            try {
                float hf = ((float) mViewHeight) / bit.getHeight();
                float hw = ((float) mViewWidth) / bit.getWidth();
                mMatrix.postScale(hw,hf);
                mShader.setLocalMatrix(mMatrix);
                p.setShader(mShader);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (mValueAnimator == null){
            mValueAnimator = ValueAnimator.ofInt(mTranSize *2);
            mValueAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
            mValueAnimator.setDuration(mTranDuration);
            mValueAnimator.setStartDelay(300);
            mValueAnimator.addListener(this);
            mValueAnimator.addUpdateListener(this);
            mValueAnimator.setTarget(this);
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        canvas.rotate(30);
        canvas.translate(length,offsetY);
        canvas.drawRect(mRect, p);
    }

    @Override
    public void startFocusLightAnim(boolean hasFocus) {
      if (hasFocus){
          if (mValueAnimator !=null)
          mValueAnimator.start();
      }else{
         if (mValueAnimator !=null )
             mValueAnimator.cancel();
         length = offsetX;
      }
    }

    @Override
    public View getView() {
        return this;
    }

    @Override
    public void onAnimationStart(Animator animation) {
        setVisibility(VISIBLE);
    }

    @Override
    public void onAnimationEnd(Animator animation) {
      setVisibility(GONE);
    }

    @Override
    public void onAnimationCancel(Animator animation) {

    }

    @Override
    public void onAnimationRepeat(Animator animation) {

    }

    @Override
    public void onAnimationUpdate(ValueAnimator animation) {
        length = (int) animation.getAnimatedValue() + offsetX;
        postInvalidate();
    }
}
