package com.coocaa.uisdk.presenter;

import android.content.Context;
import android.view.View;

import com.coocaa.uisdk.model.ContainerV8;


public class ReferencePresenterV8 extends PresenterV8
{
    protected View view;

    public ReferencePresenterV8(Context context)
    {
        super(context);
        view = makeView(context);
    }

   public View makeView(Context context){
        return null;
   }

    @Override
    public void setContainer(ContainerV8 container)
    {
        container.focusable = 1;
        super.setContainer(container);
    }

    @Override
    public void onResume()
    {

    }

    @Override
    public void onPause()
    {

    }

    @Override
    public void onStop()
    {

    }

    @Override
    public void onDestroy()
    {

    }

    @Override
    public View getView()
    {
        return view;
    }


    @Override
    public boolean obtainFocus() {
        View v = getTopFirstView();
        if (v !=null && v.isFocusable()){
            return view.requestFocus();
        }
        return false;
    }


    @Override
    public View getTopFirstView() {
        if (view.isFocusable())
            return view;
        return null;
    }
}
