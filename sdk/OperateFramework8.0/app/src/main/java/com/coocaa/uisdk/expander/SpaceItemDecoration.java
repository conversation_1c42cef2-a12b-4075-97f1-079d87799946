package com.coocaa.uisdk.expander;

import android.graphics.Rect;
import android.support.v7.widget.RecyclerView;
import android.view.View;

public class SpaceItemDecoration extends RecyclerView.ItemDecoration {

    private int space = 40;
    private int oritention;
    public static final int HORIZONTAL = 0;
    public static final int VERTICAL = 1;
    private boolean firstSpace = true, lastSpace = true;

    public SpaceItemDecoration(int space) {
        this.space = space;
    }

    public RecyclerView.ItemDecoration setOritention(int oritention) {
        this.oritention = oritention;
        if (oritention > 1) oritention = 1;
        return this;
    }

    public RecyclerView.ItemDecoration isDrawFirstSpace(boolean show) {
        this.firstSpace = show;
        return this;
    }

    public RecyclerView.ItemDecoration isDrawLastSpace(boolean show) {
        this.lastSpace = show;
        return this;
    }


    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        int current = parent.getChildAdapterPosition(view);
        if (oritention == HORIZONTAL) {
            if (current != 0)
                outRect.left = space;
        } else {
            if (current != 0)
                outRect.top = space;
        }
    }
}