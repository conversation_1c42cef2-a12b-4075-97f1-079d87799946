package com.coocaa.uisdk.model;

import android.text.TextUtils;

import com.alibaba.fastjson.JSON;

import java.io.Serializable;

public class Plugin implements Serializable {
    public String packagename;
    public String category;
    public String type;
    public String params;
    public String data;

    public transient Object parsedData;

    /**
     * 解析data ，不一定是点击事件
     */
    public <T> T parsePluginParams(Class<T> t) {
        if (!TextUtils.isEmpty(data)) {
            try {
                return JSON.parseObject(data, t);
            } catch (Exception e) {

            }
        }
        return null;
    }

    public <T> T parseData(Class<T> t) {
        if(parsedData == null) {
            parsedData = parsePluginParams(t);
        }
        return (T) parsedData;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("Plugin{");
        sb.append("packagename='").append(packagename).append('\'');
        sb.append(", category='").append(category).append('\'');
        sb.append(", type='").append(type).append('\'');
        sb.append(", params='").append(params).append('\'');
        sb.append(", data='").append(data).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
