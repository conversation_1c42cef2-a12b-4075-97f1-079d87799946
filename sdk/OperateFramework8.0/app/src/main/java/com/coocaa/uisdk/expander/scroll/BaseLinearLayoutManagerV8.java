package com.coocaa.uisdk.expander.scroll;

import android.content.Context;
import android.support.v7.widget.LinearLayoutManager;

/**
 * @Author: yuzhan
 */
public class BaseLinearLayoutManagerV8 extends LinearLayoutManager {

    public BaseLinearLayoutManagerV8(Context context, int orientation) {
        super(context, orientation, false);
    }

    public void setNeedScroll(boolean b, String tag) {}

    public void setIsFirstScroll(boolean b) {}
}
