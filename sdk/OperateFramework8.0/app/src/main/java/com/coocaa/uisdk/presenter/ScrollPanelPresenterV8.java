package com.coocaa.uisdk.presenter;

import android.content.Context;
import android.util.Log;

import com.coocaa.uisdk.expander.NewRecyclerViewHolder;
import com.coocaa.uisdk.model.ContainerV8;
import com.coocaa.uisdk.view.HorizontalScrollView;
import com.coocaa.uisdk.view.PanelLayoutV8;
import com.coocaa.uisdk.view.ScrollPanelLayoutV8;

import java.util.List;

import static android.widget.AbsListView.OnScrollListener.SCROLL_STATE_FLING;
import static android.widget.AbsListView.OnScrollListener.SCROLL_STATE_IDLE;


public class ScrollPanelPresenterV8 extends PanelPresenterV8 {
    private static final int delayMillis = 100;
    private long lastScrollUpdate = -1;

    public ScrollPanelPresenterV8(Context context) {
        super(context);
        ((ScrollPanelLayoutV8) mNewPanelLayout).addOnScrollChangeListener(mListener);
    }

    private Runnable scrollerTask = new Runnable() {
        @Override
        public void run() {
            long currentTime = System.currentTimeMillis();
            if ((currentTime - lastScrollUpdate) > 100) {
                lastScrollUpdate = -1;
                onScrollEnd();
            } else {
                mNewPanelLayout.postDelayed(this, delayMillis);
            }
        }
    };


    private HorizontalScrollView.OnScrollChangedListener mListener = new HorizontalScrollView.OnScrollChangedListener() {
        @Override
        public void onScrollChanger(int l, int t, int oldl, int oldt) {
            if (lastScrollUpdate == -1) {
                onScrollStart();
                mNewPanelLayout.postDelayed(scrollerTask, delayMillis);
            }
            // 更新ScrollView的滑动时间
            lastScrollUpdate = System.currentTimeMillis();
        }
    };

    private boolean isScrolling = false;

    private void onScrollStart() {
        if (isScrolling) return;
        isScrolling = true;
        if (mAdapter != null) {
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().setScrollState(SCROLL_STATE_FLING);
            }
        }
    }

    private void onScrollEnd() {
        if (mAdapter != null) {
            List<NewRecyclerViewHolder> holders = mAdapter.getViewHolders();
            for (NewRecyclerViewHolder holder : holders) {
                holder.getPresenter().setScrollState(0);
            }
        }
        isScrolling = false;
    }

    @Override
    public void setContainer(ContainerV8 t) {
        super.setContainer(t);
    }

    @Override
    public PanelLayoutV8 makeView(Context context) {
        if (mNewPanelLayout == null)
            mNewPanelLayout = new ScrollPanelLayoutV8(context);
        return mNewPanelLayout;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mNewPanelLayout.removeCallbacks(scrollerTask);
    }
}
