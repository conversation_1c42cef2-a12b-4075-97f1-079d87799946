package com.coocaa.uisdk.presenter;

import android.content.Context;
import android.view.View;

import com.coocaa.uisdk.model.ContainerV8;
import com.coocaa.uisdk.view.BlockLayoutV8;

/*
 *   <p>
 *   图文型 海报 + 标题
 *   </p>
 */
public class BlockPresenterV8 extends PresenterV8 {
    protected BlockLayoutV8 view;
    protected boolean isPause;

    public BlockPresenterV8(Context context) {
        super(context);
        view = makeView(context);
    }

    public BlockLayoutV8 makeView(Context context) {
        return new BlockLayoutV8(context);
    }

    @Override
    public void onResume() {
        view.resumePostImage(true);
    }

    @Override
    public void onPause() {
    }

    @Override
    public void onStop() {
    }

    @Override
    public void onDestroy() {
        view.resumePostImage(false);
    }

    @Override
    public View getView() {
        return view;
    }

    @Override
    public void setContainer(ContainerV8 container) {
        container.focusable = 1;
        super.setContainer(container);
    }

    @Override
    public boolean obtainFocus() {
        return view.requestFocus();
    }

    @Override
    public View getTopFirstView() {
        if (view.isFocusable())
            return view;
        return null;
    }

    @Override
    public void onLayoutShow() {
        super.onLayoutShow();
        if (!isPause)
            view.resumePostImage(true);
    }

    @Override
    public void onLayoutHide(boolean isPause) {
        super.onLayoutHide(isPause);
        this.isPause = isPause;
        if (!isPause)
            view.resumePostImage(false);
    }

    @Override
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
//        onLayoutShow();
//        view.resumePostImage(true);
    }

    @Override
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        onLayoutHide(false);
//        view.resumePostImage(false);
    }
}
