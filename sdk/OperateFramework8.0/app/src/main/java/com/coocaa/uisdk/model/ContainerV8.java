package com.coocaa.uisdk.model;

import android.text.TextUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coocaa.uisdk.utils.SupportUtilV8;

import java.io.Serializable;
import java.util.List;

public class ContainerV8 implements Serializable {
    public int width, height;
    public int x, y;
    public String id = "";
    public String type;
    public String extra;
    public String plugin_info;
    public Object contentObject;
    public String parents;
    public int focusable;
    public List<ContainerV8> contents;
    public String rawType;
    private boolean canPrase = true;
    private boolean mParsed;
    private Object extraPlugin;
    public int position;
    public ContainerV8 parentContainer;

    /**
     * 替换的索引 从1开始
     */
    public int seq;
    public void parseContent() {
        if (mParsed) return;
        if (parseObjectByType(type))
            return;
        if (!TextUtils.isEmpty(parents)) {
            String[] types = parents.split(",");
            for (String s : types) {
                if (parseObjectByType(s))
                    return;
            }
        }
        mParsed = true;
    }

    public String getConfirmType() {
        return rawType;
    }

    private boolean parseObjectByType(String type) {
        if (SupportUtilV8.isSupport(type)) {
            if (TextUtils.equals(type, SupportUtilV8.PLUGIN)) {
                if (!TextUtils.isEmpty(plugin_info)) {
                    try {
                        contentObject = JSONObject.parseObject(plugin_info, SupportUtilV8.getSupportModel(type));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                if (contentObject != null) {
                    rawType = ((Plugin) contentObject).type;
                }
            } else {
                if (!TextUtils.isEmpty(extra)) {
                    contentObject = JSONObject.parseObject(extra, SupportUtilV8.getSupportModel(type));
                }
                rawType = type;
            }
            canPrase = true;
            return true;
        } else {
            canPrase = false;
        }

        return false;
    }

    public boolean canParse() {
        return canPrase;
    }

    public <T> T parsePluginParams(Class<T> t) {
        if (contentObject == null || !(contentObject instanceof Plugin)) {
            return null;
        }
        Plugin data = (Plugin) contentObject;
        if (!TextUtils.isEmpty(data.params)) {
            try {
                return JSON.parseObject(data.params, t);
            } catch (Exception e) {

            }
        }
        return null;
    }

    /**
     *  针对 panelPlugin 和BlockPlugin
     * @param t
     * @return
     */
    public Object parseExtraPluginInfo(Class t) {
        if (TextUtils.isEmpty(extra))return null;
        if (contentObject == null || !(contentObject instanceof Plugin)) {
            return null;
        }
        if (extraPlugin == null)
            extraPlugin =JSONObject.parseObject(extra,t);
        return extraPlugin;
    }

//    public PanelV8 parsePanelPluginInfo() {
//        if (TextUtils.isEmpty(extra))return null;
//        if (contentObject == null || !(contentObject instanceof Plugin)) {
//            return null;
//        }
//        if (TextUtils.equals(SupportUtilV8.PANEL,parents))
//        {
//            if (panelPlugin == null)
//                panelPlugin =JSONObject.parseObject(extra,PanelV8.class);
//            return panelPlugin;
//        }
//        return null;
//    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("Container{");
        sb.append("width=").append(width);
        sb.append(", height=").append(height);
        sb.append(", x=").append(x);
        sb.append(", y=").append(y);
        sb.append(", id='").append(id).append('\'');
        sb.append(", type='").append(type).append('\'');
        sb.append(", extra='").append(extra).append('\'');
        sb.append(", contentObject=").append(contentObject);
        sb.append(", parents='").append(parents).append('\'');
        sb.append(", focusable=").append(focusable);
        sb.append(", contents=").append(contents);
        sb.append(", rawType='").append(rawType).append('\'');
        sb.append(", canPrase=").append(canPrase);
        sb.append('}');
        return sb.toString();
    }
}
