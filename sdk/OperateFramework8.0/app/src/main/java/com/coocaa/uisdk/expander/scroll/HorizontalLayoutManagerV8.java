package com.coocaa.uisdk.expander.scroll;

import android.content.Context;
import android.graphics.Rect;
import android.support.v7.widget.OrientationHelper;
import android.support.v7.widget.RecyclerView;
import android.view.View;

import com.coocaa.uisdk.R;


/**
 * @Author: yuzhan
 */
public class HorizontalLayoutManagerV8 extends BaseLinearLayoutManagerV8 {

    private Context mContext;
    public HorizontalLayoutManagerV8(Context context) {
        super(context, OrientationHelper.HORIZONTAL);
        mContext = context;
    }

    @Override
    protected int getExtraLayoutSpace(RecyclerView.State state)
    {
        return mContext.getResources().getDimensionPixelOffset(R.dimen.px40);
    }

    @Override
    public boolean requestChildRectangleOnScreen(RecyclerView parent, View child, Rect rect,
                                                 boolean immediate)
    {
        android.util.Log.d("CCC", "horizontal requestChildRectangleOnScreen, parent=" + parent + ", child=" + child + ", rect=" + rect + ", imm=" + immediate + ", focusedChild=" + parent.getFocusedChild());

        final int parentLeft = getPaddingLeft() + getWidth() / 2;
        final int parentTop = getPaddingTop() + getHeight() / 2;
        final int childLeft = child.getLeft() + rect.left - child.getScrollX() + rect.width() / 2;
        final int childTop = child.getTop() + rect.top - child.getScrollY() + rect.height() / 2;

        final int offScreenLeft = Math.min(0, childLeft - parentLeft);
        final int offScreenTop = Math.min(0, childTop - parentTop);

        int dx = 0;
        if (getOrientation() == OrientationHelper.HORIZONTAL)
            dx = offScreenLeft != 0 ? offScreenLeft : (childLeft - parentLeft);

        if (dx != 0)
        {
            android.util.Log.d("CCC", "horizontal scroll dx=" + dx);
            if (immediate)
            {
                parent.scrollBy(dx, 0);
            } else
            {
                parent.smoothScrollBy(dx, 0);
            }
            return true;
        }
        return false;
    }
}
