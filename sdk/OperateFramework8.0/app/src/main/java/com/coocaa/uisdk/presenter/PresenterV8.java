package com.coocaa.uisdk.presenter;

import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.coocaa.uisdk.listener.ICardView;
import com.coocaa.uisdk.listener.IPresenterV8;
import com.coocaa.uisdk.listener.IView;
import com.coocaa.uisdk.listener.OnBoundaryListenerV8;
import com.coocaa.uisdk.listener.OnItemClickListenerV8;
import com.coocaa.uisdk.listener.OnPanelExposureListenerV8;
import com.coocaa.uisdk.model.ContainerV8;
import com.coocaa.uisdk.model.PageDirection;


public abstract class PresenterV8 implements IPresenterV8, OnItemClickListenerV8, OnBoundaryListenerV8 ,OnPanelExposureListenerV8{
    protected Context mContext;
    protected ContainerV8 mContainer = null;
    protected OnItemClickListenerV8 mItemClickListener = null;
    protected OnPanelExposureListenerV8 mOnPanelExposureListener;
    protected OnBoundaryListenerV8 mBoundaryListener = null;
    protected int mPosition = 0;
    protected boolean isLayoutShow = false;
    protected View mCurrFocusedView;
    protected boolean mVisible = false;
    protected boolean debug = false;
    protected boolean supportLayoutAnim = true;
    protected @PageDirection int direction;
    protected boolean isResumed = true;
    protected static final String TAG = "CCLUISDK";
    public PresenterV8(Context context) {
        this.mContext = context;
    }

    @Override
    public void setContainer(ContainerV8 o) {
        this.mContainer = o;
        final View view = getView();
        setParams(o, view);
        if (debug)
        {
            Log.d(TAG, "w : " + o.width + " ,h:" + o.height);
        }
        if (view instanceof IView) {
            ((IView) view).setSize(o.width, o.height);
            ((IView) view).setBlockData(o);
        }
        boolean focusable = o.focusable == 1;
        setFocusState(view, focusable);
        if (focusable) {
            view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    if (v instanceof IView)
                        ((IView) v).setFocus(hasFocus);
                }
            });
            view.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    click(v, mContainer);
                    if (v instanceof IView) {
                        ((IView) v).onClick();
                    }
                }
            });
            view.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View v, int keyCode, KeyEvent event) {
                    if (event.getAction() == KeyEvent.ACTION_DOWN) {
                        switch (keyCode) {
                            case KeyEvent.KEYCODE_DPAD_UP:
//                                if (event.getRepeatCount() == 0)
                                return onTopBoundary(v, mContainer, mPosition);
                            case KeyEvent.KEYCODE_DPAD_DOWN:
                                return onDownBoundary(v, mContainer, mPosition);
                            case KeyEvent.KEYCODE_DPAD_LEFT:
                                return onLeftBoundary(v, mContainer, mPosition);
                            case KeyEvent.KEYCODE_DPAD_RIGHT:
                                return onRightBoundary(v, mContainer, mPosition);
//                            case KeyEvent.KEYCODE_BUTTON_A:
//                                v.performClick();
//                                return true;
                            case KeyEvent.KEYCODE_ESCAPE:
                            case KeyEvent.KEYCODE_BACK:
                                return onBackPressed();
                        }
                    }
                    return false;
                }
            });
            view.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    if (event.getAction() == MotionEvent.ACTION_DOWN) {
                        Log.d("CCCC", "onTouch Down.");
                        if (view.isFocusable() && view.isFocusableInTouchMode())
                            view.requestFocus();
                        else if (event.getAction() == MotionEvent.ACTION_UP)
                            view.callOnClick();
                    }
                    return false;
                }
            });
        }
    }


    private void setParams(ContainerV8 o, View view) {
        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        if (params == null)
            params = new FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        if (o.width != 0)
            params.width = o.width;
        else
            params.width = ViewGroup.LayoutParams.WRAP_CONTENT;

        if (o.height != 0)
            params.height = o.height;
        else
            params.height = ViewGroup.LayoutParams.WRAP_CONTENT;
        params.leftMargin = o.x;
        params.topMargin = o.y;
        view.setLayoutParams(params);
    }


    @Override
    public void onIdleShow() {

    }

    @Override
    public void setSize(int w, int h) {
        View v = getView();
        if (v != null) {
            if (v instanceof IView)
                ((IView) v).setSize(w, h);
            else {
                ViewGroup.LayoutParams mLayoutParams = v.getLayoutParams();
                if (mLayoutParams == null)
                    mLayoutParams = new ViewGroup.LayoutParams(w, h);
                else {
                    mLayoutParams.width = w;
                    mLayoutParams.height = h;
                }
                v.setLayoutParams(mLayoutParams);
            }
        }
    }


    public void setFocusState(View v, boolean focusable) {
        v.setFocusable(focusable);
        v.setFocusableInTouchMode(focusable);
        v.setClickable(focusable);
    }

    @Override
    public void setOnBoundaryListener(OnBoundaryListenerV8 listener) {
        this.mBoundaryListener = listener;
        View view = getView();
        if (view instanceof ICardView)
            ((ICardView) view).setOnBoundaryListener(mBoundaryListener);
    }


    @Override
    public void setOnItemClickListener(OnItemClickListenerV8 listener) {
        this.mItemClickListener = listener;
        View view = getView();
        if (view instanceof ICardView)
            ((ICardView) view).setOnItemClickListener(mItemClickListener);
    }

    @Override
    public void panelExposureListener(ContainerV8 container) {
        Log.d("CCLUISDK", "isLayoutShow = "+isLayoutShow + ",isResumed = "+isResumed);

        if (mOnPanelExposureListener != null && isLayoutShow && isResumed){
            mOnPanelExposureListener.panelExposureListener(container);
        }
    }

    @Override
    public void setOnPanelExposureListener(OnPanelExposureListenerV8 listener) {
        this.mOnPanelExposureListener = listener;
    }

    @Override
    public void click(View v, ContainerV8 c) {
        if (mItemClickListener != null) {
            mItemClickListener.click(v, c);
        }
    }

    @Override
    public View holdFocusedView() {
        View mView = getView();
        if (mView != null) {
            if (mView.isFocused())
                mCurrFocusedView = mView;
            else
                mCurrFocusedView = mView.findFocus();
        }
        return mCurrFocusedView;
    }

    @Override
    public void restoreFocus() {
        if (mCurrFocusedView != null)
            mCurrFocusedView.requestFocus();
    }


    @Override
    public void setPosition(int position) {
        this.mPosition = position;
        View view = getView();
        if (view instanceof ICardView)
            ((ICardView) view).setPosition(mPosition);
    }

    @Override
    public int getPosition() {
        return mPosition;
    }

    @Override
    public View getTopFirstView() {
        View mView = getView();
        if (mView!=null && mView.isFocusable())
            return mView;
        return null;
    }


    @Override
    public boolean onRightBoundary(View leaveView, ContainerV8 c, int position) {
        if (mBoundaryListener != null)
            return mBoundaryListener.onRightBoundary(leaveView, c, position);
        return false;
    }

    @Override
    public boolean onLeftBoundary(View leaveView, ContainerV8 c, int position) {
        if (mBoundaryListener != null)
            return mBoundaryListener.onLeftBoundary(leaveView, c, position);
        return false;
    }

    @Override
    public boolean onTopBoundary(View leaveView, ContainerV8 c, int position) {
        if (mBoundaryListener != null)
            return mBoundaryListener.onTopBoundary(leaveView, c, position);
        return false;
    }

    @Override
    public boolean onDownBoundary(View leaveView, ContainerV8 c, int position) {
        if (mBoundaryListener != null)
            return mBoundaryListener.onDownBoundary(leaveView, c, position);
        return false;
    }

    @Override
    public boolean onBackPressed() {
        return mBoundaryListener != null && mBoundaryListener.onBackPressed();
    }

    /**
     * 还原当前pluginlayou横向滚动状态为最初，具体由各个基类实现
     */
    public void resetDefaultScrollState() {

    }

    /**
     * 外部让AbsBaseMajorPluginLayout去获取焦点
     *
     * @return 获取焦点成功or失败
     */
    public boolean obtainFocus() {
        return false;
    }

    /**
     * 显示当前PluginLayout，用于各个插件在恢复显示时的处理
     */
    public void onLayoutShow() {
        isLayoutShow = true;
        Log.d(TAG, "w : " + mContainer.width + " ,h:" + mContainer.height);
    }

    /**
     * 当前PluginLayout移出屏幕外，用于各个插件在不显示时的处理
     */
    public void onLayoutHide(boolean isPause) {
        isLayoutShow = false;
    }

    @Override
    public void onPause() {
        isResumed = false;
    }

    @Override
    public void onStop() {
        isResumed = false;
    }


    @Override
    public void onResume() {
        isResumed = true;
        Log.d(TAG, "w : " + mContainer.width + " ,h:" + mContainer.height);
    }

    public boolean isShow() {
        return isLayoutShow;
    }


    @Override
    public void onNewIntent(Intent intent) {
        //no impl default.
    }


    @Override
    public void onDetachedFromWindow() {

    }

    @Override
    public void onAttachedToWindow() {

    }

    @Override
    public void onViewRecycled() {

    }

    @Override
    public void setVisible(boolean visible) {
        this.mVisible = visible;
    }

    @Override
    public void setScrollState(int state) {

    }

    @Override
    public void onScrolled(int dx, int dy) {

    }

    @Override
    public boolean needNotifyScrollState() {
        return false;
    }

    @Override
    public void setDebug(boolean debug) {
        this.debug = debug;
    }

    @Override
    public void onPageChanedDirection(int direction) {
        this.direction = direction;
    }

    @Override
    public void setSupportLayoutAnim(boolean support) {
        this.supportLayoutAnim  = support ;
    }
}
