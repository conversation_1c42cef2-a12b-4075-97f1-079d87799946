package com.coocaa.uisdk.utils;

public class CircleParamsV8 {
    private float radius = 0;
    private boolean asCircle = false;

    public void setRadius(float radius) {
        this.radius = radius;
    }

    public float getRadius() {
        return radius;
    }

    public boolean asCircle() {
        return asCircle;
    }

    public CircleParamsV8(float r){
        this.radius = r;
    }

    public CircleParamsV8(boolean asCircle) {
        this.asCircle = asCircle;
    }
}
