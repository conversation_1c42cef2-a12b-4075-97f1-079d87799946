package com.coocaa.uisdk.utils;

public class AndroidUtil {
    public static boolean isTouchMode = false;

    public static void printStackTrace(String log_tag, String cause) {
        printStackTrace(log_tag, cause, Thread.currentThread().getStackTrace());
    }

    public static void printStackTrace(String log_tag, Throwable e) {
        if(e != null && e.getCause() != null)
            printStackTrace(log_tag, e.getCause().toString(), e.getStackTrace());
    }

    public static void printStackTrace(String log_tag, String cause, StackTraceElement[] trace) {
        if(trace == null || log_tag == null)
            return ;
        StringBuilder sb = new StringBuilder();
        sb.append("Manually printStackTrace. Cause by: ");
        if(cause != null)
            sb.append(cause);
        sb.append(":\n");
        try {
            for (StackTraceElement traceElement : trace) {
                sb.append("\tat ");
                sb.append(traceElement);
                sb.append("\n");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        android.util.Log.w(log_tag, sb.toString());
    }
}
