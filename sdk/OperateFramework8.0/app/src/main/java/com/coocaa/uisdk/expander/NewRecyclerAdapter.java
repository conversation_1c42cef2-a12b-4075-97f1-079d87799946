package com.coocaa.uisdk.expander;

import android.support.v7.widget.RecyclerView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.coocaa.uisdk.listener.IPresenterV8;
import com.coocaa.uisdk.listener.OnBoundaryListenerV8;
import com.coocaa.uisdk.listener.OnItemClickListenerV8;
import com.coocaa.uisdk.listener.OnPanelExposureListenerV8;
import com.coocaa.uisdk.model.ContainerV8;
import com.coocaa.uisdk.presenter.PresenterFactoryV8;
import com.coocaa.uisdk.utils.CollectionUtil;
import com.coocaa.uisdk.utils.SupportUtilV8;

import java.util.List;

public class NewRecyclerAdapter extends RecyclerView.Adapter<NewRecyclerViewHolder> {

    private List<ContainerV8> mContainers = CollectionUtil.arrayList();
    private OnItemClickListenerV8 mItemClickListener = null;
    private OnPanelExposureListenerV8 mPanelExposureListener;
    private OnBoundaryListenerV8 mBoundaryListener = null;
    private List<NewRecyclerViewHolder> mViewHolders = CollectionUtil.arrayList();
    private boolean mNeedRefresh = true;
    public static final int END_POSITION = -1;
    public boolean debugMode;
    public NewRecyclerAdapter() {
        setHasStableIds(true);
    }

    public void refreshUI(List<ContainerV8> containers) {
        if (CollectionUtil.isEmpty(containers)) return;
        mContainers.clear();
        mViewHolders.clear();
        mContainers.addAll(containers);
        notifyDataSetChanged();
    }


    /**
     * 不支持指定位置
     * @param list
     */
    public void insertList(List<ContainerV8> list){
        if (!CollectionUtil.isEmpty(list) ) {
            int index = mContainers.size();
            mContainers.addAll(list);
            notifyItemRangeInserted(index,list.size());
        }
    }

    public void insertData(ContainerV8 c, int insertIndex) {
        if (c == null) return;
//        final IPresenter p = holdFocus();
        int index = mContainers.size();
        if (insertIndex == END_POSITION || insertIndex >= index) {
            mContainers.add(c);
            notifyItemRangeInserted(index,1);
        } else {
            mContainers.add(insertIndex, c);
            int count = mContainers.size() - 1 - insertIndex;
            notifyItemRangeChanged(insertIndex, count);
        }
//        restoreFocus(p);
    }

    public void remove(ContainerV8 c) {
        if (c == null) return;
        int rawIndex = mContainers.indexOf(c);
        if (rawIndex != -1) {
            remove(rawIndex);
        }
    }

    public void remove(int index) {
        if (index != -1 && !CollectionUtil.isEmpty(mContainers)) {
            mContainers.remove(index);
            notifyItemRemoved(index);
        }
    }


    public void restoreFocus(IPresenterV8 presenter) {
        if (presenter != null)
            presenter.restoreFocus();
    }

    private IPresenterV8 holdFocus() {
        IPresenterV8 ret = null;
        List<NewRecyclerViewHolder> mViewHolders = getViewHolders();
        for (NewRecyclerViewHolder mViewHolder : mViewHolders) {
            IPresenterV8 mPresenter = mViewHolder.getPresenter();
            if (mPresenter != null) {
                View mFocusView = mPresenter.holdFocusedView();
                if (mFocusView != null) {
                    ret = mPresenter;
                    break;
                }
            }

        }
        return ret;
    }

    public void setDebugMode(boolean debugMode) {
        this.debugMode = debugMode;
    }

    public void setNeedRefresh(boolean needRefresh) {
        mNeedRefresh = needRefresh;
    }


    public void setOnItemClickListener(OnItemClickListenerV8 listener) {
        this.mItemClickListener = listener;
    }


    public void setOnPanelExposureListener(OnPanelExposureListenerV8 listener){
        this.mPanelExposureListener = listener;
    }
    public void setOnBoundaryListener(OnBoundaryListenerV8 listener) {
        this.mBoundaryListener = listener;
    }


    @Override
    public int getItemViewType(int position) {
        return SupportUtilV8.getSupportViewType(mContainers.get(position).getConfirmType());
    }

    @Override
    public NewRecyclerViewHolder onCreateViewHolder(final ViewGroup parent, int viewType) {
        IPresenterV8 presenter = PresenterFactoryV8.getInstance().createPresenter(
                SupportUtilV8.getPresenterTypeByViewType(viewType), parent.getContext());
        presenter.setOnItemClickListener(mItemClickListener);
        presenter.setOnBoundaryListener(mBoundaryListener);
        presenter.setOnPanelExposureListener(mPanelExposureListener);
        View view = presenter.getView();
        presenter.setDebug(debugMode);
        ViewGroup.LayoutParams params = generateLayoutParams(parent);
        if (params != null) {
            view.setLayoutParams(params);
        }
        NewRecyclerViewHolder holder = new NewRecyclerViewHolder(view);
        mViewHolders.add(holder);
        holder.setPresenter(presenter);
        return holder;
    }

    @Override
    public void onBindViewHolder(NewRecyclerViewHolder holder, int position) {
        holder.getPresenter().setContainer(mContainers.get(position));
        holder.getPresenter().setOnItemClickListener(mItemClickListener);
        holder.getPresenter().setOnBoundaryListener(mBoundaryListener);
        holder.getPresenter().setOnPanelExposureListener(mPanelExposureListener);
        holder.getPresenter().setPosition(position);
        if (mNeedRefresh) {
            holder.refreshUI();
        }
    }

    @Override
    public int getItemCount() {
        return mContainers == null ? 0 : mContainers.size();
    }

    public ViewGroup.LayoutParams generateLayoutParams(ViewGroup parent) {
        if (parent instanceof RecyclerView)
            return new RecyclerView.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        if (parent instanceof FrameLayout)
            return new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        return null;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public void onViewRecycled(NewRecyclerViewHolder holder) {
        holder.getPresenter().onViewRecycled();
        mViewHolders.remove(holder);
    }

    public List<NewRecyclerViewHolder> getViewHolders() {
        return mViewHolders;
    }

    @Override
    public void onViewDetachedFromWindow(NewRecyclerViewHolder holder) {
        holder.getPresenter().onDetachedFromWindow();
    }

    @Override
    public void onViewAttachedToWindow(NewRecyclerViewHolder holder) {
        holder.getPresenter().onAttachedToWindow();
        if (!mViewHolders.contains(holder))
            mViewHolders.add(holder);
    }

    @Override
    public boolean onFailedToRecycleView(NewRecyclerViewHolder holder) {
        return true;
    }
}
