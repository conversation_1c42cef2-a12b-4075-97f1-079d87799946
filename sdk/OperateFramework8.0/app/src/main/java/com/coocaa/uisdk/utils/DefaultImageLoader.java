package com.coocaa.uisdk.utils;


import android.content.Context;
import android.graphics.ColorFilter;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.widget.ImageView;

import com.coocaa.uisdk.listener.IImageLoaderV8;
import com.coocaa.uisdk.listener.OnBitmapLoadListenerV8;
import com.coocaa.uisdk.listener.OnFinalCallback;

public class DefaultImageLoader implements IImageLoaderV8{


    @Override
    public void init(Context context) {

    }

    @Override
    public void setDebugMode(boolean debugMode) {

    }

    @Override
    public void destroy() {

    }

    @Override
    public View getView(Context context) {
        return null;
    }

    @Override
    public View getView(Context context, boolean original) {
        return null;
    }

    @Override
    public IImageLoaderV8 with(Context context) {
        return null;
    }

    @Override
    public IImageLoaderV8 setPlaceHolder(int id) {
        return null;
    }

    @Override
    public IImageLoaderV8 setPlaceHolder(Drawable drawable) {
        return null;
    }

    @Override
    public IImageLoaderV8 load(String url) {
        return null;
    }

    @Override
    public IImageLoaderV8 circle(CircleParamsV8 params) {
        return null;
    }

    @Override
    public IImageLoaderV8 resize(int width, int height) {
        return null;
    }

    @Override
    public IImageLoaderV8 showAnimation(boolean show) {
        return null;
    }

    @Override
    public IImageLoaderV8 showFade(boolean fade) {
        return null;
    }

    @Override
    public IImageLoaderV8 showFade(boolean fade, int duration) {
        return null;
    }

    @Override
    public void into(View view) {

    }

    @Override
    public IImageLoaderV8 setScaleType(ImageView.ScaleType scaleType) {
        return null;
    }

    @Override
    public IImageLoaderV8 wrapContent() {
        return null;
    }

    @Override
    public IImageLoaderV8 finalCallback(OnFinalCallback callback) {
        return null;
    }

    @Override
    public void getBitmap(OnBitmapLoadListenerV8 listener) {

    }

    @Override
    public void startAnimation(View view) {

    }

    @Override
    public void stopAnimation(View view) {

    }

    @Override
    public IImageLoaderV8 setLeftTopCorner(float corner) {
        return null;
    }

    @Override
    public IImageLoaderV8 setLeftBottomCorner(float corner) {
        return null;
    }

    @Override
    public IImageLoaderV8 setRightTopCorner(float corner) {
        return null;
    }

    @Override
    public IImageLoaderV8 setRightBottomCorner(float corner) {
        return null;
    }

    @Override
    public IImageLoaderV8 setColorFilter(ColorFilter cf) {
        return null;
    }

    @Override
    public void clearCacheFromMemory(String url) {

    }

    @Override
    public void reset(View view) {

    }

    @Override
    public void clearCache(String url) {

    }

    @Override
    public void updateThreadPriority(int priopity) {

    }

    @Override
    public IImageLoaderV8 setAnimLoopCount(int count) {
        return null;
    }

    @Override
    public Context getContext() {
        return null;
    }
}
