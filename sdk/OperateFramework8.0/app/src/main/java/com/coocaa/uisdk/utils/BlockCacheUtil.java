package com.coocaa.uisdk.utils;

import android.content.Context;
import android.support.v7.widget.RecyclerView;
import android.util.Log;
import android.util.SparseArray;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class BlockCacheUtil {
    public static HashMap<Context, SparseArray<List<RecyclerView.ViewHolder>>> mCache = new HashMap<>();
    public static final int CACHE_SIZE = 25;

    public static void createCache(Context context){
        if (mCache.get(context) == null) {
            Log.i("blockcache","create block cache of " + context.getClass().getName());
            mCache.put(context, new SparseArray<List<RecyclerView.ViewHolder>>());
        }
    }

    public static void destroyCache(Context context){
        if (mCache.containsKey(context)) {
            Log.i("blockcache", "destroy block cache of " + context.getClass().getName());
            mCache.remove(context);
        }
    }

    public static int size(Context context, int type){
        SparseArray<List<RecyclerView.ViewHolder>> array = mCache.get(context);
        if (array != null){
            List<RecyclerView.ViewHolder> list = array.get(type);
            if (list != null)
                return list.size();

        }
        return 0;
    }

    public static void put(Context context, int type, RecyclerView.ViewHolder holder){
        SparseArray<List<RecyclerView.ViewHolder>> array = mCache.get(context);
        if (array == null)
            return;
        List<RecyclerView.ViewHolder> list = array.get(type);
        if (list == null){
            list = new ArrayList<>();
            array.put(type,list);
        }
        if (list.size() < CACHE_SIZE)
            list.add(holder);
    }

    public static RecyclerView.ViewHolder get(Context context, int type){
        SparseArray<List<RecyclerView.ViewHolder>> array = mCache.get(context);
        if (array == null)
            return null;
        List<RecyclerView.ViewHolder> list = array.get(type);
        if (list == null || list.size() == 0){
            return null;
        }
        return list.remove(0);
    }

}
