package com.coocaa.uisdk.utils;


import com.coocaa.uisdk.listener.IImageLoaderV8;

public class ImageLoaderV8 {

    private static IImageLoaderV8 imageLoader = new DefaultImageLoader();

    public synchronized static void setImageLoader(IImageLoaderV8 loader) {
        imageLoader = loader;
    }

    public synchronized static IImageLoaderV8 getLoader(){
        return imageLoader;
    }

    public synchronized static void destroy(){
        if (imageLoader != null)
            imageLoader.destroy();
        imageLoader = null;
    }
}
