package com.coocaa.uisdk.expander.scroll;

import android.content.Context;
import android.graphics.Rect;
import android.support.v7.widget.OrientationHelper;
import android.support.v7.widget.RecyclerView;
import android.view.View;

import com.coocaa.uisdk.utils.UtilV8;

/**
 * @Author: yuzhan
 */
public class VerticalLayoutManagerV8 extends BaseLinearLayoutManagerV8 {

    private static int SCREEN_HEIGHT = UtilV8.Div(1080);



    private boolean isFirstScroll = true;

    public VerticalLayoutManagerV8(Context context) {
        super(context, OrientationHelper.VERTICAL);
    }

    public void setIsFirstScroll(boolean firstScroll) {
        isFirstScroll = firstScroll;
    }

    @Override
    protected int getExtraLayoutSpace(RecyclerView.State state) {
        return UtilV8.Div(200);
    }

    @Override
    public boolean requestChildRectangleOnScreen(RecyclerView parent, View child, Rect rect,
                                                 boolean immediate) {

        if (isFirstScroll) {
            isFirstScroll = false;
            return isFirstScroll;
        }

        final int parentTop = getPaddingTop();
        final int childTop = child.getTop() + rect.top - child.getScrollY() + rect.height() / 2;
        final int offScreenTop = Math.min(0, childTop - parentTop);
        int dy = offScreenTop != 0 ? offScreenTop : (childTop - parentTop);

        if (dy != 0) {
            int height = getHeight() < UtilV8.Div(350) ? getHeight() : SCREEN_HEIGHT;
            dy = dy - height / 2;
            if (dy == 0)
                return false;
            int firstPos = this.findFirstCompletelyVisibleItemPosition();
            if (dy < 0 && firstPos == 0) {
                //当前recyclerview 在顶部，不需要滚动
                return false;
            }
            if (immediate) {
                parent.scrollBy(0, dy);
            } else {
                parent.smoothScrollBy(0, dy);
            }
            return true;
        }
        return false;
    }

}
