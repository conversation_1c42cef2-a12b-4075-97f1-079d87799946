package com.coocaa.uisdk.presenter;

import android.content.Context;
import android.view.View;

import com.coocaa.uisdk.model.ContainerV8;
import com.coocaa.uisdk.view.CardLayout;

/**
 * 新主页 卡片
 */
public class CardPresenter extends PresenterV8 {
    protected CardLayout view ;

    public CardPresenter(Context context) {
        super(context);
        view = makeView(context);
    }


    @Override
    public View getView() {
        return view;
    }

    @Override
    public void setContainer(ContainerV8 container) {
        container.focusable = 1;
        super.setContainer(container);
    }
    @Override
    public boolean obtainFocus() {
        return view.obtainFocus();
    }

    @Override
    public View getTopFirstView() {
        if (view.isFocusable())
            return view;
        return null;
    }

    @Override
    public void onResume() {
    }

    @Override
    public void onPause() {
    }

    @Override
    public void onStop() {
    }

    @Override
    public void onDestroy() {
    }

    @Override
    public CardLayout makeView(Context context) {
        if (view == null)
            view =new CardLayout(context);
        return view;
    }

}
