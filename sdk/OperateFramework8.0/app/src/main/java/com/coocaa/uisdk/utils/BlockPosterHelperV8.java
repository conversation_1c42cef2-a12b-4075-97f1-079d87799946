package com.coocaa.uisdk.utils;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.coocaa.uisdk.R;
import com.coocaa.uisdk.listener.IImageLoaderV8;
import com.coocaa.uisdk.listener.OnFinalCallback;
import com.coocaa.uisdk.model.BlockV8;

import java.util.List;

public class BlockPosterHelperV8 {
    protected Context context;
    protected View mPostOneImageView;
    protected String mUrl = null;
    protected FrameLayout.LayoutParams mDefaulBgParams;
    protected CircleParamsV8 defaultCircleParams ;
    protected static Handler mMainThread = new Handler(Looper.getMainLooper());


    public BlockPosterHelperV8(Context context,View view) {
        this.context = context;
        mPostOneImageView = view;
        defaultCircleParams = new CircleParamsV8(context.getResources().getDimension(R.dimen.common_border_round_radius));
    }

    public View getPosterView() {
        return mPostOneImageView;
    }


    public void setPosterUrl(String url) {
        this.mUrl = url;
    }

    public void setPosterLayoutParams(FrameLayout.LayoutParams params) {
        mDefaulBgParams = params;
    }

    public void stopAnim() {

    }

    public void startAnim() {

    }

    public void setPosterCircleParams(CircleParamsV8 params) {
        this.defaultCircleParams = params;
    }

    public void loadPostOneImageView(OnFinalCallback callback) {
        if (TextUtils.isEmpty(mUrl))
            return ;
        if(mDefaulBgParams == null)
            return ;
        try {
            IImageLoaderV8 mLoader = ImageLoaderV8.getLoader();
            if (mLoader != null){
                if(defaultCircleParams != null) {
                    mLoader.with(context).load(mUrl).resize(mDefaulBgParams.width, mDefaulBgParams.height).circle(defaultCircleParams)
                            .setScaleType(ImageView.ScaleType.CENTER_CROP).finalCallback(callback).into(mPostOneImageView);
                } else {
                    mLoader.with(context).load(mUrl).resize(mDefaulBgParams.width, mDefaulBgParams.height)
                            .setScaleType(ImageView.ScaleType.CENTER_CROP).finalCallback(callback).into(mPostOneImageView);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void clear() {
        stopAnim();
        if (ImageLoaderV8.getLoader() == null)return;
        if(!TextUtils.isEmpty(mUrl))
            ImageLoaderV8.getLoader().clearCacheFromMemory(mUrl);
        if(mPostOneImageView != null)
            ImageLoaderV8.getLoader().reset(mPostOneImageView);
    }

    public String getPosterImg(BlockV8.BlockPoster poster) {
        if(poster == null)
            return null;
        return getPosterImg(poster.images);
    }

    public String getPosterImg(List<String> images) {
        if(images == null || images.size() == 0)
            return null;
        if(images.size() == 1)
            return images.get(0);
        for(String s : images) {//低端机固定静态图，找不到静态图才返回第一张图
            if(s.endsWith("png") || s.endsWith("jpg") || s.endsWith("jpeg"))
                return s;
        }
        return images.get(0);
    }
}
