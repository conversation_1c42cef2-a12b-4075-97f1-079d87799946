package com.coocaa.uisdk.utils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 17-1-9.
 */

public class CollectionUtil {
    public static boolean isEmpty(Collection collection){
        if (collection == null || collection.size() == 0)
            return true;
        return false;
    }

    public static <E> ArrayList<E> arrayList(){
        return new ArrayList<E>();
    }

    public static <E> List<E> syncList(){
        return Collections.synchronizedList(new ArrayList<E>());
    }

    public static <K,V> HashMap<K,V> hashMap(){
        return new HashMap<K,V>();
    }

    public static <V> HashSet<V> hashSet(){
        return new HashSet<V>();
    }
}
