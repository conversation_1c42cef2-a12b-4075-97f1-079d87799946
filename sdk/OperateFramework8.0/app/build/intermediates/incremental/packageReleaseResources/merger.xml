<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res"><file path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/drawable-hdpi/ic_launcher_background.xml" preprocessing="true" qualifiers="hdpi-v4"><generated-file path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/generated/res/pngs/release/drawable-hdpi/ic_launcher_background.png" qualifiers="hdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/generated/res/pngs/release/drawable-hdpi-v21/ic_launcher_background.xml" qualifiers="hdpi-v21" type="drawable"/></file></source><source path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/generated/res/rs/release"/><source path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res"><file name="item_bottom_in" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/anim/item_bottom_in.xml" qualifiers="" type="anim"/><file name="layout_up_anim" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/anim/layout_up_anim.xml" qualifiers="" type="anim"/><file name="layout_right_anim" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/anim/layout_right_anim.xml" qualifiers="" type="anim"/><file name="item_sild_from_right" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/anim/item_sild_from_right.xml" qualifiers="" type="anim"/><file name="layout_left_anim" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/anim/layout_left_anim.xml" qualifiers="" type="anim"/><file name="item_sild_from_left" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/anim/item_sild_from_left.xml" qualifiers="" type="anim"/><file name="focus_bg_rounded" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/drawable/focus_bg_rounded.xml" qualifiers="" type="drawable"/><file name="focus_bg" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/drawable/focus_bg.xml" qualifiers="" type="drawable"/><file name="block_default_bg" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/drawable/block_default_bg.xml" qualifiers="" type="drawable"/><file name="block_default_bg_color" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/color/block_default_bg_color.xml" qualifiers="" type="color"/><file path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/values/colors.xml" qualifiers=""><color name="colorPrimary">#6200EE</color><color name="colorPrimaryDark">#3700B3</color><color name="colorAccent">#03DAC5</color><color name="default_panel_title">#ffffff</color></file><file path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/values/dimens.xml" qualifiers=""><dimen name="px4">2dp</dimen><dimen name="common_border_width">3.34dp</dimen><dimen name="px20">13.33dp</dimen><dimen name="px16">12dp</dimen><dimen name="poster_radius">5.3dp</dimen><dimen name="common_border_round_radius">10.7dp</dimen><dimen name="px40">26.6dp</dimen><dimen name="block_default_bg_radius">10.67dp</dimen></file><file path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/values/styles.xml" qualifiers=""/><file path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/values/strings.xml" qualifiers=""><string name="app_name">UISDK</string></file><file name="focus_light" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/drawable-hdpi/focus_light.png" qualifiers="hdpi-v4" type="drawable"/></source><source path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/generated/res/rs/release"/><source path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/release/res"/></dataSet><mergedItems/></merger>