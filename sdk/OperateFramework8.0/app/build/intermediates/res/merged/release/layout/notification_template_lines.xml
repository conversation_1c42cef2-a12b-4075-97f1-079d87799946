<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2015 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingRight="8dp"
    android:paddingEnd="8dp"
    android:paddingTop="2dp"
    android:paddingBottom="2dp"
    >
    <LinearLayout
        android:id="@+id/line1"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:paddingTop="6dp"
        android:layout_marginLeft="8dp"
        android:layout_marginStart="8dp"
        android:orientation="horizontal"
        >
        <TextView android:id="@+id/title"
            android:textAppearance="@style/TextAppearance.StatusBar.EventContent.Title"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:ellipsize="marquee"
            android:fadingEdge="horizontal"
            android:layout_weight="1"
            />
        <include
            layout="@layout/notification_template_part_time"
            android:id="@+id/time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="0"
            android:visibility="gone"
            />
        <include
            layout="@layout/notification_template_part_chronometer"
            android:id="@+id/chronometer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="0"
            android:visibility="gone"
            />
    </LinearLayout>
    <TextView android:id="@+id/text2"
        android:textAppearance="@style/TextAppearance.StatusBar.EventContent.Line2"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="-2dp"
        android:layout_marginBottom="-2dp"
        android:layout_marginLeft="8dp"
        android:layout_marginStart="8dp"
        android:singleLine="true"
        android:fadingEdge="horizontal"
        android:ellipsize="marquee"
        android:visibility="gone"
        />
    <LinearLayout
        android:id="@+id/line3"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginLeft="8dp"
        android:layout_marginStart="8dp"
        >
        <TextView android:id="@+id/text"
            android:textAppearance="@style/TextAppearance.StatusBar.EventContent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_gravity="center"
            android:singleLine="true"
            android:ellipsize="marquee"
            android:fadingEdge="horizontal"
            />
        <TextView android:id="@+id/info"
            android:textAppearance="@style/TextAppearance.StatusBar.EventContent.Info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="0"
            android:singleLine="true"
            android:gravity="center"
            android:paddingLeft="8dp"
            android:paddingStart="8dp"
            />
    </LinearLayout>
</LinearLayout>