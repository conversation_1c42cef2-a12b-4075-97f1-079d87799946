1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.coocaa.uisdk"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="17"
8-->/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml
9        android:targetSdkVersion="29" />
9-->/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
11-->/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:5:5-79
11-->/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:5:22-76
12
13    <application
13-->/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:6:5-10:19
14        android:allowBackup="true"
14-->/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:7:9-35
15        android:supportsRtl="true" >
15-->/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:8:9-35
16    </application>
17
18</manifest>
