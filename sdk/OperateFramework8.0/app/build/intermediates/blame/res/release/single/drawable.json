[{"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_item_background_holo_dark.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_item_background_holo_dark.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_list_selector_background_transition_holo_dark.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_list_selector_background_transition_holo_dark.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_ic_menu_paste_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_ic_menu_paste_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_seekbar_track_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_seekbar_track_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_seekbar_thumb_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_seekbar_thumb_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_btn_checkbox_unchecked_mtrl.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_btn_checkbox_unchecked_mtrl.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_edit_text_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_edit_text_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_list_selector_holo_light.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_list_selector_holo_light.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_ratingbar_full_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_ratingbar_full_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_textfield_search_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_textfield_search_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_ic_menu_selectall_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_ic_menu_selectall_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_list_selector_holo_dark.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_list_selector_holo_dark.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_btn_default_mtrl_shape.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_btn_default_mtrl_shape.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_spinner_textfield_background_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_spinner_textfield_background_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/block_default_bg.xml", "source": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/drawable/block_default_bg.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_ic_voice_search_api_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_ic_voice_search_api_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_tab_indicator_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_tab_indicator_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_btn_radio_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_btn_radio_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_ic_menu_cut_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_ic_menu_cut_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_ratingbar_small_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_ratingbar_small_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_ic_menu_copy_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_ic_menu_copy_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/focus_bg_rounded.xml", "source": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/drawable/focus_bg_rounded.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_item_background_holo_light.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_item_background_holo_light.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_btn_colored_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_btn_colored_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_cab_background_internal_bg.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_cab_background_internal_bg.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_btn_radio_off_mtrl.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_btn_radio_off_mtrl.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_btn_checkbox_checked_mtrl.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_btn_checkbox_checked_mtrl.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_ic_menu_overflow_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_ic_menu_overflow_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_ic_menu_share_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_ic_menu_share_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_ratingbar_indicator_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_ratingbar_indicator_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_btn_radio_on_mtrl.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_btn_radio_on_mtrl.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_list_selector_background_transition_holo_light.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_list_selector_background_transition_holo_light.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_btn_borderless_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_btn_borderless_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_ic_go_search_api_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_ic_go_search_api_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/focus_bg.xml", "source": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/drawable/focus_bg.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_dialog_material_background_light.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_dialog_material_background_light.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_text_cursor_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_text_cursor_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_switch_thumb_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_switch_thumb_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_ic_ab_back_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_ic_ab_back_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_cab_background_top_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_cab_background_top_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_btn_check_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_btn_check_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_ic_clear_material.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_ic_clear_material.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/drawable/abc_dialog_material_background_dark.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/drawable/abc_dialog_material_background_dark.xml"}]