{"logs": [{"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/incremental/mergeReleaseResources/merged.dir/values-v14/values-v14.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-v14/values-v14.xml", "from": {"startLines": "2,7,8,9,15,21,22,23,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,331,402,485,895,1317,1437,1500,1654,1717", "endLines": "6,7,8,14,20,21,22,25,26,27", "endColumns": "12,70,82,12,12,119,62,12,62,131", "endOffsets": "326,397,480,890,1312,1432,1495,1649,1712,1844"}}]}, {"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/values-v14/values-v14.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-v14/values-v14.xml", "from": {"startLines": "2,7,8,9,15,21,22,23,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,331,402,485,895,1317,1437,1500,1654,1717", "endLines": "6,7,8,14,20,21,22,25,26,27", "endColumns": "12,70,82,12,12,119,62,12,62,131", "endOffsets": "326,397,480,890,1312,1432,1495,1649,1712,1844"}}]}]}