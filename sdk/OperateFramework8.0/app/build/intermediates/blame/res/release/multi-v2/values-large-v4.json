{"logs": [{"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/values-large-v4/values-large-v4.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-large-v4/values-large-v4.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,117,189,248,319,390,460,530,598,666,730,785,889", "endColumns": "61,71,58,70,70,69,69,67,67,63,54,103,115", "endOffsets": "112,184,243,314,385,455,525,593,661,725,780,884,1000"}}]}, {"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/incremental/mergeReleaseResources/merged.dir/values-large-v4/values-large-v4.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-large-v4/values-large-v4.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,117,189,248,319,390,460,530,598,666,730,785,889", "endColumns": "61,71,58,70,70,69,69,67,67,63,54,103,115", "endOffsets": "112,184,243,314,385,455,525,593,661,725,780,884,1000"}}]}]}