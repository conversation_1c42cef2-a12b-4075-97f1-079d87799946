[{"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/anim/item_sild_from_left.xml", "source": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/anim/item_sild_from_left.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/anim/abc_fade_out.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/anim/abc_fade_out.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/anim/layout_right_anim.xml", "source": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/anim/layout_right_anim.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/anim/abc_slide_in_bottom.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/anim/abc_slide_in_bottom.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/anim/abc_popup_enter.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/anim/abc_popup_enter.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/anim/abc_slide_in_top.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/anim/abc_slide_in_top.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/anim/abc_grow_fade_in_from_bottom.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/anim/abc_grow_fade_in_from_bottom.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/anim/abc_fade_in.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/anim/abc_fade_in.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/anim/item_sild_from_right.xml", "source": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/anim/item_sild_from_right.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/anim/abc_popup_exit.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/anim/abc_popup_exit.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/anim/abc_slide_out_bottom.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/anim/abc_slide_out_bottom.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/anim/layout_up_anim.xml", "source": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/anim/layout_up_anim.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/anim/item_bottom_in.xml", "source": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/anim/item_bottom_in.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/anim/abc_shrink_fade_out_from_bottom.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/anim/abc_shrink_fade_out_from_bottom.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/anim/layout_left_anim.xml", "source": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/res/anim/layout_left_anim.xml"}, {"merged": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/anim/abc_slide_out_top.xml", "source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/anim/abc_slide_out_top.xml"}]