{"logs": [{"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,380,473,573,655,752,860,936,1012,1090,1186,1282,1377,1474,1569,1667,1780,1874", "endColumns": "94,106,122,92,99,81,96,107,75,75,77,95,95,94,96,94,97,112,93,101", "endOffsets": "145,252,375,468,568,650,747,855,931,1007,1085,1181,1277,1372,1469,1564,1662,1775,1869,1971"}}]}, {"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/incremental/mergeReleaseResources/merged.dir/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,380,473,573,655,752,860,936,1012,1090,1186,1282,1377,1474,1569,1667,1780,1874", "endColumns": "94,106,122,92,99,81,96,107,75,75,77,95,95,94,96,94,97,112,93,101", "endOffsets": "145,252,375,468,568,650,747,855,931,1007,1085,1181,1277,1372,1469,1564,1662,1775,1869,1971"}}]}]}