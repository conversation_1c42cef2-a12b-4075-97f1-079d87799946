{"logs": [{"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/incremental/mergeReleaseResources/merged.dir/values-nl/values-nl.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-nl/values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,281,404,509,616,702,810,930,1007,1084,1166,1277,1381,1480,1595,1708,1811,1929,2032", "endColumns": "117,107,122,104,106,85,107,119,76,76,81,110,103,98,114,112,102,117,102,100", "endOffsets": "168,276,399,504,611,697,805,925,1002,1079,1161,1272,1376,1475,1590,1703,1806,1924,2027,2128"}}]}, {"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/values-nl/values-nl.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-nl/values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,281,404,509,616,702,810,930,1007,1084,1166,1277,1381,1480,1595,1708,1811,1929,2032", "endColumns": "117,107,122,104,106,85,107,119,76,76,81,110,103,98,114,112,102,117,102,100", "endOffsets": "168,276,399,504,611,697,805,925,1002,1079,1161,1272,1376,1475,1590,1703,1806,1924,2027,2128"}}]}]}