{"logs": [{"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/incremental/mergeReleaseResources/merged.dir/values-hy-rAM/values-hy-rAM.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-hy-rAM/values-hy-rAM.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,158,266,389,490,600,690,796,911,993,1074,1159,1266,1373,1472,1582,1689,1790,1910,2009", "endColumns": "102,107,122,100,109,89,105,114,81,80,84,106,106,98,109,106,100,119,98,100", "endOffsets": "153,261,384,485,595,685,791,906,988,1069,1154,1261,1368,1467,1577,1684,1785,1905,2004,2105"}}]}, {"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/values-hy-rAM/values-hy-rAM.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-hy-rAM/values-hy-rAM.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,158,266,389,490,600,690,796,911,993,1074,1159,1266,1373,1472,1582,1689,1790,1910,2009", "endColumns": "102,107,122,100,109,89,105,114,81,80,84,106,106,98,109,106,100,119,98,100", "endOffsets": "153,261,384,485,595,685,791,906,988,1069,1154,1261,1368,1467,1577,1684,1785,1905,2004,2105"}}]}]}