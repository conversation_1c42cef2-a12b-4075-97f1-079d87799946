{"logs": [{"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/incremental/mergeReleaseResources/merged.dir/values-v23/values-v23.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-v23/values-v23.xml", "from": {"startLines": "2,3,4,5,6,19,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1277,2079,2184,2299,2406", "endLines": "2,3,4,5,18,31,32,33,34,35", "endColumns": "134,134,74,86,12,12,104,114,106,112", "endOffsets": "185,320,395,482,1272,2074,2179,2294,2401,2514"}}]}, {"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/values-v23/values-v23.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-v23/values-v23.xml", "from": {"startLines": "2,3,4,5,6,19,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1277,2079,2184,2299,2406", "endLines": "2,3,4,5,18,31,32,33,34,35", "endColumns": "134,134,74,86,12,12,104,114,106,112", "endOffsets": "185,320,395,482,1272,2074,2179,2294,2401,2514"}}]}]}