{"logs": [{"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/values-iw/values-iw.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,269,395,494,602,686,786,900,978,1056,1135,1237,1341,1437,1540,1641,1741,1857,1953", "endColumns": "103,109,125,98,107,83,99,113,77,77,78,101,103,95,102,100,99,115,95,102", "endOffsets": "154,264,390,489,597,681,781,895,973,1051,1130,1232,1336,1432,1535,1636,1736,1852,1948,2051"}}]}, {"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/incremental/mergeReleaseResources/merged.dir/values-iw/values-iw.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,269,395,494,602,686,786,900,978,1056,1135,1237,1341,1437,1540,1641,1741,1857,1953", "endColumns": "103,109,125,98,107,83,99,113,77,77,78,101,103,95,102,100,99,115,95,102", "endOffsets": "154,264,390,489,597,681,781,895,973,1051,1130,1232,1336,1432,1535,1636,1736,1852,1948,2051"}}]}]}