{"logs": [{"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/incremental/mergeReleaseResources/merged.dir/values-v12/values-v12.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-v12/values-v12.xml", "from": {"startLines": "2,5,8,9", "startColumns": "4,4,4,4", "startOffsets": "55,279,479,598", "endLines": "4,7,8,9", "endColumns": "12,12,118,94", "endOffsets": "274,474,593,688"}}]}, {"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/values-v12/values-v12.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-v12/values-v12.xml", "from": {"startLines": "2,5,8,9", "startColumns": "4,4,4,4", "startOffsets": "55,279,479,598", "endLines": "4,7,8,9", "endColumns": "12,12,118,94", "endOffsets": "274,474,593,688"}}]}]}