{"logs": [{"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/values-my-rMM/values-my-rMM.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-my-rMM/values-my-rMM.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,272,397,502,619,712,824,960,1038,1117,1203,1328,1440,1542,1668,1779,1889,2012,2112", "endColumns": "108,107,124,104,116,92,111,135,77,78,85,124,111,101,125,110,109,122,99,100", "endOffsets": "159,267,392,497,614,707,819,955,1033,1112,1198,1323,1435,1537,1663,1774,1884,2007,2107,2208"}}]}, {"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/incremental/mergeReleaseResources/merged.dir/values-my-rMM/values-my-rMM.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-my-rMM/values-my-rMM.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,272,397,502,619,712,824,960,1038,1117,1203,1328,1440,1542,1668,1779,1889,2012,2112", "endColumns": "108,107,124,104,116,92,111,135,77,78,85,124,111,101,125,110,109,122,99,100", "endOffsets": "159,267,392,497,614,707,819,955,1033,1112,1198,1323,1435,1537,1663,1774,1884,2007,2107,2208"}}]}]}