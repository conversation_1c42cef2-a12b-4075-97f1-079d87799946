{"logs": [{"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/values-ml-rIN/values-ml-rIN.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-ml-rIN/values-ml-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,282,405,523,639,732,837,969,1046,1122,1204,1315,1421,1519,1633,1734,1845,1973,2074", "endColumns": "118,107,122,117,115,92,104,131,76,75,81,110,105,97,113,100,110,127,100,100", "endOffsets": "169,277,400,518,634,727,832,964,1041,1117,1199,1310,1416,1514,1628,1729,1840,1968,2069,2170"}}]}, {"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/incremental/mergeReleaseResources/merged.dir/values-ml-rIN/values-ml-rIN.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-ml-rIN/values-ml-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,282,405,523,639,732,837,969,1046,1122,1204,1315,1421,1519,1633,1734,1845,1973,2074", "endColumns": "118,107,122,117,115,92,104,131,76,75,81,110,105,97,113,100,110,127,100,100", "endOffsets": "169,277,400,518,634,727,832,964,1041,1117,1199,1310,1416,1514,1628,1729,1840,1968,2069,2170"}}]}]}