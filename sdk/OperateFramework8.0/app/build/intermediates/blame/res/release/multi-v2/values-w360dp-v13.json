{"logs": [{"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/incremental/mergeReleaseResources/merged.dir/values-w360dp-v13/values-w360dp-v13.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-w360dp-v13/values-w360dp-v13.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "54", "endOffsets": "105"}}]}, {"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/values-w360dp-v13/values-w360dp-v13.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-w360dp-v13/values-w360dp-v13.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "54", "endOffsets": "105"}}]}]}