{"logs": [{"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/values-v17/values-v17.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-v17/values-v17.xml", "from": {"startLines": "2,5,9,12,15,18,22,26,29,32,35,39,42,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,456,614,764,936,1164,1406,1577,1751,1920,2193,2393,2597", "endLines": "4,8,11,14,17,21,25,28,31,34,38,41,45,49", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "223,451,609,759,931,1159,1401,1572,1746,1915,2188,2388,2592,2921"}}]}, {"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/incremental/mergeReleaseResources/merged.dir/values-v17/values-v17.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-v17/values-v17.xml", "from": {"startLines": "2,5,9,12,15,18,22,26,29,32,35,39,42,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,456,614,764,936,1164,1406,1577,1751,1920,2193,2393,2597", "endLines": "4,8,11,14,17,21,25,28,31,34,38,41,45,49", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "223,451,609,759,931,1159,1401,1572,1746,1915,2188,2388,2592,2921"}}]}]}