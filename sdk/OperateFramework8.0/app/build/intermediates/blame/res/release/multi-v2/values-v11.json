{"logs": [{"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/incremental/mergeReleaseResources/merged.dir/values-v11/values-v11.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-v11/values-v11.xml", "from": {"startLines": "2,8,14,20,26,32,38,39,40,45,50,52,54,55,56,107,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,474,899,1327,1753,2180,2605,2694,2795,3203,3623,3730,3859,3930,4013,7721,11569", "endLines": "7,13,19,25,31,37,38,39,44,49,51,53,54,55,106,158,159", "endColumns": "12,12,12,12,12,12,88,100,12,12,12,12,70,82,12,12,90", "endOffsets": "469,894,1322,1748,2175,2600,2689,2790,3198,3618,3725,3854,3925,4008,7716,11564,11655"}}]}, {"outputFile": "/Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/build/intermediates/res/merged/release/values-v11/values-v11.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/00078a54f066072c7b34bb8eb064e224/appcompat-v7-23.2.0/res/values-v11/values-v11.xml", "from": {"startLines": "2,8,14,20,26,32,38,39,40,45,50,52,54,55,56,107,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,474,899,1327,1753,2180,2605,2694,2795,3203,3623,3730,3859,3930,4013,7721,11569", "endLines": "7,13,19,25,31,37,38,39,44,49,51,53,54,55,106,158,159", "endColumns": "12,12,12,12,12,12,88,100,12,12,12,12,70,82,12,12,90", "endOffsets": "469,894,1322,1748,2175,2600,2689,2790,3198,3618,3725,3854,3925,4008,7716,11564,11655"}}]}]}