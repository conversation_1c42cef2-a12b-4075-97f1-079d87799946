-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:2:1-12:12
INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:2:1-12:12
INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:2:1-12:12
INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:2:1-12:12
INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:2:1-12:12
INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:2:1-12:12
INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:2:1-12:12
	package
		ADDED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:3:5-31
		INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:2:1-12:12
		INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml
	android:versionCode
		INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:2:1-12:12
		INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:2:11-69
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:5:5-79
	android:name
		ADDED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:5:22-76
application
ADDED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:6:5-10:19
	android:supportsRtl
		ADDED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:8:9-35
	android:allowBackup
		ADDED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml:7:9-35
uses-sdk
INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/iot/NewTV_SmartHome/sdk/OperateFramework8.0/app/src/main/AndroidManifest.xml
