package com.ccos.tvlauncher.sdk.video;

import android.support.annotation.StringDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * @Author: yuzhan
 */
@Retention(RetentionPolicy.SOURCE)
@StringDef({VideoControlDetailType.thumbnail, VideoControlDetailType.title, VideoControlDetailType.sub_title, VideoControlDetailType.detail,
        VideoControlDetailType.img_label, VideoControlDetailType.text_label, VideoControlDetailType.btn1, VideoControlDetailType.btn2,
        VideoControlDetailType.price, VideoControlDetailType.sec_kill_price})
public @interface VideoControlDetailType {
    String thumbnail = "thumbnail"; //缩略图
    String title = "title"; //标题
    String sub_title = "sub_title"; //子标题
    String detail = "detail"; //简介
    String img_label = "img_label"; //图片标签
    String text_label = "text_label"; //文字标签
    String btn1 = "btn1"; //按钮1
    String btn2 = "btn2"; //按钮2
    String price = "price"; //原价
    String sec_kill_price = "sec_kill_price"; //秒杀价
}
