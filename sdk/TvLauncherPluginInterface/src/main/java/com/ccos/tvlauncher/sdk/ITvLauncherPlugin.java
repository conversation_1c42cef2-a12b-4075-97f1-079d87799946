package com.ccos.tvlauncher.sdk;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.View;

import com.ccos.tvlauncher.sdk.extension.ITvLauncherPluginPermissionCallback;

import java.io.Serializable;
import java.util.Map;


/**
 * @Author: yuzhan
 */
public interface ITvLauncherPlugin extends Serializable {

    /***
     * 设置Context
     * @param pluginContext 插件Context
     */
    void setContext(Context pluginContext);

    /**
     * 设置header，如果header内容有更新（如账户切换），会再次调用此方法
     * @param header
     */
    void setHeader(Map<String, String> header);

    void setNeedContentView(boolean need);

    /**
     *
     * @param header
     * @param changedKey
     */
    void onHeaderChanged(Map<String, String> header, String changedKey);

    /**
     * 初始化参数设置完毕
     */
    void onParamsReady();

    /**
     * 初始化，在setContext、setThreadGroup、setParams、setBoundaryCallback、setPluginCallback之后调用
     * 可以在这里做异步数据加载等逻辑
     */
    void onInit();

    /**
     * 内容区域状态变化（展开/收缩）
     */
    void onShortcutStateChanged(@ShortcutState int state);

    /**
     * 获取插件名称，主页tab中显示该名称
     * @return
     */
    String getTitle();

    void setPluginCallback(TvLauncherPluginCallback callback);

    /**
     * 回调
     * @param callback
     */
    void setBoundaryCallback(TvLauncherPluginBoundaryCallback callback);

    /**
     * 设置参数
     * @param params
     */
    void setParams(TvLauncherPluginParams params);

    void setFocusColor(int color);

    /**
     * 内容content数据（tab上面屏幕中间的View），只有数据是插件自定义View时，才会调用此方法.<br />
     * View创建成功后，通过{@link TvLauncherPluginCallback#onContentViewCreated(ITvLauncherPlugin, View)}方法回调
     * @param contentData
     */
    void setContentData(PluginContentData contentData);

    /**
     * 快捷页面json数据（tab上面屏幕底部的View）.<br />
     * View创建成功后，通过{@link TvLauncherPluginCallback#onContentViewCreated(ITvLauncherPlugin, View)}方法回调
     * @param shortcutStr
     */
    void setShortcutData(String shortcutStr);

    /**
     * content数据刷新，只会在onInit执行完之后调用；
     * @param contentData
     */
    void onContentDataChanged(PluginContentData contentData);

    /**
     * shortcut数据刷新，只会在onInit执行完之后调用；
     * @param shortcutStr
     */
    void onShortcutDataChanged(String shortcutStr);

    void setPermissionCallback(ITvLauncherPluginPermissionCallback callback);

    /**
     * 插件内容UI获取焦点（主页按下键时调用）
     * @return
     */
    boolean shortcutObtainFocus();

    boolean contentObtainFocus();

    /**
     * 获取指定页面的profile view（描述影片/海报信息的UI），通过{@link TvLauncherPluginCallback#onProfileViewCreated(ITvLauncherPlugin, View, int) 方法回调}
     * @param position 对应contentData中的List<PluginVideoData> 下标
     * @return
     */
    void createProfileView(PluginVideoData videoData, int position);

    boolean profileObtainFocus(View view, int index);

    /**
     * 主页切换tab到该版面，或者从其他页面返回到主页当前页面触发
     */
    void onShow();

    /**
     * 主页切换tab离开该版面，切换到其他版面，或者进入其他页面触发
     */
    void onHide();

    /**
     * 主页页面切换onShow，动画执行完毕后，回调
     */
    void onIdleShow();

    /**
     * 快捷UI滚动到初始位置
     */
    void resetShortcutScrollState();

    /**
     * 内容滚动到初始位置
     */
    void resetContentScrollState();

    /**
     * 主页在该版面onResume
     */
    void onResume();

    /**
     * 主页在该版面onPause
     */
    void onPause();

    /**
     * 主页在该版面onStop
     */
    void onStop();

    void onNewIntent(Intent intent);

    /**
     * 主页生命周期onDestroy
     */
    void onDestroy();

    /**
     * 主页生命周期onConfigurationChanged
     * @param newConfig
     */
    void onConfigurationChanged(Configuration newConfig);

    void onSystemTimeChanged();

    /**
     * 收到系统通知onLowMemory
     */
    void onLowMemory();

    /**
     * 请求权限回调，通过 {@link ITvLauncherPluginPermissionCallback#pluginRequestPermissions(ITvLauncherPlugin, String[], int) 方法来请求权限}
     * @param requestCode
     * @param permissions
     * @param grantResults
     */
    void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults);

    /**
     * 提供其他进程通过主页通知插件，传数据的能力
     * @param bundle
     */
    void onDeliverPluginMessage(Bundle bundle);

    /**
     * 提供调用一些系统API的能力
     * @param connector
     */
    void setCcConnector(IPluginConnector connector);

    /**
     * 主页页面切换时候，设置切换来自的方向；
     * @param direction
     */
    void setPageChangeDirection(@IPageChangeDirection int direction);

    /**
     * 日志采集productId，空表示提交到主页下
     * @return
     */
    String productId();
}
