package com.ccos.tvlauncher.sdk;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.View;

import com.ccos.tvlauncher.sdk.extension.ITvLauncherPluginPermissionCallback;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: yuzhan
 */
public abstract class BaseTvLauncherPlugin implements ITvLauncherPlugin {
    protected Context pluginContext;
    protected TvLauncherPluginParams params;
    protected TvLauncherPluginCallback callback;
    protected TvLauncherPluginBoundaryCallback boundaryCallback;
    protected Map<String, String> header = new ConcurrentHashMap<>();
    protected PluginContentData pluginContentData;
    protected String shortcutJsonString;
    protected ITvLauncherPluginPermissionCallback permissionCallback;
    protected IPluginConnector connector;
    protected boolean needContentView;

    @Override
    public final void setContext(Context pluginContext) {
        this.pluginContext = pluginContext;
    }

    @Override
    public void onShortcutStateChanged(@ShortcutState int state) {

    }

    @Override
    public boolean contentObtainFocus() {
        return false;
    }

    @Override
    public void createProfileView(PluginVideoData videoData, int position) {

    }

    @Override
    public void setNeedContentView(boolean need) {
        this.needContentView = need;
    }

    @Override
    public boolean profileObtainFocus(View view, int index) {
        return false;
    }

    @Override
    public void setContentData(PluginContentData contentData) {
        this.pluginContentData = contentData;
    }

    @Override
    public void setShortcutData(String shortcutStr) {
        this.shortcutJsonString = shortcutStr;
    }

    @Override
    public void onContentDataChanged(PluginContentData contentData) {

    }

    @Override
    public void onShortcutDataChanged(String shortcutStr) {

    }

    @Override
    public final void setPermissionCallback(ITvLauncherPluginPermissionCallback callback) {
        this.permissionCallback = callback;
    }

    @Override
    public void resetContentScrollState() {

    }

    @Override
    public void setFocusColor(int color) {

    }

    @Override
    public final void setParams(TvLauncherPluginParams params) {
        this.params = params;
    }

    @Override
    public void onParamsReady() {
        
    }

    @Override
    public final void setHeader(Map<String, String> header) {
        if(header != null) {
            this.header.putAll(header);
        }
    }

    @Override
    public void onHeaderChanged(Map<String, String> header, String changedKey) {
        if(header != null && header.get(changedKey) != null) {
            this.header.put(changedKey, header.get(changedKey));
        }
    }

    @Override
    public void resetShortcutScrollState() {

    }

    @Override
    public boolean shortcutObtainFocus() {
        return false;
    }

    @Override
    public String getTitle() {
        return null;
    }

    @Override
    public void setPluginCallback(TvLauncherPluginCallback callback) {
        this.callback = callback;
    }

    @Override
    public void setBoundaryCallback(TvLauncherPluginBoundaryCallback callback) {
        this.boundaryCallback = callback;
    }

    @Override
    public abstract void onInit();

    @Override
    public void onNewIntent(Intent intent) {

    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {

    }

    @Override
    public void onSystemTimeChanged() {

    }

    @Override
    public void onLowMemory() {

    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {

    }

    @Override
    public abstract void onShow();

    @Override
    public void onIdleShow() {

    }

    @Override
    public abstract void onHide();

    @Override
    public void onResume() {

    }

    @Override
    public void onPause() {

    }

    @Override
    public void onStop() {

    }

    @Override
    public abstract void onDestroy();

    @Override
    public void onDeliverPluginMessage(Bundle bundle) {

    }

    @Override
    public void setCcConnector(IPluginConnector connector) {
        this.connector = connector;
    }

    @Override
    public String productId() {
        return null;
    }

    @Override
    public void setPageChangeDirection(int direction) {

    }
}
