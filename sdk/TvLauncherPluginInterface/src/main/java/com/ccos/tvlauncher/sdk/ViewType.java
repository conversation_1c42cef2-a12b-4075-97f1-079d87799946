package com.ccos.tvlauncher.sdk;

import android.support.annotation.StringDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

import static com.ccos.tvlauncher.sdk.ViewType.CUSTOM_VIEW;
import static com.ccos.tvlauncher.sdk.ViewType.STANDARD_VIDEO_FLOW;
import static com.ccos.tvlauncher.sdk.ViewType.STANDARD_VIDEO_PROFILE;

@Retention(RetentionPolicy.SOURCE)
@StringDef({CUSTOM_VIEW, STANDARD_VIDEO_PROFILE, STANDARD_VIDEO_FLOW})
public @interface ViewType {

    /**
     * 插件自定义UI
     */
    String CUSTOM_VIEW = "Normal";

    /**
     * 背景视频/图片，前面有图文介绍的UI
     */
    String STANDARD_VIDEO_PROFILE = "StreamL";

    /**
     * 全视频/图片流
     */
    String STANDARD_VIDEO_FLOW = "StreamB";

    /**
     * 自定义全屏
     */
    String FULL_SCREEN_CUSTOM_VIEW = "Plugin";


    /**
     * 全屏海报
     */
    String POSTER = "Poster";
}
