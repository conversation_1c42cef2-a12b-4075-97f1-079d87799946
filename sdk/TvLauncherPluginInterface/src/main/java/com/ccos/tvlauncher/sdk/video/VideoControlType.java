package com.ccos.tvlauncher.sdk.video;

import android.support.annotation.StringDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * @Author: yuzhan
 */
@Retention(RetentionPolicy.SOURCE)
@StringDef({VideoControlType.poster, VideoControlType.text, VideoControlType.label, VideoControlType.button})
public @interface VideoControlType {
    String poster = "poster"; //海报组件，如：缩略图
    String text = "text"; //文字控件
    String label = "label"; //标签控件
    String button = "button"; //按钮控件
}
