package com.ccos.tvlauncher.sdk.video;

import android.view.View;

import com.ccos.tvlauncher.sdk.PluginVideoData;

/**
 * @Author: yuzhan
 */
public interface IVideoProfile {

    View createView(PluginVideoData data, int position);

    /**
     * 复用UI
     * @param videoData
     */
    void refresh(PluginVideoData videoData, int position);

    boolean obtainFocus(int position);

    void onHide();

    void onShow();

    void onDestroy();
}
