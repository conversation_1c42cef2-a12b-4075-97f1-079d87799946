package com.ccos.tvlauncher.sdk.extension;

import android.content.Context;
import android.view.View;

import com.ccos.tvlauncher.sdk.IPluginConnector;

import java.util.Map;

/**
 * @Author: yuzhan
 */
public interface ITvLauncherVoicePlugin {

    /**
     * 设置Context
     * @param hostContext
     * @param pluginContext
     */
    void setContext(Context hostContext, Context pluginContext);

    /**
     * 设置Header
     * @param header
     */
    void setHeader(Map<String, String> header);

    /**
     * 设置完参数后，调用初始化
     */
    void onInit();

    /**
     * 获取VoiceView
     * @return
     */
    View getVoiceView();

    /**
     * 主页页面切换变化
     * @param pageName 页面名称，如今日/影视/短视频等， 参考{@link IPageDefine}定义
     * @param pkgName page的插件包名，如果pageName是UNKNOWN的时（pageName会动态拓展，会新增一些之前不认识的page），可用pkgName做一些逻辑
     */
    void onPageChanged(String pageName, String pkgName);

    /**
     * 显示后回调，开始动画、加载图片等
     */
    void onShow();

    /**
     * 隐藏回调，停止动画、回收内存、释放资源等
     */
    void onHide();

    void onDestroy();

    /**
     * 提供调用一些系统API的能力
     * @param connector
     */
    void setCcConnector(IPluginConnector connector);
}
