package com.ccos.tvlauncher.sdk;

import android.support.annotation.IntDef;

import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

import static com.ccos.tvlauncher.sdk.ShortcutState.SHORTCUT_EXPAND;
import static com.ccos.tvlauncher.sdk.ShortcutState.SHORTCUT_SHRINK;

@Retention(RetentionPolicy.SOURCE)
@IntDef({SHORTCUT_EXPAND, SHORTCUT_SHRINK})
public @interface ShortcutState {
    /**
     * 内容展开到全屏
     */
    int SHORTCUT_EXPAND = 1;
    /**
     * 内容收缩到底部初始位置
     */
    int SHORTCUT_SHRINK = 2;
}
