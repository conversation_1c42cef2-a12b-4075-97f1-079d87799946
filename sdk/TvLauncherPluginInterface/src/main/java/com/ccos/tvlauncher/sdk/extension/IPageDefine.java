package com.ccos.tvlauncher.sdk.extension;

import android.support.annotation.StringDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * tab页面名称定义，注意，由于tab页面支持动态添加，可能会有新增类型，会超出定义范围（比如新增游戏tab、戏曲tab），需要用到的业务自行处理
 */
@Retention(RetentionPolicy.SOURCE)
@StringDef({IPageDefine.TODAY, IPageDefine.MOVIE, IPageDefine.SHORT_VIDEO,
        IPageDefine.APP_STORE, IPageDefine.EDUCATE, IPageDefine.AIOT,
        IPageDefine.SHOPPING_MALL, IPageDefine.SYSTEM, IPageDefine.TV, IPageDefine.UNKNOWN})
public @interface IPageDefine {
    //今日
    String TODAY = "TODAY";
    //影视
    String MOVIE = "MOVIE";
    //短视频
    String SHORT_VIDEO = "SHORT_VIDEO";
    //应用
    String APP_STORE = "APP_STORE";
    //教育
    String EDUCATE = "EDUCATE";
    //智慧家庭AIOT
    String AIOT = "AIOT";
    //购物
    String SHOPPING_MALL = "SHOPPING_MALL";
    //系统
    String SYSTEM = "SYSTEM";
    //TV
    String TV = "TV";

    //拓展，未知
    String UNKNOWN = "UNKNOWN";

    //信息发布
    String INFORMATION_DELIVERY = "INFORMATION_DELIVERY";
}
