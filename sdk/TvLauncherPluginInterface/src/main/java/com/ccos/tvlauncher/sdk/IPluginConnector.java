package com.ccos.tvlauncher.sdk;

import android.database.Cursor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Author: yuzhan
 */
public interface IPluginConnector extends Serializable {

    User user();
    Executor executor();
    Logger logger();
    boolean debugMode();
    int homeVersion();
    Iot iot();
    Vip vip();
    Pcfg pcfg();
    Network network();
    DB db();

    interface UserChangeListener extends Serializable {
        void onUserChanged();
    }

    interface User extends Serializable {
        class UserInfo implements Serializable {
            public String token;
            public Map<String, Object> info;
        }

        UserInfo getUserInfo();

        boolean hasLogin();

        /**
         * 账户登录
         * @param params
         * @param from 业务来源，特殊日志采集需要，一般不需要该值，传null即可
         * @return
         */
        boolean login(Map<String, String> params, boolean needFinish, String from);

        void addUserChangeListener(UserChangeListener listener);

        void removeUserChangeListener(UserChangeListener listener);
    }

    interface Executor extends java.util.concurrent.Executor, Serializable {
        void execute(Runnable runnable);
        void execute(Runnable runnable, long delay);
    }

    interface Logger extends Serializable {
        void pageResumeEvent(String pageName, Map<String, String> params);

        void pagePausedEvent(String pageName, Map<String, String> params);

        void pageFailEvent(String pageName, String result, int errorCode);

        void pageCustomEvent(String eventId, Map<String, String> params);

        /**
         * 提交日志事件
         * @param eventId
         * @param params
         */
        void baseEvent(String eventId, Map<String, String> params);

        /**
         * 同步提交日志事件
         * @param eventId
         * @param params
         */
        void baseEventSync(String eventId, Map<String, String> params);


        void submitBaseEventWithPolicy(String eventId, Map<String, String> params, int policyTime, int policyMaxLine);
    }

    interface IotListener extends Serializable {
        void onAccessTokenChanged(String accessToken);
    }

    interface Iot extends Serializable {
        String accessToken();
        void addListener(IotListener listener);
        void removeListener(IotListener listener);
    }

    interface IVipChangeListener extends Serializable {
        void onVipInfoChanged(String data);
    }

    interface Vip extends Serializable {
        boolean isVip(String sourceSign);
        String getVipInfo(String sourceSign);
        String getAllVipInfo();
        void addVipChangeListener(IVipChangeListener listener);
        void removeVipChangeListener(IVipChangeListener listener);
    }

    interface IPcfgListener extends Serializable {
        void onMovieSourceChanged(String movieSource, String movieLicense);
        void onServerConfigChanged(Map<String, List<String>> newServerConfigMap);
    }

    interface Pcfg extends Serializable {
        String getMovieSource();
        String getMovieLicense();
        Map<String, List<String>> getServerConfig();
        void addPcfgListener(IPcfgListener listener);
        void removePcfgListener(IPcfgListener listener);
    }

    interface Network extends Serializable {
        void showConnectNet();
    }

    interface DB extends Serializable {
        Cursor query(String tableName);
    }
}
