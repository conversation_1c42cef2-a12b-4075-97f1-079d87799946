package com.ccos.tvlauncher.sdk.extension;

import android.content.Context;
import android.view.View;

import com.ccos.tvlauncher.sdk.IPluginConnector;

import java.util.Map;

/**
 * @Author: yuzhan
 */
public abstract class BaseTvLauncherVoicePlugin implements ITvLauncherVoicePlugin {

    protected Context hostContext;
    protected Context pluginContext;
    protected Map<String, String> headers;
    protected IPluginConnector connector;

    @Override
    public void setContext(Context hostContext, Context pluginContext) {
        this.pluginContext = pluginContext;
        this.hostContext = hostContext;
    }

    @Override
    public void setHeader(Map<String, String> header) {
        this.headers = header;
    }

    @Override
    public void onInit() {

    }

    @Override
    public View getVoiceView() {
        return null;
    }

    @Override
    public void onPageChanged(String pageName, String pkgName) {

    }

    @Override
    public void onShow() {

    }

    @Override
    public void onHide() {

    }

    @Override
    public void onDestroy() {

    }

    @Override
    public void setCcConnector(IPluginConnector connector) {
        this.connector = connector;
    }
}
