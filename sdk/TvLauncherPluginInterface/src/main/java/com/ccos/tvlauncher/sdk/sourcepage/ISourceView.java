package com.ccos.tvlauncher.sdk.sourcepage;

import android.view.View;
import android.widget.FrameLayout;

public interface ISourceView {
    void setRightContentView(View view, FrameLayout.LayoutParams params);

    void setVisable(boolean visable);

    void dismissDialog();

    View getMainLayout();

    /**
     *
     * @param unread
     * @return
     */
    void setNaviItemStatus(int index,boolean unread);

    View getNaviItem(int index);

    void changePage(int index);

    void startVoicePluginAnim();

    void stopVoicePluginAnim();

    void startSecondaryPageAnim(int currPage,int index);

    void stopSecondaryPageAnim(boolean resetFocus);

    int getCurrIndex();

    String getStartSourceFrom();
}
