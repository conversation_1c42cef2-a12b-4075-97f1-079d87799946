package com.ccos.tvlauncher.sdk.sourcepage;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.CallSuper;
import android.support.annotation.WorkerThread;
import android.text.TextUtils;
import android.util.Log;
import android.util.TimingLogger;
import android.view.View;

import com.ccos.tvlauncher.sdk.IPluginConnector;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executor;

public abstract class DefaultSourcePlugin implements ISourcePlugin {
    protected Context mPluginContext;
    protected View mContenteView;
    protected int position;
    protected ISourceView mISourceView;
    protected Executor ioExecutor;
    protected Handler uiHandler = new Handler(Looper.getMainLooper());
    protected OnSourceBoundaryListener callback;
    protected SourcePagePluginParams mExtraParams;
    protected IPluginConnector connector;
    protected int focusColor;
    protected static final int EXPOSURE_TIME = 1500;
    private static final String[] pageNames = new String[]{"智能设备", "快捷", "信号源", "应用", "消息"};
    protected String pageName;
    protected Map<String, String> exposureMap = new HashMap<>();
    protected Map<String, String> clickMap = new HashMap<>();
    // 当前界面是否显示
    protected boolean isShow;
    protected TimingLogger mTimingLogger = new TimingLogger("CCSourcePlugin", getClass().getName());

    @Override
    public void setContext(Context pluginContext) {
        mPluginContext = pluginContext;
    }

    @Override
    public void setPosition(int position) {
        this.position = position;
    }

    public void setExecutor(Executor ioExecutor) {
        this.ioExecutor = ioExecutor;
    }

    @Override
    public void setPluginBoundaryCallback(OnSourceBoundaryListener callback) {
        this.callback = callback;
    }

    @Override
    public void setHeader(Map<String, String> header) {

    }

    @WorkerThread
    @CallSuper
    public void onInit() {
        Log.e("CCSource", getClass().getName() + " >>>  " + position);
        mTimingLogger.addSplit("onInit start");
        if (mContenteView == null) {
            mTimingLogger.addSplit("makeContentView start");
            mContenteView = makeContentView();
            mTimingLogger.addSplit("makeContentView finish");
        }
        mTimingLogger.addSplit("onInit finish ");
        mTimingLogger.dumpToLog();
    }

    @Override
    public void setParams(SourcePagePluginParams params) {
        mExtraParams = params;
    }

    @Override
    public View getContentView() {
        return mContenteView;
    }

    @Override
    public void setISourceView(ISourceView iView) {
        this.mISourceView = iView;
    }

    public abstract View makeContentView();


    @Override
    public boolean obtainFocus() {
        return false;
    }

    private void buildClickMap() {
        String parent_from = mISourceView.getStartSourceFrom();
        if (mISourceView != null && !TextUtils.isEmpty(parent_from)
                && !clickMap.containsKey("parent_page_name")) {
            clickMap.put("parent_page_name", parent_from);
        }
        if (TextUtils.isEmpty(pageName)) {
            pageName = "信号源";
        }
        if (!clickMap.containsKey("signal_page_name"))
            clickMap.put("signal_page_name", pageName);
    }

    @Override
    public void onShow() {
        isShow = true;
        try {
            pageName = pageNames[position];
            if (exposureMap.isEmpty()) {
                exposureMap.put("page_name", pageName);
            }
            buildClickMap();
        } catch (Exception e) {
            e.printStackTrace();
        }
        uiHandler.removeCallbacks(exposureRunnable);
        uiHandler.postDelayed(exposureRunnable, EXPOSURE_TIME);
        obtainFocus();
    }

    Runnable exposureRunnable = new Runnable() {
        @Override
        public void run() {
            submitExposureEvent(exposureMap);
        }
    };


    protected void submitExposureEvent(Map<String, String> map) {
        submitLog("signal_page_show", map);
    }

    protected void submitClickEvent(Map<String, String> map) {
        submitLog("signal_block_clicked", map);
    }

    protected void submitLog(String eventId, Map<String, String> map) {
//        try {
//            Log.e("kkk", "click map > " + map);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        if (connector != null) {
            connector.logger().baseEvent(eventId, map);
        }
    }


    @Override
    public void onHide() {
        isShow = false;
        uiHandler.removeCallbacks(exposureRunnable);
        resetContentScrollState();
    }

    @Override
    public void resetContentScrollState() {

    }

    @Override
    public void onResume() {

    }

    @Override
    public void onPause() {

    }

    @Override
    public void onStop() {

    }

    @Override
    public void onNewIntent(Intent intent) {

    }

    @Override
    public void onDestroy() {

    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {

    }

    @Override
    public void onLowMemory() {

    }

    @Override
    public void setCcConnector(IPluginConnector connector) {
        this.connector = connector;
    }

    @Override
    public void resetFocusIndex(int index) {

    }

    @Override
    public void onDeliverPluginMessage(Bundle bundle) {

    }

    @Override
    public String productId() {
        return null;
    }

    @Override
    public void setFocusColor(int color) {
        this.focusColor = color;
    }

    @Override
    public void setMargins(int l, int t, int r, int b) {

    }
}
