package com.ccos.tvlauncher.sdk;

import android.view.View;

/**
 * @Author: yuzhan
 */
public interface TvLauncherPluginCallback {
    /**
     * 插件内容View创建回调
     * @param plugin
     * @param contentView
     */
    void onContentViewCreated(ITvLauncherPlugin plugin, View contentView);

    /**
     * 插件内容View创建回调
     * @param plugin
     * @param shortcutView
     */
    void onShortcutViewCreated(ITvLauncherPlugin plugin, View shortcutView);

    /**
     * 插件内容View创建回调，带有额外参数
     * @param plugin
     * @param shortcutView
     * @param previewTopMargin 预览收缩状态时，需要预留的间距，一般内部如果有top padding的时候，传top padding过来
     * @param expandTopMargin
     */
    void onShortcutViewCreated(ITvLauncherPlugin plugin, View shortcutView, int previewTopMargin, int expandTopMargin);

    /**
     * 插件视频描述View创建回调
     * @param plugin
     * @param profileView
     * @param index
     */
    void onProfileViewCreated(ITvLauncherPlugin plugin, View profileView, int index);

    /**
     * 插件视频描述View创建回调，带有额外参数
     * @param plugin
     * @param profileView
     * @param index
     */
    void onProfileViewCreated(ITvLauncherPlugin plugin, View profileView, int index, int x, int y);
}
