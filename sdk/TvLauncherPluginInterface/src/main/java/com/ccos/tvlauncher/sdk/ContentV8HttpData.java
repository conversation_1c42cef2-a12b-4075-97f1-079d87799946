package com.ccos.tvlauncher.sdk;

import java.io.Serializable;

/**
 * @Author: yuzhan
 */
public class ContentV8HttpData implements Serializable {
    public String tab;
    public ContentExtra extra;
    public int total;
    public String type;
    public String md5;

    public transient boolean isCache = false;

    public static class ContentExtra implements Serializable {
        public String bg;
        public String bg_color;//版面背景色，16进制格式，#开头，如：#12FE1C
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("ContentV8HttpData{");
        sb.append("tab='").append(tab).append('\'');
        sb.append(", extra=").append(extra);
        sb.append(", total=").append(total);
        sb.append(", type='").append(type).append('\'');
        sb.append(", md5='").append(md5).append('\'');
        sb.append(", isCache=").append(isCache);
        sb.append('}');
        return sb.toString();
    }
}
