package com.ccos.tvlauncher.sdk.sourcepage;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.View;

import com.ccos.tvlauncher.sdk.IPluginConnector;

import java.io.Serializable;
import java.util.Map;
import java.util.concurrent.Executor;


public interface ISourcePlugin extends Serializable {


    void setContext(Context pluginContext);


    void onInit();


    void setPosition(int position);


    void setHeader(Map<String, String> header);
    /**
     * 回调
     *
     * @param callback
     */
    void setPluginBoundaryCallback(OnSourceBoundaryListener callback);
//
//    /**
//     * 设置参数
//     * @param params
//     */
    void setParams(SourcePagePluginParams params);


    View getContentView();

    /**
     * 预留接口，获取父View
     * @param iView
     */
    void setISourceView(ISourceView iView);

    /**
     * 子线程使用的线程池
     * @param ioExecutor
     */
    void setExecutor(Executor ioExecutor);


    boolean obtainFocus();

    /**
     * 主页切换tab到该版面，或者从其他页面返回到主页当前页面触发
     */
    void onShow();

    /**
     * 主页切换tab离开该版面，切换到其他版面，或者进入其他页面触发
     */
    void onHide();

    @Deprecated
    void resetContentScrollState();

    @Deprecated
    void onResume();

    @Deprecated
    void onPause();

    @Deprecated
    void onStop();

    @Deprecated
    void onNewIntent(Intent intent);

    @Deprecated
    void onDestroy();

    @Deprecated
    void onConfigurationChanged(Configuration newConfig);

    @Deprecated
    void onLowMemory();

    /**
     * 提供调用一些系统API的能力
     * @param connector
     */
    void setCcConnector(IPluginConnector connector);

    void resetFocusIndex(int index);

    /**
     * 提供其他进程通过主页通知插件，传数据的能力
     * @param bundle
     */
    void onDeliverPluginMessage(Bundle bundle);

    String productId();

    void setFocusColor(int color);
    //
    void setMargins(int l,int t,int r,int b);

}
