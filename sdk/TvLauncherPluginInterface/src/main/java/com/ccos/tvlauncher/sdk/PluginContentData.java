package com.ccos.tvlauncher.sdk;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: yuzhan
 */
public class PluginContentData implements Serializable {
    //默认背景海报图url
    public String bg;
    public String bg_color;

    public volatile @ViewType String viewType;

    //内容json数据
    public volatile String content;
    public volatile String videoString;
    //content json 反序列化后的对象，需要自行反序列化，主页默认不做反序列化动作
    public volatile Object contentObject;

    public volatile int contentTotal = 0;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("@PluginContentData={content=")
          .append(content);
        sb.append(", viewType=").append(viewType);
        sb.append(", contentObject=[");
        sb.append(contentObject == null ? "null" : contentObject.toString());
        sb.append(", hashCode=").append(this.hashCode());
        sb.append("]}");
        return sb.toString();
    }
}
