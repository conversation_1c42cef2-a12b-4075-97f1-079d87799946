package com.ccos.tvlauncher.sdk;

import android.text.TextUtils;
import android.util.Log;

import com.ccos.tvlauncher.sdk.video.VideoControlDetailType;
import com.ccos.tvlauncher.sdk.video.VideoControlType;
import com.ccos.tvlauncher.sdk.video.VideoStyle;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: yuzhan
 */
public class PluginVideoData implements Serializable {
    public int id;//唯一标志
    //背景海报图url
    public String bg;

    public String title;

    public int auto_play = 1; //是否自动起播，0-否，1-是

    public VideoDataMediaInfo media_info;

    public @VideoStyle String style;

    public List<VideoControlData> control;

    public transient @ViewType String viewType;//动态赋值，非后台数据带有的字段
    public transient int index; //动态赋值，在flow中的位置；
    public transient String playUrl; //播放地址
    public transient String playOwner; //是自己CDN，还是第三方
    public transient long updateTime; //播放地址刷新时间
    public transient String updateDate;
    public transient long duration;//视频时长，单位秒

    public transient Map<String, VideoControlData> parsedControlDataMap;

    @Deprecated
    public String subTitle;

    public void parseControlData() {
        if(parsedControlDataMap == null) {
            if(control != null) {
                parsedControlDataMap = new HashMap<>(control.size());
                for(VideoControlData data : control) {
                    if(TextUtils.isEmpty(data.type))
                        continue;
                    parsedControlDataMap.put(data.type, data);
                    data.parseDetailData();
                }
//                Log.d("test", "new control data map = " + this.hashCode() + ", videoData=" + this.hashCode());
            }
        }
        if(parsedControlDataMap == null) {
            parsedControlDataMap = new HashMap<>();
//            Log.d("test", "new >> empty control data map = " + this.hashCode() + ", videoData=" + this.hashCode());
        }
    }

    public static class VideoDataMediaInfo implements Serializable {
        public String vId;//剧集id
        public String mId;//单集id
        public String score;//评分

        //电视-1,
        //电影-4,
        //综艺-5，
        //教育只有数据-30，
        //教育第三方-11，
        //少儿-3,
        //记录片-9,
        //动漫-2
        public int category;//视频分类，

        @Override
        public String toString() {
            final StringBuffer sb = new StringBuffer("MediaInfo{");
            sb.append("vId='").append(vId).append('\'');
            sb.append(", mId='").append(mId).append('\'');
            sb.append(", score='").append(score).append('\'');
            sb.append(", category=").append(category);
            sb.append('}');
            return sb.toString();
        }
    }

    public static class VideoControlData implements Serializable {
        public @VideoControlType String type; //UI控件类型
        public List<VideoControlDetailData> data; //json字符串，当插件类型为custom，需要有值, 为该插件的内容数据

        public transient Map<String, List<VideoControlDetailData>> parsedDetailDataMap;

        public void parseDetailData() {
            if(parsedDetailDataMap == null) {
                if(data != null && !data.isEmpty()) {
                    parsedDetailDataMap = new HashMap<>();
                    for(VideoControlDetailData detailData : data) {
                        if(TextUtils.isEmpty(detailData.sub_type))
                            continue;
                        if(parsedDetailDataMap.get(detailData.sub_type) != null) {
                            parsedDetailDataMap.get(detailData.sub_type).add(detailData);
                        } else {
                            List<VideoControlDetailData> detailDataList = new ArrayList<>();
                            detailDataList.add(detailData);
                            parsedDetailDataMap.put(detailData.sub_type, detailDataList);
                        }

                    }
                }
            }
            if(parsedDetailDataMap == null) {
                parsedDetailDataMap = new HashMap<>();
            }
        }

        @Override
        public String toString() {
            final StringBuffer sb = new StringBuffer("VideoControlData{");
            sb.append("type='").append(type).append('\'');
            sb.append(", data=").append(data);
            sb.append('}');
            return sb.toString();
        }
    }

    public static class VideoControlDetailData implements Serializable {
        public @VideoControlDetailType String sub_type; //控件类型
        public String text; //文字
        public String img_url; //海报url
        public String onclick; //点击事件
        public String click_type; //点击类型，normal-标准，custom-自定义
        public Object parsedOnClickData;

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("VideoControlDetailData{");
            sb.append("sub_type='").append(sub_type).append('\'');
            sb.append(", text='").append(text).append('\'');
            sb.append(", img_url='").append(img_url).append('\'');
            sb.append(", onclick='").append(onclick).append('\'');
            sb.append(", click_type='").append(click_type).append('\'');
            sb.append('}');
            return sb.toString();
        }
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("PluginVideoData{");
        sb.append("id=").append(id);
        sb.append(", bg='").append(bg).append('\'');
        sb.append(", index=").append(index);
        sb.append(", title='").append(title).append('\'');
        sb.append(", updateDate='").append(updateDate).append('\'');
        sb.append(", playUrl='").append(playUrl).append('\'');
        sb.append(", auto_play=").append(auto_play);
        sb.append(", viewType='").append(viewType).append('\'');
        sb.append(", media_info=").append(media_info);
        sb.append(", style='").append(style).append('\'');
        sb.append(", control=").append(control);
        sb.append('}');
        return sb.toString();
    }


    public String toPlayerString() {
        final StringBuffer sb = new StringBuffer("PlayVideoData{");
        sb.append("id=").append(id).append(", title='").append(title)
            .append(", viewType='").append(viewType).append(", playUrl=").append(playUrl)
            .append(", updateDate='").append(updateDate).append('\'')
            .append(", owner='").append(playOwner).append(", media_info=").append(media_info).append(", hashCode=").append(hashCode()).append('}');
        return sb.toString();
    }
}
