apply plugin: 'java'

dependencies {
    compileOnly files(getAndroidJar())
    compileOnly "com.android.support:support-annotations:23.2.0"
}

String getAndroidJar() {
    def path = getSDKPath(-1)
    def androidPath = "${path}${File.separator}android.jar"
    return androidPath
}

String getSDKPath(int version) {
    def android_home = System.getenv()['ANDROID_HOME']

    if (android_home == null || android_home.equals(""))
        return 'not found android.jar'
    def compileSdkVersion
    if (version == -1)
        compileSdkVersion = 'android-23'
    else
        compileSdkVersion = 'android-' + version
    def path = "${android_home}${File.separator}platforms${File.separator}${compileSdkVersion}"
    println("<CCBuild>getSDKPath : " + path + '@' + compileSdkVersion)
    return path
}

sourceCompatibility = "7"
targetCompatibility = "7"
