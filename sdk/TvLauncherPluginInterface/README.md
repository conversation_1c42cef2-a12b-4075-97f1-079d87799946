# 新TV主页插件接口对接文档

[TOC]

# 1、插件形式

​    插件打包成apk，插件可以单独启动，显示自己的UI页面，也可以作为插件形式，将某个UI显示在主页中，如果在主页中有多个版面，就可以创建多个插件入口类，每个类里面返回对应的UI

# 2、对接步骤

​    对接步骤有如下几步：

1. 插件工程依赖本接口工程interface（必须是compileOnly），见下面工程依赖部分
2. 插件实现接口类（或者通过集成自基类实现），见下面接口API说明部分
3. 插件工程的AndroidManifest.xml里面通过meta-data方式添加接口实现类注册，见下面插件工程注册部分

# 3、插件工程配置

​      由于插件接口工程必须要compileOnly，所以插件接口工程只能是java library，需要插件工程有一些必要的配置来保证编译通过，具体如下：

##   3.1、插件工程需要的一些变量定义

​       1、电脑需要添加ANDROID_HOME的环境变量配置，正常安装Android SDK后都会有此配置，可以自行检查，Gradle Sync 或者Run事后，插件接口工程也会打印该配置，如果是null，表示没读到或者没配置对，会找不到Android相关方法，就会报错。

##   3.2、插件工程的引入

​     插件作为独立的APP工程，需要在build.gradle中添加对本接口工程的依赖

```
dependencies {
    ...
    compileOnly project(':tvlauncher_plugin_interface')
}
```

​    记得先添加工程到指定路径

```
include ':tvlauncher_plugin_interface'
project(':tvlauncher_plugin_interface').projectDir = getProjectDir('TvLauncherPluginInterface')
注意，如果是直接在根目录 settings.gradle中添加，可能会找不到 getProjectDir方法，就可以改成直接用file的方式，如下(修正为your path)：
project(':tvlauncher_plugin_interface').projectDir = new File('TvLauncherPluginInterface')
```

注：后续可能会将接口转到maven中，目前demo联调可以继续使用本工程

##   3.3、插件工程的混淆

​    如果插件工程需要混淆，那么需要在插件工程的proguard-rules.pro中添加如下keep保护：

```
-dontwarn com.ccos.tvlauncher.sdk.**
-keep class com.ccos.tvlauncher.sdk.** {*;}
-keep class * implements java.io.Serializable {*;}
-keep class android.content.res.**
-keep class * extends java.lang.annotation.Annotation { *; }
-keep interface * extends java.lang.annotation.Annotation { *; }

-keep class * implements com.ccos.tvlauncher.sdk.ITvLauncherPlugin {*;}
-keep class * extends com.ccos.tvlauncher.sdk.BaseTvLauncherPlugin {*;}
-keep class * implements com.ccos.tvlauncher.sdk.IPluginConnector {*;}
-keep class * implements com.ccos.tvlauncher.sdk.TvLauncherPluginCallback {*;}
-keep class * implements com.ccos.tvlauncher.sdk.TvLauncherPluginBoundaryCallback {*;}
```



# 4、接口API说明

## 4.1、接口API

### 4.1.1 ITvLauncherPlugin

| 接口(参数)                                                   | 接口用途                                                     | 必须 | 注意事项                                                     |
| ------------------------------------------------------------ | ------------------------------------------------------------ | ---- | ------------------------------------------------------------ |
| setContext(Context pluginContext)                            | 设置插件的context                                            | 是   | 实现类中需自己保存context                                    |
| void setThreadGroup(ThreadGroup threadGroup)                 | 设置插件ThreadGroup                                          | 否   | 建议插件的线程，都使用该threadGroup                          |
| setParams(HomeTabPluginParams params)                        | 设置参数                                                     | 否   |                                                              |
| HomeTabPluginParams getParams()                              | 获取设置的参数                                               | 否   |                                                              |
| setHeader(Map<String, String> header)                        | 设置header                                                   | 否   | 插件可以使用宿主的header，也可以添加/修改参数;               |
| void onHeaderChanged(Map<String, String> header, String changedKey) | header发生变化                                               | 否   | header内容发生变化，如账户切换等；                           |
| setPluginCallback(TvLauncherPluginCallbackcallback)          | 设置功能逻辑回调                                             | 是   |                                                              |
| setPluginBoundaryCallback(TvLauncherPluginBoundaryCallback boundaryCallback） | 设置插件边缘回调                                             | 是   |                                                              |
| setPermissionCallback(ITvLauncherPluginPermissionCallback callback) | 设置插件可以用来请求permission的回调                         | 否   | 插件如有请求permission的需求，可通过permissionCallback的方法来请求； |
| setContentData(PluginContentData contentData)                | 设置插件内容数据，在onInit中创建UI，通过TvLauncherPluginCallback的onContentViewCreated方法回调内容回来 | 是   | 数据定义中的contentObject是null；content是后台json数据，需要插件自己序列化成自己业务插件的数据结构，特别是插件图文类型内容的； |
| setShortcutData(String shortcutStr)                          | 设置插件快捷页面内容数据，在onInit中创建UI，通过TvLauncherPluginCallback的onShortcutViewCreated方法回调内容回来 | 是   | 主页从后台获取到快捷页面json数据，传给插件                   |
| onInit()                                                     | 插件设置参数完毕，创建content view、shortcut view，回调回来； | 是   | 主页是在上面各种set参数调用后，才会调用此方法；              |
| getTitle()                                                   | 获取插件名称                                                 | 否   | 预留，目前没什么用                                           |
| void createProfileView(int index)                            | 创建视频带简介的View，index是多页数据中的index，每一页创建单独的View，通过回调的onProfileViewCreated来返回； | 是   | 只有内容区UI是频带简介的，才需要实现此方法；                 |
| boolean contentObtainFocus()                                 | 内容UI获取焦点                                               | 否   | 图文类的，主页tab按上键，会调用此方法；                      |
| boolean shortcutObtainFocus()                                | 快捷UI获取焦点                                               | 是   | 主页tab按了下键，会调用此方法，并展开快捷UI                  |
| boolean profileObtainFocus(View view, int index)             | 视频带简介的UI获取焦点，View和index是createProfileView回调的视频带简介View和index,插件可以通过View或者index来定位； | 否   | 只有内容区UI是频带简介的，才需要实现此方法；                 |
| onShortcutStateChanged(@ShortcutState int state)             | 快捷UI展开/收缩状态变化；                                    | 否   | 快捷UI展开/收缩时候，动态的显示/隐藏第一行标题；             |
| onShow()                                                     | 切换tab版面，该插件页面显示出来                              | 是   | 可以做海报图展示/动画展示等操作                              |
| onHide()                                                     | 切换tab版面，离开该插件页面                                  | 是   | 可以做海报图回收/动画停止等动作                              |
| resetShortcutScrollState()                                   | 快捷UI滚动到初始位置                                         | 否   | UI如果超过一屏，需要滚回到顶部                               |
| resetContentScrollState                                      | 内容UI滚动到初始位置                                         | 否   | UI如果超过一屏，需要滚回到顶部                               |
| onResume                                                     | 生命周期方法                                                 | 否   |                                                              |
| onPause                                                      | 生命周期方法                                                 | 否   |                                                              |
| onStop                                                       | 生命周期方法                                                 | 否   |                                                              |
| onDestroy                                                    | 主页onDestroy，插件做回收内存、释放资源等动作                | 是   |                                                              |
| onNewIntent                                                  | 生命周期方法                                                 | 否   |                                                              |
| onSaveInstanceState                                          | 生命周期方法                                                 |      |                                                              |
| onSystemTimeChanged                                          | 系统时间变化                                                 |      |                                                              |
| onConfigurationChanged                                       | 主页页面onConfigurationChanged时候触发（如横竖屏切换等）     | 否   |                                                              |
| onLowMemory                                                  | 主页收到系统通知内存不足时触发，版面做内存回收动作           | 否   |                                                              |
| onRequestPermissionsResult                                   | 插件requestPermission的结果，通过此方法传给插件              | 否   | 插件如需要请求权限的话，需要通过permissionCallback来请求，此方法返回请求结果； |

### 4.1.2  插件回调 TvLauncherPluginCallback

| 回调方法                                                     | 用途                                                         | 是否必须 | 备注 |
| ------------------------------------------------------------ | ------------------------------------------------------------ | -------- | ---- |
| onContentViewCreated(ITvLauncherPlugin plugin, View contentView) | 插件内容UI创建回调                                           | 是       |      |
| onShortcutViewCreated(ITvLauncherPlugin plugin, View shortcutView) | 插件快捷UI创建回调；                                         | 是       |      |
| onShortcutViewCreated(ITvLauncherPlugin plugin, View shortcutView, int previewTopMargin, int expandTopMargin) | 插件快捷UI创建回调，带有指定预览/展开状态的顶部间距需求；如果插件内部UI有设置padding top，传该值过来即可； | 否       |      |
| onProfileViewCreated(ITvLauncherPlugin plugin, View profileView, int index) | 视频带简介UI创建回调，主页提供的容器是全屏的                 | 否       |      |
| onProfileViewCreated(ITvLauncherPlugin plugin, View profileView, int index, int x, int y) | 视频带简介UI创建回调，指定起始x,y坐标；                      | 否       |      |
|                                                              |                                                              |          |      |
|                                                              |                                                              |          |      |



### 4.1.4 插件边缘回调 TvLauncherPluginBoundaryCallback

| 回调方法                                                     | 用途                     | 是否必须 | 备注                                                  |
| ------------------------------------------------------------ | ------------------------ | -------- | ----------------------------------------------------- |
| onPluginContentTopBoundary(ITvLauncherPlugin plugin)         | 内容UI，顶部边缘按上键   | 否       | 插件可以自行实现边缘动画，通过回调回来主页控制tab显示 |
| onPluginContentDownBoundary(ITvLauncherPlugin plugin)        | 内容UI，底部边缘按下键   | 是       | 插件可以自行实现边缘动画                              |
| onPluginContentLeftBoundary(ITvLauncherPlugin plugin)        | 内容UI，左侧边缘         | 否       | 插件可以自行实现边缘动画                              |
| onPluginContentRightBoundary(IHomeTabPlugin plugin)          | 内容UI，右侧边缘         | 否       | 可以自行实现边缘动画                                  |
| onPluginContentBack(ITvLauncherPlugin plugin)                | 内容UI按返回键           | 是       | 通过回调回来主页控制tab显示                           |
|                                                              |                          |          |                                                       |
| onPluginShortcutTopBoundary(ITvLauncherPlugin plugin)        | 快捷UI                   | 是       |                                                       |
| onPluginShortcutDownBoundary(ITvLauncherPlugin plugin)       |                          | 否       |                                                       |
| onPluginShortcutLeftBoundary(ITvLauncherPlugin plugin)       |                          | 否       |                                                       |
| onPluginShortcutRightBoundary(ITvLauncherPlugin plugin)      |                          | 否       |                                                       |
| onPluginShortcutBack(ITvLauncherPlugin plugin)               |                          | 是       |                                                       |
|                                                              |                          |          |                                                       |
| onPluginProfileTopBoundary(ITvLauncherPlugin plugin, int index) | 视频带简介UI，顶部按上键 | 否       |                                                       |
| onPluginProfileDownBoundary(ITvLauncherPlugin plugin, int index) |                          | 是       |                                                       |
| onPluginProfileLeftBoundary(ITvLauncherPlugin plugin, int index) |                          | 是       |                                                       |
| onPluginProfileRightBoundary(ITvLauncherPlugin plugin, int index) |                          | 是       |                                                       |
| onPluginProfileBack(ITvLauncherPlugin plugin, int index)     |                          | 是       |                                                       |
|                                                              |                          |          |                                                       |



## 4.1、快速对接

​    快速简单对接只需要继承自基类（com.ccos.tvlauncher.sdk.BaseTvLauncherPlugin），实现几个必须接口或者override必要的接口即可；如：

图文插件示例：

```
public class DemoHomePluginImpl extends BaseTvLauncherPlugin {
    private MyPluginView contentView;
    private MyShortcutView shortcutView;

    @Override
    public void onInit() {
        initContentView();//创建内容View
        callback.onContentViewCreated(DemoHomePluginImpl.this, contentView);//回调回来
        initShortcutView();
        callback.onShortcutViewCreated(DemoHomePluginImpl.this, shortcutView, Util.Div(40), Util.Div(40));
    }

    private void initContentView() {
        if (contentView == null) {
            //创建自己的内容View，并设置一些回调、数据
            contentView = new MyPluginView(pluginContext);
            contentView.setCallback(new IKeyListenerImpl(boundaryCallback,this));
            contentView.setData(pluginContentData);
        }
    }
    
   
    
    @Override
    public boolean contentObtainFocus() {//让内容UI获焦
        return contentView!=null && contentView.obtainFocus();
    }
    
    
    private void initShortcutView() {
        if (shortcutView == null) {
            //创建自己的内容View，并设置一些回调、数据
            shortcutView = new MyShortcutView(pluginContext);
            shortcutView.setCallback(new IKeyListenerImpl(boundaryCallback,this));
            shortcutView.setData(shortcutJsonString);
        }
    }
    
    @Override
    public boolean shortcutObtainFocus() {
        return shortcutView!=null && shortcutView.obtainFocus();
    }

    @Override
    public void onShow() {
        super.onShow();
        if(contentView != null) {
            contentView.onShow();//加载海报图
        }
    }

    @Override
    public void onHide() {
        super.onHide();
        if(contentView != null) {
            contentView.onHide();//回收海报图
        }
    }

    @Override
    public void onResume() {
        if(demoView != null)
            demoView.onShow();
    }

    @Override
    public void onStop() {
        if(demoView != null)
            demoView.onHide();
    }

    @Override
    public void onDestroy() {
        //销毁
    }

    protected ContentListener contentListener = new ContentListener() {
        @Override
        public boolean onContentTopBoundary() {
            if(boundaryCallback != null) {
                return boundaryCallback.onPluginContentTopBoundary(MyPlugin.this);
            }
            return false;
        }

        @Override
        public boolean onContentDownBoundary() {
            return false;
        }

        @Override
        public boolean onContentLeftBoundary() {
            if(boundaryCallback != null) {
                return boundaryCallback.onPluginContentLeftBoundary(MyPlugin.this);
            }
            return false;
        }

        @Override
        public boolean onContentRightBoundary() {
            if(boundaryCallback != null) {
                return boundaryCallback.onPluginContentRightBoundary(MyPlugin.this);
            }
            return false;
        }

        @Override
        public boolean onContentBack() {
            if(boundaryCallback != null) {
                return boundaryCallback.onPluginContentBack(MyPlugin.this);
            }
            return false;
        }
    };

   
```



## 4.2、插件初始化

​    插件作为独立APP时，会在自己的Application中做一些初始化动作，比如下面是插件Application代码的示例：

```
插件原本MyApplication中的代码：

@Override
protected void attachBaseContext(Context base) {
    super.attachBaseContext(base);
}

@Override
public void onCreate() {
    super.onCreate();
    MyApplication.applicationContext = this;

    MyHeader.getInstance().initHeader(this);
    Util.instence(this);
    ImageLoader.getLoader().init(this);

    initLogSDK();
    initADSDK();
    initDeviceInfo();
    ...
    this.registerReceiver(new GlobalReceiver(), filter);
    ...
}
```

​     当插件在主页框架中加载的时候，是不会走到Application初始化的，所以需要插件在自己的plugin impl实现类中，补充做一些必要的初始化动作，必要是指只有插件UI页面需要的动作，不是插件UI页面需要的都不需要处理，因为点击跳转页面后就会拉起插件APP的进程，会走到插件Application的初始化。

​     插件UI在主页框架中的时候是在主页进程里的，插件点击跳转到自己的Activity页面后，就在插件自己的APP进程里了。如下是上面的插件实现类增加了必要的初始化逻辑后的示例。

```
public class MyPlugin extends BasePadLauncherPlugin {

    

    private void onInit() {
        //注意，该方法也是子线程中调用的
        MyApplication.applicationContext = pluginContext.getApplicationContext();

        MyHeader.getInstance().initHeader(pluginContext.getApplicationContext());
        Util.instence(pluginContext.getApplicationContext());
        ImageLoader.getLoader().init(pluginContext.getApplicationContext());

        initLogSDK();
        
        ...其他逻辑
    }

    ...
}
```

​	

## 4.3、详细对接

​    下面的版面客制化了一些逻辑需求，所以对接了额外接口，如：onShow时加载图片、onHide时回收图片，快速对接中有一些相关代码，这里就不详细描述了；

# 5、插件注册生效步骤

​    经过上面几个步骤后，已经完成了插件接口的实现，在打包插件工程前，需要将实现的插件类在AndroidManifest.xml中注册添加，因为主页框架是通过扫描meta-data来寻找实现类的，找不到，就无法加载该插件。

​    添加方法如下，修改插件工程的 AndroidManifest.xml，添加如下 meta-data

```
<application...>
        ...
        <meta-data
             android:name="TV_LAUNCHER_PLUGIN"
             android:value="com.xxx.yyy.zzz.MyPlugin">
        </meta-data>
        ...
</application>
```

​    注意，name必须是TV_LAUNCHER_PLUGIN，value是完整的插件类（包名+类名，如com.tianci.movieplatform.plugin.tvlauncher.MoviePluginImpl）

​    如果一个APP工程里面有多个插件，比如影视APP可能同时有影视、体育、教育等不同插件，则用 | 分开不同的插件类，并且每个插件类后面加上“：插件分类”，如下面示例（次序不重要）

```
<meta-data
    android:name="TV_LAUNCHER_PLUGIN"               android:value="com.tianci.movieplatform.plugin.tvlauncher.MoviePluginImpl:MOVIE|com.tianci.movieplatform.plugin.tvlauncher.EduTabPluginImpl:EDU|com.tianci.movieplatform.plugin.tvlauncher.SportsTabPluginImpl:SPORTS">
</meta-data>
```

   插件分类目前定义如下：

| 分类名称 | 分类值        |
| -------- | ------------- |
| 影视     | MOVIE         |
| 短视频   | SHORT_VIDEO   |
| AIOT     | AIOT          |
| 购物     | SHOPPING_MALL |
| 应用     | APP_STORE     |
| 教育     | EDUCATE       |
| 体育     | SPORTS        |
|          |               |

   可以动态拓展，只要和tab里的定义一致就可以

# 6、插件注意事项

## 6.1、插件页面内startActivity

​    如果插件代码中，是显示启动的内部Activity，示例代码如下：

```
Intent intent = new Intent();
...
intent.setClass(pluginContext, MActivity.class);
...
pluginContext.startActivity(intent);
```

​    插件UI页面被start的Activity需要在AndroidManifest.xml中注册的时候，添加android:exported="true"，如下：

```
<activity
   android:name=".MyActivity"
   android:exported="true">
   ...
</activity>
```



​    如果是通过action/category/uri等隐式方式启动的activity，则activity不需要添加exported true

```
Intent intent = new Intent("com.xxx.action.yy.xx");
...
pluginContext.startActivity(intent);
```

