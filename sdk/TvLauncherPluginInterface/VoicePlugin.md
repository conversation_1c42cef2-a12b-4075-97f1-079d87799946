# 新TV主页插件接口对接文档

[TOC]

# 1、插件形式

​    插件打包成apk，插件可以单独启动，显示自己的UI页面，也可以作为插件形式，将某个UI显示在主页中，如果在主页中有多个版面，就可以创建多个插件入口类，每个类里面返回对应的UI

# 2、对接步骤

​    对接步骤有如下几步：

1. 插件工程依赖本接口工程interface（必须是compileOnly），见下面工程依赖部分
2. 插件实现接口类（或者通过集成自基类实现），见下面接口API说明部分
3. 插件工程的AndroidManifest.xml里面通过meta-data方式添加接口实现类注册，见下面插件工程注册部分

# 3、语音插件工程配置

​      由于插件接口工程必须要compileOnly，所以插件接口工程只能是java library，需要插件工程有一些必要的配置来保证编译通过，具体如下：

##   3.1、语音插件工程需要的一些变量定义

​       1、电脑需要添加ANDROID_HOME的环境变量配置，正常安装Android SDK后都会有此配置，可以自行检查，Gradle Sync 或者Run事后，插件接口工程也会打印该配置，如果是null，表示没读到或者没配置对，会找不到Android相关方法，就会报错。

##   3.2、语音插件工程的引入

​     插件作为独立的APP工程，需要在build.gradle中添加对本接口工程的依赖

```
dependencies {
    ...
    compileOnly project(':tvlauncher_plugin_interface')
}
```

​    记得先添加工程到指定路径

```
include ':tvlauncher_plugin_interface'
project(':tvlauncher_plugin_interface').projectDir = getProjectDir('TvLauncherPluginInterface')
注意，如果是直接在根目录 settings.gradle中添加，可能会找不到 getProjectDir方法，就可以改成直接用file的方式，如下(修正为your path)：
project(':tvlauncher_plugin_interface').projectDir = new File('TvLauncherPluginInterface')
```

注：后续可能会将接口转到maven中，目前demo联调可以继续使用本工程

##   3.3、语音插件工程的混淆

​    如果插件工程需要混淆，那么需要在插件工程的proguard-rules.pro中添加如下keep保护：

```
-dontwarn com.ccos.tvlauncher.sdk.**
-keep class * implements com.ccos.tvlauncher.sdk.extension.ITvLauncherVoicePlugin {*;}
-keep class * extends com.ccos.tvlauncher.sdk.extension.BaseTvLauncherVoicePlugin {*;}
-keep class android.content.res.**
-keep class * extends java.lang.annotation.Annotation { *; }
-keep interface * extends java.lang.annotation.Annotation { *; }
```



# 4、语音插件接口API说明

## 4.1、语音插件接口API

### 4.1.1 ITvLauncherVoicePlugin

| 接口(参数)                                             | 接口用途                                         | 必须 | 注意事项                                                     |
| ------------------------------------------------------ | ------------------------------------------------ | ---- | ------------------------------------------------------------ |
| setContext(Context hostContext, Context pluginContext) | 设置宿主和插件的context                          | 是   | 实现类中需自己保存context                                    |
| setHeader(Map<String, String> header)                  | 设置header                                       | 否   | 插件可以使用宿主的header，也可以添加/修改参数                |
| onInit()                                               | 初始化                                           | 是   | set方法后，getVoiceView前调用，子线程中调用，可以做一些数据/UI创建初始化动作 |
| getVoiceView(）                                        | 获取语音的View                                   | 是   |                                                              |
| onPageChanged(String pageName, String pkgName)         | 主页页面切换变化                                 | 否   | pageName是页面名称，常用主要一般在 IPageDefine中定义，动态添加的拓展页面可能不在范围内；pkgName是页面插件的pkgName，可作为拓展参数使用 |
| onShow()                                               | 语音UI显示后回调，开始动画、加载图片等           | 是   |                                                              |
| onHide()                                               | 语音UI隐藏后回调，停止动画、回收内存、释放资源等 | 是   |                                                              |
| onDestroy()                                            | 主页Activity销毁                                 | 是   | 做一些销毁动作（如unBindService、unRegister广播回调等），避免内存泄漏 |



## 4.2、语音插件对接

​    快速简单对接只需要继承自基类（com.ccos.tvlauncher.sdk.extension.BaseTvLauncherVoicePlugin），实现几个必须接口或者override必要的接口即可；如：

```
public class MyTvLauncherVoicePlugin extends BaseTvLauncherVoicePlugin{

    MyVoiceView voiceView;

    @Override
    public void onInit() {
        super.onInit();
        if(voiceView == null) {
            voiceView = new MyVoiceView(pluginContext);
        }
    }

    @Override
    public View getVoiceView() {
//        if(voiceView == null) {//如果在onInit中new了，就不需要在这里new了
//            voiceView = new MyVoiceView(pluginContext);
//        }
        return voiceView;
    }

    @Override
    public void onShow() {
        if(voiceView != null) {
            voiceView.onShow();
        }
    }

    @Override
    public void onHide() {
        if(voiceView != null) {
            voiceView.onHide();
        }
    }
}
```



## 4.3、语音插件初始化

​    插件作为独立APP时，会在自己的Application中做一些初始化动作，比如下面是插件Application代码的示例：

```
插件原本MyApplication中的代码：

@Override
protected void attachBaseContext(Context base) {
    super.attachBaseContext(base);
}

@Override
public void onCreate() {
    super.onCreate();
    MyApplication.applicationContext = this;

    MyHeader.getInstance().initHeader(this);
    Util.instence(this);
    ImageLoader.getLoader().init(this);

    initLogSDK();
    initADSDK();
    initDeviceInfo();
    ...
    this.registerReceiver(new GlobalReceiver(), filter);
    ...
}
```

​     当插件在主页框架中加载的时候，是不会走到Application初始化的，所以需要插件在自己的plugin impl实现类中，补充做一些必要的初始化动作，必要是指只有插件UI页面需要的动作，不是插件UI页面需要的都不需要处理，因为点击跳转页面后就会拉起插件APP的进程，会走到插件Application的初始化。

​     插件UI在主页框架中的时候是在主页进程里的，插件点击跳转到自己的Activity页面后，就在插件自己的APP进程里了。如下是上面的插件实现类增加了必要的初始化逻辑后的示例。

```
public class MyPlugin extends BasePadLauncherPlugin {

    

    @Override
    public void onInit() {
        //注意，该方法也是子线程中调用的
        MyApplication.applicationContext = pluginContext.getApplicationContext();

        MyHeader.getInstance().initHeader(pluginContext.getApplicationContext());
        Util.instence(pluginContext.getApplicationContext());
        ImageLoader.getLoader().init(pluginContext.getApplicationContext());

        initLogSDK();
        
        ...其他逻辑
    }

    ...
}
```

# 5、语音插件注册生效步骤

​    经过上面几个步骤后，已经完成了插件接口的实现，在打包插件工程前，需要将实现的插件类在AndroidManifest.xml中注册添加，因为主页框架是通过扫描meta-data来寻找实现类的，找不到，就无法加载该插件。

​    添加方法如下，修改插件工程的 AndroidManifest.xml，添加如下 meta-data

```
<application...>
        ...
        <meta-data
             android:name="TV_LAUNCHER_VOICE_PLUGIN"
             android:value="com.xxx.yyy.zzz.MyPlugin">
        </meta-data>
        ...
</application>
```

​    注意，name必须是TV_LAUNCHER_VOICE_PLUGIN，value是完整的插件类（包名+类名，如com.tianci.movieplatform.plugin.tvlauncher.MyTvLauncherVoicePluginImpl）
