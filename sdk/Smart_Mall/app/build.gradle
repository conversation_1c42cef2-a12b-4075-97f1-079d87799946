apply plugin: 'com.android.application'

def ver_major = 1
def ver_minor = 0
def ver_build = getDate()

android {
    compileSdkVersion 29
    buildToolsVersion '30.0.1'

    defaultConfig {
        applicationId "com.coocaa.smartmall.app"
        minSdkVersion 17
        targetSdkVersion 27

        versionCode ((Integer.valueOf(ver_major) * 100 + Integer.valueOf(ver_minor)) * 1000000 + Integer.valueOf(ver_build))
        versionName ""
        versionName ver_major.toString() + "." + ver_minor.toString() + "." + ver_build.toString()

        manifestPlaceholders = [isRelease: false]
        ndk {
            abiFilters "armeabi-v7a"
        }
    }

    signingConfigs {
        release {
            keyAlias sign_config["keystore.alias"]
            keyPassword sign_config["keystore.alias_password"]
            storeFile file("../${sign_config["keystore.path"]}")
            storePassword sign_config["keystore.password"]
        }
    }


    buildTypes {
        debug {
            minifyEnabled false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

        }

        release {
            minifyEnabled false
            signingConfig signingConfigs.release
            manifestPlaceholders = [isRelease: true]
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            applicationVariants.all { variant ->
                def buildType = variant.buildType
                if (buildType.name.equals("debug")) {
                    return
                }
                variant.outputs.all {
                    outputFileName = 'SmartMall_V'+versionName+'.apk'
                }
            }
        }
    }
    sourceSets {
        main {
            res {
                srcDirs 'src/main/res', 'src/main/res/drawable'
            }
        }
    }
}

static def getDate() {
    def date = new Date()
    def formattedDate = date.format('MMddHH')
    return formattedDate
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation 'com.android.support:appcompat-v7:28.0.0'
    implementation project(':mall_app')
}