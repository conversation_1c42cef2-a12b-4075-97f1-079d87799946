ext {
    COMPILE_SDK_VERSION = 'android-28'
    MIN_SDK_VERSION = '15'
    TARGET_SDK_VERSION = '29'
    BUILDTOOLS_VERSION = '29.0.3'
    androidJar = getAndroidJar()
    layoutlibJar = getLayoutlibJar()

    androidx_appcompat_appcompat = '1.1.0'

    fastjson_version = '1.1.71.android'
    okhttp3_version = '3.6.0'
    okhttp3_logging_interceptor_version = '3.6.0'
    retrofit2_version = '2.1.0'
    retrofit2_converter_fastjson_android_version = '2.1.0'
    zxing_core_version = '3.4.0'
    nanohttpd_webserver = '2.3.1'

    kotlin_version = '1.3.71'
    anko_version = '0.10.1'

    support_annotations = '23.2.0'
    support_v4 = '23.2.0'
    recyclerview_v7 = '23.2.0'

    multidex = '1.0.2'


    version_code = getVersionCode()
    version_name = getVersionName()
}

File getProjectDir(String dir) {
    return new File("${buildscript.sourceFile.getParent()}/${dir}")
}


String getAndroidJar() {
    def path = getSDKPath()
    def androidPath = "${path}${File.separator}android.jar"
    return androidPath
}

String getSDKPath() {
    def android_home = System.getenv()['ANDROID_HOME']
    if (android_home == null || android_home.equals("")) {
        android_home = getSDKPathFromLocalProperty()
    }
    if (android_home == null || android_home.equals(""))
        return 'not found android.jar'
    def compileSdkVersion = COMPILE_SDK_VERSION
    def path = "${android_home}${File.separator}platforms${File.separator}${compileSdkVersion}"
    return path
}


String getSDKPathFromLocalProperty() {
    def localProperties = new File(rootDir, "local.properties")
    if (localProperties.exists()) {
        Properties properties = new Properties()
        localProperties.withInputStream {
            instr -> properties.load(instr)
        }
        return properties.getProperty('sdk.dir')
    }
    return ""
}

String getLayoutlibJar() {
    return getProjectDir("build-libs" + File.separator + "layoutlib.jar").absolutePath
}

int getVersionCode() {
    def cmd = 'git rev-list HEAD --first-parent --count'
    return cmd.execute().text.trim().toInteger()
}

String getVersionName() {
    def cmd = 'git describe --tags'
    def version = cmd.execute().text.trim()

    def pattern = "-(\\d+)-g"
    def matcher = version =~ pattern

    if (matcher) {
        version = version.substring(0, matcher.start()) + "." + matcher[0][1]
    } else {
        version = version + ".0"
    }

    return version
}