package com.coocaa.smartmall;

import android.content.ComponentName;

import java.util.Map;

import swaiotos.sal.network.INetwork;

public interface ISal {
    String getDeviceChip();

    String getDeviceModel();

    String getDeviceName();

    String getActiveID();

    String getBarcode();

    Map<String, String> getCommonHeader();

    boolean startNetSetting();

    String getMAC();

    String getVersionName();

    long getVersionCode();

    String getDeviceBrand();

    ComponentName getCurrentLauncher();

    boolean showNetSettings();

    String getPattern();

    boolean isStoreMode();

    boolean isOLED();
    // net settings
    int addNetListener(INetwork.INetworkListener listener);

    int removeNetListener(INetwork.INetworkListener listener);


}
