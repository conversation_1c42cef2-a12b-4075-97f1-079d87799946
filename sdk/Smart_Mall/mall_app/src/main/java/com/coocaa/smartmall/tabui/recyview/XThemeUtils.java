package com.coocaa.smartmall.tabui.recyview;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.StateListDrawable;

import com.skyworth.ui.api.ROUNDTYPE;
import com.skyworth.ui.api.SkyBorderFocusDrawable;
import com.skyworth.ui.api.SkyGradientShapeDrawable;
import com.skyworth.ui.api.SkyShapeDrawable;
import com.coocaa.smartmall.R;

/**
 * Created by XuZeXiao on 2016/12/13.
 * 主题跟随系统切换工具类
 * 使用前先初始化: XThemeUtils.init(Context context)
 */

public class XThemeUtils {

    private static Resources mResources;

    public static void init(Context context) {
        mResources = context.getResources();
    }

    private static Resources getResources() {
        if (mResources == null) {
            throw new NullPointerException("context is null, please initialize!!!");
        }
        return mResources;
    }

    /********************************************背景模块颜色规范********************************************************/
    /*主题色，跟随主题*/
    public static Drawable b_1() {
        return getResources().getDrawable(R.drawable.b_1);
    }

    public static Drawable b_2() {
        return getResources().getDrawable(R.drawable.b_2);
    }

    public static Drawable b_3() {
        return getResources().getDrawable(R.drawable.b_3);
    }

    public static Drawable b_4() {
        return getResources().getDrawable(R.drawable.b_4);
    }

    public static Drawable b_5() {
        return getResources().getDrawable(R.drawable.b_5);
    }

    public static Drawable b_6() {
        return getResources().getDrawable(R.drawable.b_6);
    }

    public static Drawable b_7() {
        return getResources().getDrawable(R.drawable.b_7);
    }

    public static Drawable b_7_no_shadow() {
        return getResources().getDrawable(R.drawable.b_7_no_shadow);
    }

    //圆角半径30
    public static Drawable b_7_round_corner() {
        return getResources().getDrawable(R.drawable.b_7_round_corner);
    }

    //圆角半径40
    public static Drawable b_7_title_round_corner() {
        return getResources().getDrawable(R.drawable.b_7_title_round_corner);
    }

    public static Drawable b_7_under_line() {
        return getResources().getDrawable(R.drawable.b_7_underline);
    }

    public static Drawable b_8() {
        return getResources().getDrawable(R.drawable.b_8);
    }

    //圆角半径30
    public static Drawable b_8_round_corner() {
        return getResources().getDrawable(R.drawable.b_8_round_corner);
    }

    //圆角半径40
    public static Drawable b_8_title_round_corner() {
        return getResources().getDrawable(R.drawable.b_8_title_round_corner);
    }

    public static Drawable b_9() {
        return getResources().getDrawable(R.drawable.b_9);
    }

    public static int c_b_1() {
        return getResources().getColor(R.color.c_b_1);
    }

    public static int c_b_2() {
        return getResources().getColor(R.color.c_b_2);
    }

    public static int c_b_3() {
        return getResources().getColor(R.color.c_b_3);
    }

    public static int c_b_4() {
        return getResources().getColor(R.color.c_b_4);
    }

    public static int c_b_5() {
        return getResources().getColor(R.color.c_b_5);
    }

    public static int c_b_6() {
        return getResources().getColor(R.color.c_b_6);
    }

    public static int c_b_7() {
        return getResources().getColor(R.color.c_b_7);
    }

    public static int c_b_8() {
        return getResources().getColor(R.color.c_b_8);
    }

    public static int c_b_9() {
        return getResources().getColor(R.color.c_b_9);
    }

    public static int c_b_10() {
        return getResources().getColor(R.color.c_b_10);
    }

    public static int c_b_11() {
        return getResources().getColor(R.color.c_b_11);
    }

    public static int c_b_12() {
        return getResources().getColor(R.color.c_b_12);
    }

    /*会员色，不跟随主题*/
//    public static final Drawable b_vip1 = getResources().getDrawable(R.drawable.b_vip1);
//    public static final int c_b_vip1 = getResources().getColor(R.color.c_b_vip1);
//    /*特殊色，不跟随主题*/
//    public static final Drawable b_p1 = getResources().getDrawable(R.drawable.b_p1);
//    public static final int c_b_p1 = getResources().getColor(R.color.c_b_p1);
//    public static final Drawable b_p2 = getResources().getDrawable(R.drawable.b_p2);
//    public static final int c_b_p2 = getResources().getColor(R.color.c_b_p2);
//    public static final Drawable b_p3 = getResources().getDrawable(R.drawable.b_p3);
//    public static final int c_b_p3 = getResources().getColor(R.color.c_b_p3);

    /********************************************dp长度引用********************************************************/

    //4px
    public static int focusWidth() {
        return (int) (getResources().getDimension(R.dimen.dimen_b6) + 0.5f);
    }

    /**
     * 反色处理
     *
     * @param resId      资源id
     * @param isAddColor icon+ 传 ture, icon- 传 false
     */
    @Deprecated
    public static Drawable getDrawable(int resId, Boolean isAddColor) {
        if (resId == 0) {
            return null;
        }
        return getResources().getDrawable(resId);
    }

    @Deprecated
    public static Bitmap getBitMap(Bitmap srcBmp, boolean isAddColor) {
        return srcBmp;
    }

    @Deprecated
    public static Drawable getDrawable(Bitmap srcBmp, boolean isAddColor) {
        return new BitmapDrawable(srcBmp);
    }

    /**
     * 无反色处理,标准getDrawable();
     *
     * @param resId
     */
    public static Drawable getDrawable(int resId) {
        if (resId == 0) {
            return null;
        }
        return getResources().getDrawable(resId);
    }

    /**
     * 产生shape类型的drawable
     *
     * @param solidColor  主颜色值
     * @param strokeColor 边框颜色
     * @param strokeWidth 边框宽度
     * @param radius      椭圆半径(上下左右的圆角，数组的2个元素为一个角)  new float[]{2,2,2,2,0,0,0,0}
     * @return
     */
    public static GradientDrawable getDrawable(int solidColor, int strokeColor, int strokeWidth, float[] radius) {
        GradientDrawable drawable = new GradientDrawable();
        drawable.setColor(solidColor);
        drawable.setStroke(strokeWidth, strokeColor);
        drawable.setCornerRadii(radius);
        return drawable;
    }

    public static GradientDrawable getDrawable(int solidColor, int strokeColor, int strokeWidth, int radius) {
        return getDrawable(solidColor, strokeColor, strokeWidth, new float[]{radius, radius, radius, radius, radius, radius, radius, radius});
    }

    /**
     * 以下8.0UI新增
     *
     * @return
     */
    private static int getColor(int res) {
        return getResources().getColor(res);
    }

    public static int c_1a() {
        return getColor(R.color.c_1a);
    }

    public static int c_2a() {
        return getColor(R.color.c_2a);
    }

    public static int c_2b() {
        return getColor(R.color.c_2b);
    }

    public static int c_2c() {
        return getColor(R.color.c_2c);
    }

    public static int c_2d() {
        return getColor(R.color.c_2d);
    }

    public static int c_3a() {
        return getColor(R.color.c_3a);
    }

    public static int c_3b() {
        return getColor(R.color.c_3b);
    }

    public static int c_3c() {
        return getColor(R.color.c_3c);
    }

    public static int c_3d() {
        return getColor(R.color.c_3d);
    }

    public static int c_4a() {
        return getColor(R.color.c_4a);
    }

    public static int c_4b() {
        return getColor(R.color.c_4b);
    }

    public static int c_4c() {
        return getColor(R.color.c_4c);
    }

    public static int c_4d() {
        return getColor(R.color.c_4d);
    }

    public static int c_5a() {
        return getColor(R.color.c_5a);
    }

    public static int c_5b() {
        return getColor(R.color.c_5b);
    }

    public static int c_5c() {
        return getColor(R.color.c_5c);
    }

    public static int c_5d() {
        return getColor(R.color.c_5d);
    }

    public static int c_6a() {
        return getColor(R.color.c_6a);
    }

    public static int c_7a() {
        return getColor(R.color.c_7a);
    }

    public static int c_7b() {
        return getColor(R.color.c_7b);
    }

    public static int c_7c() {
        return getColor(R.color.c_7c);
    }

    public static int c_7d() {
        return getColor(R.color.c_7d);
    }

    public static int v_1a() {
        return getColor(R.color.v_1a);
    }

    public static int v_2a() {
        return getColor(R.color.v_2a);
    }

    public static int v_3a() {
        return getColor(R.color.v_3a);
    }

    public static int v_4a() {
        return getColor(R.color.v_4a);
    }

    public static int b_7a() {
        return getColor(R.color.b_7a);
    }

    public static Drawable a_1() {
        return getDrawable(R.drawable.a_1);
    }

    public static Drawable a_1_oval() {
        return getDrawable(R.drawable.a_1_oval);
    }

    public static Drawable a_1_round() {
        return getDrawable(R.drawable.a_1_round);
    }

    public static Drawable b_3a_oval() {
        return getDrawable(R.drawable.b_3a_oval);
    }

    public static Drawable b_3a_round() {
        return getDrawable(R.drawable.b_3a_round);
    }

    public static Drawable b_1a_underline() {
        return getDrawable(R.drawable.b_1a_underline);
    }

    public static Drawable b_1a_title() {
        return getDrawable(R.drawable.b_1a_title);
    }

    public static Drawable b_1a_oval() {
        return getDrawable(R.drawable.b_1a_oval);
    }

    public static Drawable b_1a_round() {
        return getDrawable(R.drawable.b_1a_round);
    }

    public static Drawable b_4a_round() {
        return getDrawable(R.drawable.b_4a_round);
    }

    public static Drawable b_5a_round() {
        return getDrawable(R.drawable.b_5a_round);
    }

    public static Drawable b_6a_round() {
        return getDrawable(R.drawable.b_6a_round);
    }


    public static Drawable getSkyBorderFocusDrawable(Context context, int radius) {
        return new SkyBorderFocusDrawable(context).setCornerRadiu(radius).setRoundType(ROUNDTYPE.ALL);
    }

    public static SkyShapeDrawable getSkyShapeDrawable(Context context) {
        return new SkyShapeDrawable(context);
    }

    public static SkyGradientShapeDrawable getSkyGradientShapeDrawable(Context context) {
        return new SkyGradientShapeDrawable(context);
    }

    public static StateListDrawable getNormalBgDrawable(Context context, int radius) {
        return getNormalBgDrawable(context, radius, true);
    }

    public static StateListDrawable getNormalBgDrawable(Context context, int radius, boolean needUnfocusBg) {
        GradientDrawable backgroundDrawable = new GradientDrawable();
        backgroundDrawable.setShape(GradientDrawable.RECTANGLE);
        backgroundDrawable.setCornerRadius(radius);
        backgroundDrawable.setColor(Color.parseColor("#19ffffff"));
        SkyGradientShapeDrawable skyGradientShapeDrawable =
                getSkyGradientShapeDrawable(context).setCornerRadiu(radius);
        skyGradientShapeDrawable.setShape(GradientDrawable.RECTANGLE);

        StateListDrawable drawable = new StateListDrawable();
        if (needUnfocusBg) {
            //Non focused states
            drawable.addState(new int[]{-android.R.attr.state_focused, -android.R.attr.state_selected, -android.R.attr.state_pressed},
                    backgroundDrawable);
            drawable.addState(new int[]{-android.R.attr.state_focused, android.R.attr.state_selected, -android.R.attr.state_pressed},
                    backgroundDrawable);
        }
        //Focused states
        drawable.addState(new int[]{android.R.attr.state_focused, -android.R.attr.state_selected, -android.R.attr.state_pressed},
                skyGradientShapeDrawable);
        drawable.addState(new int[]{android.R.attr.state_focused, android.R.attr.state_selected, -android.R.attr.state_pressed},
                skyGradientShapeDrawable);
        //Pressed
        drawable.addState(new int[]{android.R.attr.state_selected, android.R.attr.state_pressed},
                skyGradientShapeDrawable);
        drawable.addState(new int[]{android.R.attr.state_pressed},
                skyGradientShapeDrawable);
        return drawable;
    }

    public static float getRoundRadius() {
        return getResources().getDimension(R.dimen.ui_sdk_common_round_radius);
    }
}
