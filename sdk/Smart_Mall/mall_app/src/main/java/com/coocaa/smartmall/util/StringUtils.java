package com.coocaa.smartmall.util;

import android.graphics.Bitmap;
import android.text.TextUtils;
import android.widget.ImageView;
import android.widget.TextView;

import com.alibaba.fastjson.JSONObject;
import com.coocaa.mylibrary.discover.DetailResult.Detail;
import com.coocaa.mylibrary.discover.RecommandResult;
import com.skyworth.util.Util;

import java.util.List;

public class StringUtils {
   public static String cacheListDefaultStr="{\n" +
           "    \"resultcode\": \"200\",\n" +
           "    \"reason\": \"success\",\n" +
           "    \"data_result\": [\n" +
           "        {\n" +
           "            \"product_id\": \"17\",\n" +
           "            \"product_name\": \"Swaiot PANEL台面支架\",\n" +
           "            \"product_image\": \"http://update-nj.skyworth-cloud.com/nj_apk/weixin/20200807135516a6un47.png\",\n" +
           "            \"product_description\": \"Swaiot PANEL专属台面支架，可为Swaiot PANEL充电，也可以作为一款精美相框\",\n" +
           "            \"grade\": 1\n" +
           "        },\n" +
           "        {\n" +
           "            \"product_id\": \"16\",\n" +
           "            \"product_name\": \"Swaiot PANEL挂墙支架\",\n" +
           "            \"product_image\": \"http://update-nj.skyworth-cloud.com/nj_apk/weixin/20200807132757ymn14s.png\",\n" +
           "            \"product_description\": \"Swaiot PANEL专属挂墙支架，可为Swaiot PANEL充电\",\n" +
           "            \"grade\": 2\n" +
           "        },\n" +
           "        {\n" +
           "            \"product_id\": \"22\",\n" +
           "            \"product_name\": \"Swaiot PANEL\",\n" +
           "            \"product_image\": \"http://update-nj.skyworth-cloud.com/nj_apk/weixin/20200821111448h0pqbx.png\",\n" +
           "            \"product_description\": \"一块掌上的智慧屏\",\n" +
           "            \"grade\": 1\n" +
           "        },\n" +
           "        {\n" +
           "            \"product_id\": \"24\",\n" +
           "            \"product_name\": \"Swaiot 智能LED球泡灯\",\n" +
           "            \"product_image\": \"http://update-nj.skyworth-cloud.com/nj_apk/weixin/20200821142327c5hoag.jpg\",\n" +
           "            \"product_description\": \"智能灯泡，无极调光调色，5W，3000K-6500K\",\n" +
           "            \"grade\": 2\n" +
           "        },\n" +
           "        {\n" +
           "            \"product_id\": \"23\",\n" +
           "            \"product_name\": \"Swaiot 智能蓝牙网关\",\n" +
           "            \"product_image\": \"http://update-nj.skyworth-cloud.com/nj_apk/weixin/20200821140003tx2wnw.jpg\",\n" +
           "            \"product_description\": \"创维智慧网关，WiFi-Mesh协议，语音远程自动化控制\",\n" +
           "            \"grade\": 3\n" +
           "        },\n" +
           "        {\n" +
           "            \"product_id\": \"20\",\n" +
           "            \"product_name\": \"创维智慧网关\",\n" +
           "            \"product_image\": \"http://update-nj.skyworth-cloud.com/nj_apk/weixin/20200807143055qouvvz.jpg\",\n" +
           "            \"product_description\": \"创维智慧网关，WiFi-Zigbee-Mesh协议三合一，语音远程自动化控制\",\n" +
           "            \"grade\": 5\n" +
           "        },\n" +
           "        {\n" +
           "            \"product_id\": \"25\",\n" +
           "            \"product_name\": \"Swaiot 智能开关模块\",\n" +
           "            \"product_image\": \"http://update-nj.skyworth-cloud.com/nj_apk/weixin/2020082119450812pk3i.jpg\",\n" +
           "            \"product_description\": \"智能开关模块，一位开关\",\n" +
           "            \"grade\": 8\n" +
           "        },\n" +
           "        {\n" +
           "            \"product_id\": \"26\",\n" +
           "            \"product_name\": \"Swaiot 智能转换器\",\n" +
           "            \"product_image\": \"http://update-nj.skyworth-cloud.com/nj_apk/weixin/20200822163628oaoxfu.jpg\",\n" +
           "            \"product_description\": \"智能插座，10A，定时开关，远程控制，语音控制\",\n" +
           "            \"grade\": 9\n" +
           "        },\n" +
           "        {\n" +
           "            \"product_id\": \"27\",\n" +
           "            \"product_name\": \"Swaiot X FSL 智能光源模组\",\n" +
           "            \"product_image\": \"http://update-nj.skyworth-cloud.com/nj_apk/weixin/20200822165323h79zed.jpg\",\n" +
           "            \"product_description\": \"光源改造模组，25W，3000K-6500K\",\n" +
           "            \"grade\": 10\n" +
           "        },\n" +
           "        {\n" +
           "            \"product_id\": \"28\",\n" +
           "            \"product_name\": \"Swaiot 智慧套装入门版\",\n" +
           "            \"product_image\": \"http://update-nj.skyworth-cloud.com/nj_apk/weixin/20200822173336d8o2jb.jpg\",\n" +
           "            \"product_description\": \"Mesh入门版\",\n" +
           "            \"grade\": 11\n" +
           "        },\n" +
           "        {\n" +
           "            \"product_id\": \"29\",\n" +
           "            \"product_name\": \"Swaiot 智慧套装升级版\",\n" +
           "            \"product_image\": \"http://update-nj.skyworth-cloud.com/nj_apk/weixin/20200822175813apyglw.jpg\",\n" +
           "            \"product_description\": \"Mesh升级版\",\n" +
           "            \"grade\": 12\n" +
           "        },\n" +
           "        {\n" +
           "            \"product_id\": \"31\",\n" +
           "            \"product_name\": \"Swaiot X FSL 智能吸顶灯 圆形 25W\",\n" +
           "            \"product_image\": \"http://update-nj.skyworth-cloud.com/nj_apk/voice/manage/mall/0ef48044-eea8-11ea-9292-52540018b279.jpg\",\n" +
           "            \"product_description\": \"智能吸顶灯，圆形，25W\",\n" +
           "            \"grade\": 13\n" +
           "        },\n" +
           "        {\n" +
           "            \"product_id\": \"32\",\n" +
           "            \"product_name\": \"Swaiot X FSL 智能吸顶灯 圆形 36W\",\n" +
           "            \"product_image\": \"http://update-nj.skyworth-cloud.com/nj_apk/voice/manage/mall/281c6aa0-eea8-11ea-9292-52540018b279.jpg\",\n" +
           "            \"product_description\": \"智能吸顶灯，圆形，36W\",\n" +
           "            \"grade\": 14\n" +
           "        },\n" +
           "        {\n" +
           "            \"product_id\": \"30\",\n" +
           "            \"product_name\": \"Swaiot X FSL 智能吸顶灯 方形 72W\",\n" +
           "            \"product_image\": \"http://update-nj.skyworth-cloud.com/nj_apk/voice/manage/mall/38762378-eea8-11ea-9292-52540018b279.jpg\",\n" +
           "            \"product_description\": \"智能吸顶灯，方形，72W\",\n" +
           "            \"grade\": 15\n" +
           "        }\n" +
           "    ],\n" +
           "    \"total\": 14\n" +
           "}";
    public static String getJsonString(Object object ){
        String str= "";
        try {
            str=JSONObject.toJSONString(object);
        }catch (Exception e){
            e.printStackTrace();
        }
        return str;
    }
    public static Object parseObject(String str,Class clazz){
        Object o=null;
        try {
            o =JSONObject.parseObject(str,clazz);
        }catch (Exception e){
            e.printStackTrace();
        }
        return o;
    }
    public static String getTags(List<String> data){
        StringBuffer sb = new StringBuffer();
        if (EmptyUtils.isNotEmpty(data)) {
            for (int i = 0; i < data.size(); i++) {
                sb.append(data.get(i));
                if (i != data.size() - 1) {
                    sb.append(" | ");
                }

            }
        }
        return sb.toString();
    }
    public static String getQRString(Detail data){
        StringBuffer sb=new StringBuffer();
                 sb.append("product_id = ").append(data.getProductId())
                .append("  image_url = ").append(data.getImageUrl())
                .append("  product_name = ").append(data.getProductName())
                .append("  product_price = ").append(data.getPrice());
       return sb.toString();
    }
    public static void initQRInfo(TextView name, TextView price, TextView tag, ImageView qrcodeImageView, Detail data){
        //设置主题
        if (!TextUtils.isEmpty(data.getProductName())) {
            name.setText(data.getProductName());
        }
        //设置价格
        if (!TextUtils.isEmpty(data.getPrice())) {
            price.setText("¥ "+data.getPrice());
        }
        //设置标志

        tag.setText(StringUtils.getTags(data.getTags()));
        //设置二维码
        try {
            //todo 二维码内容没有字段
            final Bitmap bitmap = QRUtils.createQRImage(data.getQrcode_url(), Util.Div(430), Util.Div(430), null);
            if (bitmap != null) {
                qrcodeImageView.setImageBitmap(bitmap);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
