package com.coocaa.smartmall.tabui;

import android.content.res.ColorStateList;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.GradientDrawable;

public class RoundDrawable extends GradientDrawable {
    private boolean mIsStadium = false;
    private ColorStateList mSolidColors;
    private int mFillColor;

    public RoundDrawable(boolean isStadium) {
        mIsStadium = isStadium;
    }

    public void setSolidColors(ColorStateList colors) {
        mSolidColors = colors;
        setColor(colors.getDefaultColor());
    }

    @Override
    protected void onBoundsChange(Rect bounds) {
        super.onBoundsChange(bounds);
        if (mIsStadium) {
            RectF rect = new RectF(getBounds());
            setCornerRadius((rect.height() > rect.width() ? rect.width() : rect.height()) / 2);
        }
    }

    @Override
    public void setColor(int argb) {
        mFillColor = argb;
        super.setColor(argb);
    }

    @Override
    protected boolean onStateChange(int[] stateSet) {
        if (mSolidColors != null) {
            final int newColor = mSolidColors.getColorForState(stateSet, 0);
            if (mFillColor != newColor) {
                setColor(newColor);
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean isStateful() {
        return super.isStateful() || (mSolidColors != null && mSolidColors.isStateful());
    }
}
