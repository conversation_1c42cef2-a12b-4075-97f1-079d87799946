package com.coocaa.smartmall.tabadepter;

import android.graphics.Rect;
import android.support.v7.widget.GridLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;

import com.skyworth.util.Util;

import java.util.List;

public abstract class ReclclerViewBaseAdapter<T> extends RecyclerView.Adapter {
    public static class GridDivider extends RecyclerView.ItemDecoration {
        int space;
        public GridDivider(int space){
            this.space=space;
        }
        @Override
        public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
//            GridLayoutManager.LayoutParams params = (GridLayoutManager.LayoutParams) view.getLayoutParams();
//            // 获取item在span中的下标
//            int spanIndex = params.getSpanIndex();
//            // 中间间隔
//            Log.i("spanIndex",spanIndex+"position"+parent.getChildAdapterPosition(view));
//            if (spanIndex % 4 == 0) {
//                // outRect.left = 0;
//            } else {
//                // item为奇数位，设置其左间隔
//                //   outRect.left = Util.Div(40);
//            }
             outRect.left=space;
//            // 上方间隔
            outRect.bottom= space;
        }
    }
    List<T> datas;

    public List<T> getDatas() {
        return datas;
    }

    public void setDatas(List<T> datas) {
        this.datas = datas;
    }
    public T getData(int position) {
        return datas.get(position);
    }
    @Override
    public int getItemCount() {
        return datas==null?0:datas.size();
    }
    public  class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener, View.OnFocusChangeListener ,View.OnKeyListener{
        public int position;
        public ViewHolder(View itemView) {
            super(itemView);
            itemView.setFocusable(true);
            itemView.setClickable(true);
            itemView.setOnClickListener(this);
            itemView.setOnFocusChangeListener(this);
            itemView.setOnKeyListener(this);
        }

        @Override
        public void onClick(View v) {
            onItemClick(v,position);
        }

        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            onItemFocus(v,hasFocus,position);
        }

        @Override
        public boolean onKey(View v, int keyCode, KeyEvent event) {
            return onItemKey(v,keyCode,event,position);
        }
    }
    public  abstract void update(RecyclerView.ViewHolder holder, T data);
    public  abstract   void onItemClick(View itemView,int position);
    public  abstract void onItemFocus(View itemView,boolean hasFocus,int position);
    public  abstract boolean onItemKey(View v, int keyCode, KeyEvent event,int position);
}
