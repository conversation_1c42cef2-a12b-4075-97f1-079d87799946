package com.coocaa.smartmall.detail;

import android.content.Context;
import android.graphics.drawable.GradientDrawable;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.coocaa.smartmall.R;
import com.coocaa.smartmall.util.ImageUtils;
import com.coocaa.smartmall.util.StringUtils;
import com.coocaa.smartmall.util.ThreadManager;
import com.coocaa.smartmall.detail.adpter.AutoAdapter;
import com.coocaa.smartmall.util.ViewBuilder;
import com.coocaa.mylibrary.discover.DetailResult.Detail;
import com.skyworth.util.Util;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/18
 */
public class ImageDetailLayout extends FrameLayout {
    private ImageView mLeftImageView, mRightImageView, mQRCodeImageView, mIconImageView, mGuideImageView;
    private RelativeLayout mContentLayout, mRootView;
    private LinearLayout mIndexLayout;
    private TextView mThemeTextView, mDescribeTextView, mPriceTextView;
    private RecyclerView mRecyclerView;
    private int mCurrentPosition;
    private Run mRun;
    private GradientDrawable white_40GroudDrawable, white_100GroudDrawable;
    private Detail mProductDetailInfo;


    public ImageDetailLayout( Context context) {
        super(context);
        init();
    }

    public ImageDetailLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public ImageDetailLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        addView(ViewBuilder.getDetailImageLayout(getContext()));
    }

//    @RequiresApi(api = Build.VERSION_CODES.N)
    public void createView(Detail detailInfo) {
        mProductDetailInfo = detailInfo;
        mRootView = findViewById(R.id.smart_home_detail_rootview);
        mLeftImageView = findViewById(R.id.smart_home_detail_direct_left);
        mRightImageView = findViewById(R.id.smart_home_detail_direct_right);
        mQRCodeImageView = findViewById(R.id.smart_home_detail_qrcode);
        mIconImageView = findViewById(R.id.smart_home_detail_icon);
        mContentLayout = findViewById(R.id.smart_home_detail_content_layout);
        mThemeTextView = findViewById(R.id.smart_home_detail_theme);
        mDescribeTextView = findViewById(R.id.smart_home_detail_describe);
        mPriceTextView = findViewById(R.id.smart_home_detail_price);
        mRecyclerView = findViewById(R.id.smart_home_detail_recyclerView);
        mGuideImageView = findViewById(R.id.smart_home_detail_guide);
        mGuideImageView.setVisibility(View.INVISIBLE);
        mContentLayout.setVisibility(View.VISIBLE);

        //边框圆角效果
        white_40GroudDrawable = new GradientDrawable();
        white_40GroudDrawable.setShape(GradientDrawable.RECTANGLE);
        white_40GroudDrawable.setCornerRadius(Util.Div(7));
        white_40GroudDrawable.setColor(getResources().getColor(R.color.white_40));

        white_100GroudDrawable = new GradientDrawable();
        white_100GroudDrawable.setShape(GradientDrawable.RECTANGLE);
        white_100GroudDrawable.setCornerRadius(Util.Div(7));
        white_100GroudDrawable.setColor(getResources().getColor(R.color.white_100));

        initData();
    }

    public static String convertSpannedToRichText(Spanned spanned) {
        SpannableStringBuilder stringBuilder = new SpannableStringBuilder(spanned);
        return stringBuilder.toString().replace(", " , " | ").replace(". " , " | ");
    }

    /**
     * 初始化数据
     */
//    @RequiresApi(api = Build.VERSION_CODES.N)
    private void initData() {
        if (mProductDetailInfo != null) {
            StringUtils.initQRInfo(mThemeTextView,mPriceTextView,mDescribeTextView,mQRCodeImageView,mProductDetailInfo);
            //设置产品图片
            ImageUtils.loadImage(mIconImageView,mProductDetailInfo.getImageUrl(),Util.Div(200), Util.Div(200));
            //创建滚动index
            if (mProductDetailInfo.getImages()!= null) {
                if (mProductDetailInfo.getImages().size() > 1) {
                    mIndexLayout = new LinearLayout(getContext().getApplicationContext());
                    RelativeLayout.LayoutParams indexLayoutRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                            RelativeLayout.LayoutParams.WRAP_CONTENT);
                    indexLayoutRL.topMargin = Util.Div(50);
                    indexLayoutRL.addRule(RelativeLayout.CENTER_HORIZONTAL);
                    mRootView.addView(mIndexLayout, indexLayoutRL);

                    for (int i = 0; i < mProductDetailInfo.getImages().size(); i++) {
                        TextView textView = new TextView(getContext().getApplicationContext());
                        if (i == 0) {
                            textView.setBackground(white_100GroudDrawable);
                        } else {
                            textView.setBackground(white_40GroudDrawable);
                        }

                        LinearLayout.LayoutParams textViewLL = new LinearLayout.LayoutParams(Util.Div(20), Util.Div(5));
                        textViewLL.leftMargin = Util.Div(10);
                        mIndexLayout.addView(textView, textViewLL);
                    }
                } else {
                    mLeftImageView.setVisibility(View.INVISIBLE);
                    mRightImageView.setVisibility(View.INVISIBLE);
                }
                mCurrentPosition = Integer.MAX_VALUE/2 - Integer.MAX_VALUE%mProductDetailInfo.getImages().size() -1;
                setupRecyclerView();
                mRun = new Run();
                ThreadManager.getInstance().uiThread(mRun, 10000);
            }
        }
    }
    long lastTime=0;
    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            long currentTime=System.currentTimeMillis();
            switch (event.getKeyCode()) {
                case KeyEvent.KEYCODE_DPAD_RIGHT:
                    if (mProductDetailInfo == null || mProductDetailInfo.getImages() == null || mProductDetailInfo.getImages().size() <= 1)
                        return false;
                    if(currentTime-lastTime<200)return true;
                    lastTime=currentTime;
                    mCurrentPosition++;
                    mRecyclerView.smoothScrollToPosition(mCurrentPosition);
                    reflushIndexLayout();
                    ThreadManager.getInstance().removeUiThread(mRun);
                    ThreadManager.getInstance().uiThread(mRun, 10000);
                    return true;
                case KeyEvent.KEYCODE_DPAD_LEFT:
                    if (mProductDetailInfo == null || mProductDetailInfo.getImages() == null || mProductDetailInfo.getImages().size() <= 1)
                        return false;
                    if(currentTime-lastTime<200)return true;
                    lastTime=currentTime;
                    mCurrentPosition--;
                    mRecyclerView.smoothScrollToPosition(mCurrentPosition);
                    reflushIndexLayout();
                    ThreadManager.getInstance().removeUiThread(mRun);
                    ThreadManager.getInstance().uiThread(mRun, 10000);
                    return true;
                case KeyEvent.KEYCODE_DPAD_DOWN:
                    mContentLayout.setVisibility(View.VISIBLE);
                    mGuideImageView.setVisibility(View.INVISIBLE);
                    ThreadManager.getInstance().removeUiThread(mRun);
                    ThreadManager.getInstance().uiThread(mRun, 10000);
                    return true;
                default:
                    break;
            }
        }
        return super.dispatchKeyEvent(event);
    }

    private class Run implements Runnable {

        @Override
        public void run() {
            if (!stop||!ImageDetailLayout.this.isActivated()) {
                if (mContentLayout.getVisibility() == View.VISIBLE) {
                    mContentLayout.setVisibility(View.INVISIBLE);
                    mGuideImageView.setVisibility(View.VISIBLE);
                }
                mCurrentPosition++;
                mRecyclerView.smoothScrollToPosition(mCurrentPosition);

                reflushIndexLayout();
                ThreadManager.getInstance().uiThread(mRun, 6000);
            }

        }
    }
public void onPause(){

}
private boolean stop;
public void onStop(){
        if(mRun!=null){
            stop=true;
            ThreadManager.getInstance().removeUiThread(mRun);
        }
}
    private void reflushIndexLayout() {
        if (mIndexLayout != null && mIndexLayout.getChildCount() > 0) {
            for (int i = 0; i < mIndexLayout.getChildCount(); i++) {
                if (mCurrentPosition % mIndexLayout.getChildCount() == i) {
                    mIndexLayout.getChildAt(i).setBackground(white_100GroudDrawable);
                } else {
                    mIndexLayout.getChildAt(i).setBackground(white_40GroudDrawable);
                }
            }
        }
    }

    private void setupRecyclerView() {
        AutoAdapter adAdapter = new AutoAdapter(getContext().getApplicationContext(), mProductDetailInfo.getImages());
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getContext().getApplicationContext(), LinearLayoutManager.HORIZONTAL, false));
        mRecyclerView.setAdapter(adAdapter);
        mRecyclerView.scrollToPosition(mCurrentPosition);
    }
}
