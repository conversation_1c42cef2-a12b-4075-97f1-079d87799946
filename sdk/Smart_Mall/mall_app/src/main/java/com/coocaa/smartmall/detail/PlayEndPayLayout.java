package com.coocaa.smartmall.detail;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.GradientDrawable;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.coocaa.smartmall.R;
import com.coocaa.smartmall.util.StringUtils;
import com.coocaa.smartmall.util.UiUtil;
import com.coocaa.mylibrary.discover.DetailResult.Detail;
import com.coocaa.smartmall.util.ViewBuilder;
import com.skyworth.util.Util;

/**
 * @Description: 播放完成后的支付二维码View
 * @Author: wzh
 * @CreateDate: 2020/6/18
 */
public class PlayEndPayLayout extends RelativeLayout {

    private RelativeLayout mCenterLayout;
    private TextView themeTextView;
    private TextView describeTextView;
    private TextView priceTextView;
    private ImageView qrcodeImageView;
    private Button mLookAgainBtn;

    public PlayEndPayLayout(Context context) {
        super(context);
        GradientDrawable bg = new GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, new int[]{R.color.black_10, R.color.black_50});
        setBackground(bg);
    }

    private void initView() {
        Typeface typefaceB=Typeface.defaultFromStyle(Typeface.BOLD);
        mCenterLayout = new RelativeLayout(getContext());
        setBackgroundResource(R.mipmap.detail_bg);
        int colorWhite=Color.WHITE;
        int colorBlack=Color.BLACK;
        mCenterLayout.setPadding(Util.Div(50), Util.Div(50), Util.Div(50), Util.Div(50));
        mCenterLayout.setBackground(UiUtil.getDrawable(getResources().getColor(R.color.black_80), 0, 0, Util.Div(16)));
        LayoutParams params = new LayoutParams(Util.Div(860), Util.Div(440));
        params.addRule(CENTER_IN_PARENT);
        addView(mCenterLayout, params);


        themeTextView = ViewBuilder.initTextView(new TextView(getContext()),Util.Dpi(40),colorWhite,2,"",typefaceB);
        themeTextView.setId(R.id.smart_home_detail_theme_text);
        LayoutParams themeRL = new  LayoutParams(Util.Div(450), ViewGroup.LayoutParams.WRAP_CONTENT);
        mCenterLayout.addView(themeTextView, themeRL);


        describeTextView =ViewBuilder.initTextView(new TextView(getContext()),Util.Dpi(24),colorWhite,99,"",null);
        describeTextView.setId(R.id.smart_home_detail_describe_text);
        LayoutParams describeTextViewRL = new LayoutParams(Util.Div(450), Util.Div(154));
        describeTextViewRL.topMargin = Util.Div(20);
        describeTextViewRL.addRule(RelativeLayout.BELOW,R.id.smart_home_detail_theme_text);
        mCenterLayout.addView(describeTextView, describeTextViewRL);


        priceTextView =ViewBuilder.initTextView(new TextView(getContext()),Util.Dpi(40),getContext().getResources().getColor(R.color.color_FF5E00),1,"",typefaceB);
        LayoutParams priceTextViewRL = new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
        priceTextViewRL.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        priceTextViewRL.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
        mCenterLayout.addView(priceTextView, priceTextViewRL);

        //边框圆角效果
        GradientDrawable backGroudDrawable = new GradientDrawable();
        backGroudDrawable.setShape(GradientDrawable.RECTANGLE);
        backGroudDrawable.setCornerRadius(Util.Div(8));
        backGroudDrawable.setColor(colorWhite);


        qrcodeImageView = new ImageView(getContext());
        qrcodeImageView.setPadding(Util.Div(15), Util.Div(15), Util.Div(15), Util.Div(25));
        LayoutParams qrcodeLayoutRL = new LayoutParams(Util.Div(280), Util.Div(280));
        qrcodeLayoutRL.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
        mCenterLayout.addView(qrcodeImageView, qrcodeLayoutRL);
        qrcodeImageView.setBackground(backGroudDrawable);

        RelativeLayout btnFocusView = new RelativeLayout(getContext());
        btnFocusView.setBackground(UiUtil.getDrawable(0, colorWhite, Util.Div(4), Util.Div(53)));
        params = new LayoutParams(Util.Div(316), Util.Div(96));
        params.topMargin = Util.Div(843);
        params.addRule(CENTER_HORIZONTAL);
        addView(btnFocusView, params);
        btnFocusView.setPadding(Util.Div(8), Util.Div(8), Util.Div(8), Util.Div(8));
        mLookAgainBtn = (Button) ViewBuilder.initTextView(new Button(getContext()),Util.Dpi(28),colorBlack,1,"再看一遍",typefaceB);
        mLookAgainBtn.setGravity(Gravity.CENTER);
        mLookAgainBtn.setBackground(UiUtil.getDrawable(colorWhite, 0, 0, Util.Div(40)));
        params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        btnFocusView.addView(mLookAgainBtn, params);
        mLookAgainBtn.requestFocus();
    }

//    @RequiresApi(api = Build.VERSION_CODES.N)
    public void setData(Detail data) {
        if (mCenterLayout != null) {
            return;
        }
        initView();
        StringUtils.initQRInfo(themeTextView,priceTextView,describeTextView,qrcodeImageView,data);
    }
}
