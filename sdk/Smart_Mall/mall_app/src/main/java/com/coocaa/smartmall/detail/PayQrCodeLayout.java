package com.coocaa.smartmall.detail;

import android.content.Context;
import android.graphics.Typeface;
import android.graphics.drawable.GradientDrawable;
import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.coocaa.smartmall.R;
import com.coocaa.smartmall.util.ImageUtils;
import com.coocaa.smartmall.util.StringUtils;
import com.coocaa.mylibrary.discover.DetailResult.Detail;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;

/**
 * @Description: 按下键二维码详情View
 * @Author: wzh
 * @CreateDate: 2020/6/18
 */
public class PayQrCodeLayout extends FrameLayout {

    public PayQrCodeLayout(Context context) {
        super(context);
    }

//    @RequiresApi(api = Build.VERSION_CODES.N)
    public void setData(Detail data) {
        //渐变效果
        GradientDrawable aDrawable = new GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM,
                new int[]{android.R.color.transparent, R.color.black_50});

        RelativeLayout contentLayout = new RelativeLayout(getContext());
        contentLayout.setBackground(aDrawable);
        contentLayout.setId(R.id.smart_home_detail_content_layout);
        RelativeLayout.LayoutParams contentRL = new RelativeLayout.LayoutParams(
                RelativeLayout.LayoutParams.MATCH_PARENT, Util.Div(500));
        contentRL.topMargin = Util.Div(580);
        addView(contentLayout, contentRL);

        //边框圆角效果
        GradientDrawable BackGroudDrawable = new GradientDrawable();
        BackGroudDrawable.setShape(GradientDrawable.RECTANGLE);
        BackGroudDrawable.setCornerRadius(Util.Div(8));
        BackGroudDrawable.setColor(getContext().getResources().getColor(R.color.white_100));

        RelativeLayout iconLayout = new RelativeLayout(getContext());
        iconLayout.setBackground(BackGroudDrawable);
        iconLayout.setPadding(Util.Div(10), Util.Div(10), Util.Div(10), Util.Div(10));
        RelativeLayout.LayoutParams iconRL = new RelativeLayout.LayoutParams(Util.Div(200),
                Util.Div(200));
        iconRL.leftMargin = Util.Div(470);
        iconRL.topMargin = Util.Div(250);
        contentLayout.addView(iconLayout, iconRL);

        View iconImageView = ImageLoader.getLoader().getView(getContext());
        iconImageView.setId(R.id.smart_home_detail_icon);
        iconLayout.addView(iconImageView, new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                RelativeLayout.LayoutParams.MATCH_PARENT));

        TextView themeTextView = new TextView(getContext());
        themeTextView.setTextColor(getContext().getResources().getColor(R.color.white_100));
        themeTextView.setTextSize(Util.Dpi(36));
        themeTextView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        themeTextView.setSingleLine(true);
        themeTextView.setMarqueeRepeatLimit(-1);
        themeTextView.setGravity(Gravity.START);
        themeTextView.setId(R.id.smart_home_detail_theme);
        RelativeLayout.LayoutParams themeRL = new RelativeLayout.LayoutParams(Util.Div(500),
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        themeRL.topMargin = Util.Div(250);
        themeRL.leftMargin = Util.Div(690);
        contentLayout.addView(themeTextView, themeRL);

        TextView describeTextView = new TextView(getContext());
        describeTextView.setId(R.id.smart_home_detail_describe);
        describeTextView.setTextColor(getContext().getResources().getColor(R.color.white_100));
        describeTextView.setTextSize(Util.Dpi(24));
        describeTextView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        describeTextView.setSingleLine(false);
        describeTextView.setGravity(Gravity.START);
        RelativeLayout.LayoutParams describeTextViewRL = new RelativeLayout.LayoutParams(Util.Div(500),
                Util.Div(64));
        describeTextViewRL.topMargin = Util.Div(295);
        describeTextViewRL.leftMargin = Util.Div(690);
        contentLayout.addView(describeTextView, describeTextViewRL);

        TextView priceTextView = new TextView(getContext());
        priceTextView.setId(R.id.smart_home_detail_price);
        priceTextView.setTextColor(getContext().getResources().getColor(R.color.color_FF5E00));
        priceTextView.setTextSize(Util.Dpi(24));
        priceTextView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        priceTextView.setSingleLine(true);
        RelativeLayout.LayoutParams priceTextViewRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        priceTextViewRL.topMargin = Util.Div(414);
        priceTextViewRL.leftMargin = Util.Div(690);
        contentLayout.addView(priceTextView, priceTextViewRL);

        ImageView qrcodeImageView = new ImageView(getContext());
        qrcodeImageView.setId(R.id.smart_home_detail_qrcode);
        qrcodeImageView.setBackground(BackGroudDrawable);
        qrcodeImageView.setPadding(Util.Div(10), Util.Div(10), Util.Div(10), Util.Div(10));
        RelativeLayout.LayoutParams qrcodeLayoutRL = new RelativeLayout.LayoutParams(Util.Div(200),
                Util.Div(200));
        qrcodeLayoutRL.leftMargin = Util.Div(1250);
        qrcodeLayoutRL.topMargin = Util.Div(250);
        contentLayout.addView(qrcodeImageView, qrcodeLayoutRL);



        //

        StringUtils.initQRInfo(themeTextView,priceTextView,describeTextView,qrcodeImageView,data);
        ImageUtils.loadImage(iconImageView,data.getImageUrl(),Util.Div(200), Util.Div(200));
    }
}
