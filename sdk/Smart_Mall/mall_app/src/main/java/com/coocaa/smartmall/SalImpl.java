package com.coocaa.smartmall;


import android.content.ComponentName;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.coocaa.os.idmanager.client.SystemApi;

import java.util.Map;

import swaiotos.sal.SalModule;
import swaiotos.sal.audio.IAudio;
import swaiotos.sal.hardware.IScreen;
import swaiotos.sal.network.INetwork;
import swaiotos.sal.picture.IPicture;
import swaiotos.sal.platform.IDeviceInfo;
import swaiotos.sal.platform.ISystemInfo;
import swaiotos.sal.setting.ISetting;
import swaiotos.sal.storage.IStorage;
import swaiotos.sal.system.ISystem;
import swaiotos.sal.webservice.SALCommonHeader;

public class SalImpl implements ISal {
    private static SalImpl SAL = null;
    private static final String TAG = "SalImpl_Mall";

    private Context mContext;
    private ISetting mISetting = null;
    private IDeviceInfo mIDeviceInfo = null;
    private ISystemInfo mISystemInfo = null;
    private ISystem mSystem = null;
    private IPicture mpic = null;
    private IAudio mAudio = null;
    private IScreen mscreen = null;
    private INetwork mNetwork = null;
    private IStorage mStorage = null;
    private final SystemApi mSystemApi;

    public static SalImpl getSAL(Context c) {
        if (SAL == null) {
            SAL = new SalImpl(c);
        }
        return SAL;
    }

    public SalImpl(Context c) {
        mContext = c;
        swaiotos.sal.SAL.init(c);
        mISetting = swaiotos.sal.SAL.getModule(c, SalModule.SETTING);
        mIDeviceInfo = swaiotos.sal.SAL.getModule(c, SalModule.DEVICE_INFO);
        mISystemInfo = swaiotos.sal.SAL.getModule(c, SalModule.SYSTEM_INFO);
        mSystem = swaiotos.sal.SAL.getModule(c, SalModule.SYSTEM);
        mpic = swaiotos.sal.SAL.getModule(c, SalModule.PICTURE);
        mAudio = swaiotos.sal.SAL.getModule(c, SalModule.AUDIO);
        mscreen = swaiotos.sal.SAL.getModule(c, SalModule.SCREEN);
        mNetwork = swaiotos.sal.SAL.getModule(c, SalModule.NETWORK);
        mStorage = swaiotos.sal.SAL.getModule(c, SalModule.STORAGE);
        mSystemApi = new SystemApi(c);

    }

    @Override
    public String getDeviceChip() {
        return mIDeviceInfo.getChip();
    }

    @Override
    public String getDeviceModel() {
        return mIDeviceInfo.getModel();
    }

    @Override
    public String getDeviceName() {
        return mSystem.getDeviceName();
    }

    @Override
    public String getActiveID() {
        if (!TextUtils.isEmpty(mSystem.getActiveId())) {
            Log.d(TAG, "getActiveID: " + mSystem.getActiveId());
            return mSystem.getActiveId();
        } else {
            Log.d(TAG, "getOneID: " + mSystemApi.getOAID());
            return mSystemApi.getOAID();
        }
    }

    @Override
    public String getBarcode() {
        return mIDeviceInfo.getBarCode();
    }

    @Override
    public Map<String, String> getCommonHeader() {
        return SALCommonHeader.getCommonHeaderMap(mContext);
    }

    @Override
    public boolean startNetSetting() {
        mISetting.startNetSettings();
        return true;
    }


    @Override
    public String getMAC() {
        return mIDeviceInfo.getMac();
    }

    @Override
    public String getVersionName() {
        return mISystemInfo.getVersionName();
    }

    @Override
    public long getVersionCode() {
        return mISystemInfo.getVersionCode();
    }

    @Override
    public String getDeviceBrand() {
        return mIDeviceInfo.getBrand();
    }

    @Override
    public ComponentName getCurrentLauncher() {
        return mSystem.getHomePackageName();
    }

    @Override
    public boolean showNetSettings() {
        mISetting.startNetSettings();
        return true;
    }

    @Override
    public String getPattern() {
        return mIDeviceInfo.getPattern();
    }

    @Override
    public boolean isStoreMode() {
        return mISystemInfo.isStoreModeOn();
    }


    @Override
    public boolean isOLED() {
        return mscreen.isOLED();
    }

    @Override
    public int addNetListener(INetwork.INetworkListener listener) {
        return mNetwork.addNetListener(listener);
    }

    @Override
    public int removeNetListener(INetwork.INetworkListener listener) {
        return mNetwork.removeNetListener(listener);
    }
}
