package com.coocaa.smartmall.detail;

import android.content.Intent;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Build;
import android.os.Bundle;
import android.support.annotation.RequiresApi;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.coocaa.smartmall.R;
import com.coocaa.smartmall.XToast;
import com.coocaa.smartmall.tabui.BaseActivity;
import com.coocaa.mylibrary.api.HttpApi;
import com.coocaa.mylibrary.api.HttpSubscribe;
import com.coocaa.mylibrary.api.HttpThrowable;
import com.coocaa.mylibrary.discover.DetailResult.Detail;
import com.coocaa.mylibrary.discover.DetailResult;
import com.coocaa.smartmall.util.UiUtil;
import com.coocaa.smartmall.util.ViewBuilder;
import com.skyworth.ui.api.SkyWithBGLoadingView;
import com.skyworth.util.Util;

import java.util.List;


/**
 * @ProjectName: FamilyDiscovery
 * @Package: com.coocaa.familydiscovery.detail
 * @ClassName: DetailActivity
 * @Description: 详情页
 * @Author: wangyuehui
 * @CreateDate: 2020/6/16 16:43
 * @UpdateUser: 更新者
 * @UpdateDate: 2020/6/16 16:43
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class DetailActivity extends BaseActivity implements HttpSubscribe<DetailResult> {

    private ImageDetailLayout mImageDetailLayout;
    private VideoDetailLayout mVideoLayout;
    private String mProductId = "";
    private String mSmallImage = "";

    private Detail mProductDetailInfo;
    private SkyWithBGLoadingView loadingView;

    @RequiresApi(api = Build.VERSION_CODES.N)
    @Override
    protected void onCreate(Bundle savedInstanceState) {
//        //去掉标题栏
//        this.requestWindowFeature(Window.FEATURE_NO_TITLE);
//        //设置全屏
//        this.getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
        super.onCreate(savedInstanceState);

//        Util.instence(getApplicationContext());
        Intent disPlayIntent = getIntent();
        mProductId = disPlayIntent.getStringExtra("product_id");
        mSmallImage = disPlayIntent.getStringExtra("image_url");
        if(!TextUtils.isEmpty(mSmallImage)){
            mSmallImage.replace("\\", "");
        }
        loadingView=new SkyWithBGLoadingView(this);
        FrameLayout.LayoutParams layoutParams=new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.gravity= Gravity.CENTER;
        mMainLayout.addView(loadingView,layoutParams);
        loadingView.setVisibility(View.VISIBLE);
        getData();
//        showNotData();
    }
private void getData(){
    loadingView.bringToFront();
    loadingView.showLoading();
    HttpApi.getInstance().getDetail(DetailActivity.this, mProductId);
}
    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            boolean onKeyDown = false;
            if (mVideoLayout != null) {
                onKeyDown = mVideoLayout.onKeyDown(event);
            }
            if (!onKeyDown) {
                return super.dispatchKeyEvent(event);

            } else {
                return true;
            }
        }
        return super.dispatchKeyEvent(event);
    }

    private void showImageDetailLayout() {

        mImageDetailLayout = new ImageDetailLayout(this);
        FrameLayout.LayoutParams layoutParams=new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,ViewGroup.LayoutParams.MATCH_PARENT);
        mMainLayout.addView(mImageDetailLayout,layoutParams);
        Log.i("OKHTTP-LOG", "howImageDetailLayout()--->product_id = " + mProductDetailInfo.getProductId() + "   product_name = " + mProductDetailInfo.getProductName()
                + "    product_price = " + mProductDetailInfo.getPrice());
        mImageDetailLayout.createView(mProductDetailInfo);
    }

    //    @RequiresApi(api = Build.VERSION_CODES.N)
    private void showVideoDetailLayout() {
        mVideoLayout = new VideoDetailLayout(this);
        FrameLayout.LayoutParams layoutParams=new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,ViewGroup.LayoutParams.MATCH_PARENT);
        mMainLayout.addView(mVideoLayout,layoutParams);
        mVideoLayout.setData(mProductDetailInfo);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mVideoLayout != null) {
            mVideoLayout.onResume();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (mVideoLayout != null) {
            mVideoLayout.onPause();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (mImageDetailLayout != null) {
            mImageDetailLayout.onStop();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mVideoLayout != null) {
            mVideoLayout.onDestroy();
        }

    }

    @Override
    public void onSuccess(DetailResult result) {
        loadingView.dismissLoading();
        if (result != null && result.getProduct_detail() != null) {
            mProductDetailInfo = result.getProduct_detail();
            if(TextUtils.isEmpty(mSmallImage)){
                if(mProductDetailInfo.getDisplayType().equals("image")){
                    List<String> images=mProductDetailInfo.getImages();
                    if(images!=null&&images.size()>0){
                        mProductDetailInfo.setImageUrl(images.get(0));
                    }
                }else{
                    mProductDetailInfo.setImageUrl("http://update-nj.skyworth-cloud.com/nj_apk/weixin/20200718103937nzodgs.png");
                }
            }else{
                mProductDetailInfo.setImageUrl(mSmallImage);
            }
            if(!TextUtils.isEmpty(mProductDetailInfo.getLittle_image())){
                mProductDetailInfo.setImageUrl(mProductDetailInfo.getLittle_image());
            }
            String display_type = mProductDetailInfo.getDisplayType();
            Log.d("OKHTTP-LOG", Thread.currentThread().getName());
            if (Detail.DISPLAY_TYPE_IMAGE.equals(display_type)) {//
                showImageDetailLayout();
            } else if (Detail.DISPLAY_TYPE_VIDEO.equals(display_type)) {
                showVideoDetailLayout();
            }
        }else{

            showNotData();
        }
    }

    @Override
    public void onError(HttpThrowable error) {

        loadingView.dismissLoading();
        Log.d("OKHTTP-LOG", "getErrCode = " + error.getErrCode() + "     getErrMsg = " + error.getErrMsg());
        showNotData();
    }
    private LinearLayout errorLayout;
    Button  loadAgainBtn;
    private void showNotData(){
        if(errorLayout==null){
            Typeface typefaceB=Typeface.defaultFromStyle(Typeface.BOLD);
            errorLayout=new LinearLayout(this);
            errorLayout.setOrientation(LinearLayout.VERTICAL);
            FrameLayout.LayoutParams layoutParams=new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,ViewGroup.LayoutParams.WRAP_CONTENT);
            layoutParams.gravity= Gravity.CENTER;
            mMainLayout.addView(errorLayout,layoutParams);
            errorLayout.setGravity(Gravity.CENTER_HORIZONTAL);
            TextView textView= ViewBuilder.initTextView(new TextView(this), Util.Dpi(40), Color.WHITE,1,"页面加载失败，请刷新重试",typefaceB);
            LinearLayout.LayoutParams tvParams=new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,ViewGroup.LayoutParams.WRAP_CONTENT);
            errorLayout.addView(textView,tvParams);


            RelativeLayout btnFocusView = new RelativeLayout(this);
            btnFocusView.setBackground(UiUtil.getDrawable(0, Color.WHITE, Util.Div(3), Util.Div(48)));
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            params.topMargin = Util.Div(42);
            errorLayout.addView(btnFocusView,params);
            btnFocusView.setPadding(Util.Div(5), Util.Div(5), Util.Div(5), Util.Div(5));
            loadAgainBtn = (Button) ViewBuilder.initTextView(new Button(this),Util.Dpi(28),Color.BLACK,1,"刷新",null);
            loadAgainBtn.setGravity(Gravity.CENTER);
            loadAgainBtn.setBackground(UiUtil.getDrawable(getResources().getColor(R.color.white), 0, 0, Util.Div(40)));
            RelativeLayout.LayoutParams paramsLoadAgainBtn = new RelativeLayout.LayoutParams(Util.Div(350), Util.Div(80));
//            params.topMargin = Util.Div(50);
            btnFocusView.addView(loadAgainBtn, paramsLoadAgainBtn);
            loadAgainBtn.requestFocus();
            loadAgainBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    errorLayout.setVisibility(View.GONE);
                    getData();
                }
            });
        }
        errorLayout.setVisibility(View.VISIBLE);
        loadAgainBtn.requestFocus();
        errorLayout.bringToFront();
    }
}
