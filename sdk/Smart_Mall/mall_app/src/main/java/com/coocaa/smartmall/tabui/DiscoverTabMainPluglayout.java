//package com.coocaa.smartmall.tabui;
//
//import android.annotation.SuppressLint;
//import android.content.Context;
//import android.content.Intent;
//import android.net.ConnectivityManager;
//import android.net.NetworkInfo;
//import android.os.Build;
//import android.support.annotation.NonNull;
//import android.support.annotation.Nullable;
//import android.support.v7.widget.NewRecycleAdapter;
//import android.util.AttributeSet;
//import android.util.Log;
//import android.view.Gravity;
//import android.view.KeyEvent;
//import android.view.View;
//import android.view.ViewGroup;
//import android.view.ViewTreeObserver;
//import android.view.animation.Animation;
//import android.view.animation.AnimationUtils;
//import android.widget.FrameLayout;
//import android.widget.LinearLayout;
//import android.widget.Toast;
//
//import com.coocaa.smartmall.R;
//import com.coocaa.smartmall.XNetworkDialog;
//import com.coocaa.smartmall.tabui.recyview.TabDiscoverItemView;
//import com.coocaa.smartmall.tabui.recyview.TabProductLargeView;
//import com.coocaa.smartmall.tabui.recyview.TabProductView;
//import com.coocaa.smartmall.util.ActivityUtils;
//import com.coocaa.smartmall.util.ViewBuilder;
//import com.coocaa.mylibrary.api.HttpApi;
//import com.coocaa.mylibrary.api.HttpSubscribe;
//import com.coocaa.mylibrary.api.HttpThrowable;
//import com.coocaa.mylibrary.discover.RecommandResult.Recommand;
//import com.coocaa.mylibrary.discover.RecommandResult;
//import com.skyworth.smarthome_tv.smarthomeplugininterface.IViewBoundaryCallback;
//import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
//import com.skyworth.ui.newrecycleview.NewRecycleLayout;
//import com.skyworth.ui.newrecycleview.OnBoundaryListener;
//import com.skyworth.ui.newrecycleview.OnItemClickListener;
//import com.skyworth.ui.newrecycleview.OnItemFocusChangeListener;
//import com.skyworth.util.Util;
//
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.List;
//
//public class DiscoverTabMainPluglayout extends FrameLayout implements HttpSubscribe<RecommandResult> {
//    private static final String TAG = "DiscoverTabMainPluglayout";
//
//    private long mLastAnimationXPlugin;
//    private IViewBoundaryCallback mViewBoundaryCallback;
//
//    private NewRecycleAdapter<Recommand> mAdapterPlugin;
//    private NewRecycleLayout<Recommand> mDiscoverNewRecycleLayoutPlugin;
//
//    private LinearLayout mTab_two_large_view_layoutPlugin;
//    private LinearLayout mTabMainLayoutPlugin;
//
//    private TabProductLargeView mFirstLargeViewPlugin, mSecondLargeViewPlugin;
//    private Intent mShowDetailInfoIntentPlugin;
//    private List<Recommand> mAllDiscoverProducts,mAllDiscoverProductsBig;
//    private RecommandResult mTabProduct;
//    private int pageSize=8;
//    public DiscoverTabMainPluglayout(@NonNull Context context, IViewBoundaryCallback callback) {
//        super(context);
//        initView(callback);
//    }
//
//    public DiscoverTabMainPluglayout(@NonNull Context context, @Nullable AttributeSet attrs, IViewBoundaryCallback callback) {
//        super(context, attrs);
//        initView(callback);
//    }
//
//    public DiscoverTabMainPluglayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, IViewBoundaryCallback callback) {
//        super(context, attrs, defStyleAttr);
//        initView(callback);
//    }
//
//    private void initView(IViewBoundaryCallback callback) {
//        setClipChildren(false);
//        setClipToPadding(false);
//        addView(ViewBuilder.getDiscoverTabMainLayout(getContext()));
//        mViewBoundaryCallback = callback;
//    }
//
//    public void createDiscoverTabPluginLayout() {
//        initTabMainViewPlugin();
//        initTabRecyViewPlugin();
//
//        HttpApi.getInstance().getRecommand(DiscoverTabMainPluglayout.this);
//    }
//
//    private void initTabMainViewPlugin() {
//        mTab_two_large_view_layoutPlugin = findViewById(R.id.tab_two_large_view_layout_plugin);
//
//
//        mFirstLargeViewPlugin = new TabProductLargeView(getContext(), true);
//        mSecondLargeViewPlugin = new TabProductLargeView(getContext(), true);
//        mFirstLargeViewPlugin.setVisibility(GONE);
//        mSecondLargeViewPlugin.setVisibility(GONE);
//        mFirstLargeViewPlugin.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                if(!XNetworkDialog.isNetConnected(getContext()))return;
//                if (mAllDiscoverProducts != null && mAllDiscoverProducts.size() > 0) {
//                    ActivityUtils.startDetail(getContext(),mAllDiscoverProductsBig.get(0));
//                }
//            }
//        });
//
//        mFirstLargeViewPlugin.setOnKeyListener(new OnKeyListener() {
//            @Override
//            public boolean onKey(View v, int keyCode, KeyEvent event) {
//                if (event.getAction() == KeyEvent.ACTION_DOWN && mViewBoundaryCallback != null) {
//                    switch (event.getKeyCode()) {
//                        case KeyEvent.KEYCODE_DPAD_UP:
//                            return mViewBoundaryCallback.onTopBoundary(mFirstLargeViewPlugin);
//                        case KeyEvent.KEYCODE_DPAD_LEFT:
//                            return mViewBoundaryCallback.onLeftBoundary(mFirstLargeViewPlugin);
//                        default:
//                            break;
//                    }
//                }
//                return false;
//            }
//        });
//
//        mSecondLargeViewPlugin.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                if(!XNetworkDialog.isNetConnected(getContext()))return;
//                if (mAllDiscoverProducts != null && mAllDiscoverProducts.size() > 1) {
//
//                    ActivityUtils.startDetail(getContext(),mAllDiscoverProductsBig.get(1));
//                }
//            }
//        });
//
//        mFirstLargeViewPlugin.setOnFocusChangeListener(new View.OnFocusChangeListener() {
//            @Override
//            public void onFocusChange(View v, boolean hasFocus) {
//                mFirstLargeViewPlugin.onFocusChange(v, hasFocus);
//            }
//        });
//
//        mSecondLargeViewPlugin.setOnFocusChangeListener(new View.OnFocusChangeListener() {
//            @Override
//            public void onFocusChange(View v, boolean hasFocus) {
//                mSecondLargeViewPlugin.onFocusChange(v, hasFocus);
//            }
//        });
//
//        mSecondLargeViewPlugin.setOnKeyListener(new OnKeyListener() {
//            @Override
//            public boolean onKey(View v, int keyCode, KeyEvent event) {
//                if (event.getAction() == KeyEvent.ACTION_DOWN && mViewBoundaryCallback != null) {
//                    switch (event.getKeyCode()) {
//                        case KeyEvent.KEYCODE_DPAD_UP:
//                            return mViewBoundaryCallback.onTopBoundary(mSecondLargeViewPlugin);
//                        case KeyEvent.KEYCODE_DPAD_RIGHT:
//                            return mViewBoundaryCallback.onRightBoundary(mSecondLargeViewPlugin);
//                        default:
//                            break;
//                    }
//                }
//                return false;
//            }
//        });
//
//        //以下两个是在平板上的处理，显示效果很正常，电视上的还要调整大小和leftMargin以及topMargin的大小
//        LinearLayout.LayoutParams first_largeViewparams =
//                new LinearLayout.LayoutParams(Util.Div(860 + 10), Util.Div(400 + 10));
//        mTab_two_large_view_layoutPlugin.addView(mFirstLargeViewPlugin, first_largeViewparams);
//
//        LinearLayout.LayoutParams second_largeViewparams =
//                new LinearLayout.LayoutParams(Util.Div(860 + 10), Util.Div(400 + 10));
//        second_largeViewparams.leftMargin = Util.Div(38 - 10);
//        mTab_two_large_view_layoutPlugin.addView(mSecondLargeViewPlugin, second_largeViewparams);
//        mFirstLargeViewPlugin.setFocusableInTouchMode(true);
////        post(new Runnable() {
////            @Override
////            public void run() {
////                mFirstLargeViewPlugin.requestFocus();
////            }
////        });
////        mFirstLargeViewPlugin.getViewTreeObserver().addOnDrawListener(new ViewTreeObserver.OnDrawListener() {//绘制完成后聚焦
////            @Override
////            public void onDraw() {
////
////                mFirstLargeViewPlugin.getViewTreeObserver().removeOnDrawListener(this);
////            }
////        });
//    }
//
//    private void initTabRecyViewPlugin() {
//        mTabMainLayoutPlugin = findViewById(R.id.tab_main_layout_plugin);
//        mDiscoverNewRecycleLayoutPlugin = new NewRecycleLayout<Recommand>(getContext());
//        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(Util.Div(1800), ViewGroup.LayoutParams.WRAP_CONTENT);
//        params.gravity = Gravity.CENTER_HORIZONTAL;
//        params.leftMargin = Util.Div(10);
//        mTabMainLayoutPlugin.addView(mDiscoverNewRecycleLayoutPlugin, params);
//
//        mDiscoverNewRecycleLayoutPlugin.setOrientation(LinearLayout.VERTICAL);
//        mDiscoverNewRecycleLayoutPlugin.setSpanCount(4);
//        mDiscoverNewRecycleLayoutPlugin.setClipChildren(false);
//        mDiscoverNewRecycleLayoutPlugin.setClipToPadding(false);
//        mDiscoverNewRecycleLayoutPlugin.setItemSpace(Util.Div(0), Util.Div(38 - 10));
////        mDiscoverNewRecycleLayoutPlugin.setPadding(0,0,22,0);
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
//            mDiscoverNewRecycleLayoutPlugin.setNestedScrollingEnabled(false);
//        }
//
//        //抖动效果
//        mDiscoverNewRecycleLayoutPlugin.setmBoundaryListener(new OnBoundaryListener() {
//            @Override
//            public boolean onLeftBoundary(View leaveView, int position) {
////                lastXAnimationPlugin(leaveView,getContext());
//                if (mViewBoundaryCallback == null)
//                    return false;
//                return mViewBoundaryCallback.onLeftBoundary(leaveView);
//            }
//
//            @Override
//            public boolean onTopBoundary(View leaveView, int position) {
////              return  mTab_two_large_view_layoutPlugin.requestFocus();
//////                lastYAnimationPlugin(leaveView,getContext());
////                if (mViewBoundaryCallback == null)
////                    return false;
//                return mFirstLargeViewPlugin.requestFocus();
////                return mViewBoundaryCallback.onTopBoundary(leaveView);
//            }
//
//            @Override
//            public boolean onDownBoundary(View leaveView, int position) {
////                lastYAnimationPlugin(leaveView,getContext());
//                if (mViewBoundaryCallback == null)
//                    return true;
//                mViewBoundaryCallback.onDownBoundary(leaveView);
//                return true;
//            }
//
//            @Override
//            public boolean onRightBoundary(View leaveView, int position) {
////                lastXAnimationPlugin(leaveView,getContext());
//                if (mViewBoundaryCallback == null)
//                    return false;
//                return mViewBoundaryCallback.onRightBoundary(leaveView);
//            }
//
//            @SuppressLint("LongLogTag")
//            @Override
//            public boolean onOtherKeyEvent(View v, int position, int keyCode) {
//                Log.d(TAG, "--------------XXXX");
//                return false;
//            }
//
//        });
//
//        mDiscoverNewRecycleLayoutPlugin.setmItemClickListener(new OnItemClickListener() {
//            @Override
//            public void click(View view, int position) {
//                if(!XNetworkDialog.isNetConnected(getContext()))return;
//                if (view instanceof TabDiscoverItemView) {
//                    if (mAllDiscoverProducts != null && mAllDiscoverProducts.size() > 0) {
//                        NewRecycleAdapter adapter=mDiscoverNewRecycleLayoutPlugin.getRecyclerAdapter();
//                        if(adapter!=null){
//                            List<Recommand> list=  adapter.getData();
//                            if(list!=null&&list.size()>position){
//                                ActivityUtils.startDetail(getContext(),list.get(position));
//                            }
//                        }
//                    }
//                }
//            }
//        });
//
//        mDiscoverNewRecycleLayoutPlugin.setmItemFocusChangeListener(new OnItemFocusChangeListener() {
//            @Override
//            public void focusChange(View v, int position, boolean hasFocus) {
//                ((TabDiscoverItemView) v).onFocusChange(v, hasFocus);
//            }
//        });
//    }
//
//
//    private void lastXAnimationPlugin(View view, Context mActivity) {
//        long duration = AnimationUtils.currentAnimationTimeMillis() - mLastAnimationXPlugin;
//        if (duration > 500) {
//            Animation animation = AnimationUtils.loadAnimation(mActivity,
//                    R.anim.user_share_shake);
//            view.startAnimation(animation);
//            mLastAnimationXPlugin = AnimationUtils.currentAnimationTimeMillis();
//        }
//    }
//
//    private void lastYAnimationPlugin(View view, Context mActivity) {
//        long duration = AnimationUtils.currentAnimationTimeMillis() - mLastAnimationXPlugin;
//        if (duration > 500) {
//            Animation animation = AnimationUtils.loadAnimation(mActivity,
//                    R.anim.user_share_shake_y);
//            view.startAnimation(animation);
//            mLastAnimationXPlugin = AnimationUtils.currentAnimationTimeMillis();
//        }
//    }
//
//    private void refreshTwoLargeView(List<Recommand> deviceBeans) {
//        if (deviceBeans != null && deviceBeans.size() > 0) {
//            if (deviceBeans.size() == 1) {
//                mFirstLargeViewPlugin.setVisibility(VISIBLE);
//                mFirstLargeViewPlugin.refreshUI(deviceBeans.get(0));
//            } else {
//                mFirstLargeViewPlugin.refreshUI(deviceBeans.get(0));
//                mSecondLargeViewPlugin.refreshUI(deviceBeans.get(1));
//                mSecondLargeViewPlugin.setVisibility(VISIBLE);
//                mFirstLargeViewPlugin.setVisibility(VISIBLE);
//            }
//        }
//    }
//
//    private void bindAllProductDataToItemViewPlugin(List<Recommand> deviceBeans) {
//
//        if (deviceBeans != null && deviceBeans.size() > 0) {
//            deviceBeans.remove(0);
//            deviceBeans.remove(0);
//            mAdapterPlugin = new NewRecycleAdapter<Recommand>(deviceBeans, 1) {
//                @Override
//                public NewRecycleAdapterItem<Recommand> onCreateItem(Object type) {
//                    return new TabProductView(getContext(), false);
//                }
//            };
//            mDiscoverNewRecycleLayoutPlugin.setRecyclerAdapter(mAdapterPlugin);
////            mDiscoverNewRecycleLayoutPlugin.setSelection(0);
//        }
//    }
//
//    @Override
//    public void onSuccess(RecommandResult result) {
//        if (result != null && result != null) {
//            mTabProduct = result;
//            mAllDiscoverProducts = mTabProduct.getDataResult();
//
//            if (mAllDiscoverProducts != null && mAllDiscoverProducts.size() > 0) {
//                mAllDiscoverProductsBig=new ArrayList<>();
//                mAllDiscoverProductsBig.add(mAllDiscoverProducts.get(0));
//                if(mAllDiscoverProducts.size()>=2){
//                    mAllDiscoverProductsBig.add(mAllDiscoverProducts.get(1));
//                }
//                refreshTwoLargeView(mAllDiscoverProductsBig);
//                if(mAllDiscoverProducts.size()>2){
//                    bindAllProductDataToItemViewPlugin(mAllDiscoverProducts);
//                }
//            }
//        }
//    }
//
//    @SuppressLint("LongLogTag")
//    @Override
//    public void onError(HttpThrowable error) {
//        Log.d(TAG, "获取推荐列表失败");
//        Toast.makeText(getContext(), "获取推荐列表失败", Toast.LENGTH_LONG).show();
//    }
//}
