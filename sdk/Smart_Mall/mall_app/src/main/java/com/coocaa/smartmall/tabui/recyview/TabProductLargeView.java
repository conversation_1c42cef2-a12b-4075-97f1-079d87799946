package com.coocaa.smartmall.tabui.recyview;

import android.content.Context;
import android.view.View;

import com.coocaa.mylibrary.discover.RecommandResult.Recommand;

public class TabProductLargeView extends TabDiscoverItemView{
    public TabProductLargeView(Context context, boolean lsLargeItemView) {
        super(context, lsLargeItemView);
    }

    @Override
    public void destroy() {
        super.destroy();
    }

    public void refreshLageItemView(Recommand discoverProduct){
        refreshUI(discoverProduct);
    }

    public void onLargeViewFocusChange(View view, boolean hasFocus){
        onFocusChange(view , hasFocus);
    }
}
