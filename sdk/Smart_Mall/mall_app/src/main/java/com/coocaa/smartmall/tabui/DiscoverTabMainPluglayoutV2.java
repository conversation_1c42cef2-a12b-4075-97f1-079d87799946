package com.coocaa.smartmall.tabui;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.support.annotation.NonNull;
import android.support.v7.widget.GridLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.Toast;

import com.alibaba.fastjson.JSONObject;
import com.coocaa.mylibrary.api.HttpApi;
import com.coocaa.mylibrary.api.HttpSubscribe;
import com.coocaa.mylibrary.api.HttpThrowable;
import com.coocaa.mylibrary.discover.RecommandResult;
import static com.coocaa.mylibrary.discover.RecommandResult.Recommand;
import com.coocaa.smartmall.XNetworkDialog;
import com.coocaa.smartmall.tabadepter.ReclclerViewBaseAdapter;
import com.coocaa.smartmall.tabadepter.RecommendAdapter;
import com.coocaa.smartmall.util.ActivityUtils;
import com.coocaa.smartmall.util.ImageUtils;
import com.coocaa.smartmall.util.StringUtils;
import com.skyworth.smarthome_tv.smarthomeplugininterface.IViewBoundaryCallback;
import com.skyworth.util.Util;
import java.util.ArrayList;
import java.util.List;

/**
 * 重构新版本
 */
public class DiscoverTabMainPluglayoutV2 extends FrameLayout {
    String TAG="DiscoverlayoutV2";
    RecyclerView recyclerView;
    RecommendAdapter<RecommandResult.Recommand> adapter;
    private IViewBoundaryCallback mViewBoundaryCallback;
    private NetworkChangeReceiver networkChangeReceiver;

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if(networkChangeReceiver==null) {
            IntentFilter  intentFilter = new IntentFilter();
            intentFilter.addAction("android.net.conn.CONNECTIVITY_CHANGE");
            networkChangeReceiver = new NetworkChangeReceiver();
            getContext().registerReceiver(networkChangeReceiver,intentFilter);
            Log.i(TAG,"开始监听网络状态");
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if(networkChangeReceiver!=null){
            getContext().unregisterReceiver(networkChangeReceiver);
            networkChangeReceiver=null;
        }
    }

    public DiscoverTabMainPluglayoutV2(@NonNull Context context, IViewBoundaryCallback callback) {
        super(context);
        this.mViewBoundaryCallback = callback;
    }

    public void createDiscoverTabPluginLayout() {
        setClipChildren(false);
        setClipToPadding(false);
        initCache();
        getData(pageIndex,pageSize);
    }
    HttpSubscribe<RecommandResult> callBack;
    private void getData(final int page,int pageSize ){
        if (!XNetworkDialog.isNetConnected(getContext()))return;
        callBack=new HttpSubscribe<RecommandResult>() {
            @Override
            public void onSuccess(RecommandResult result) {
                callBack=null;
                if (result == null||result.getDataResult()==null||result.getDataResult().size()<=0) {
                    Log.i(TAG,"網絡數據 為空 page="+page);
                    return;
                }

                if(page==1) {//緩存第一頁數據
                    String str=StringUtils.getJsonString(result);
                    if(isCache&&cacheListStr.equals(str)){//缓存数据和网络数据一样，不需要刷新
                        isCache=false;
                        return;
                    }
                    SharedPreferences sp=getContext().getSharedPreferences("recommed",Context.MODE_PRIVATE);
                    sp.edit().putString("list",str).commit();
                    Log.i(TAG,"save cache "+str);
                }
                List<Recommand> list=adapter.getDatas();
                if(isCache){//清除掉緩存数据
                    list.clear();
                    isCache=false;
                }
                list.addAll(result.getDataResult());
                totalSize=result.getTotal();
                adapter.notifyDataSetChanged();
            }

            @Override
            public void onError(HttpThrowable error) {
                callBack=null;
            }
        };
        HttpApi.getInstance().getRecommand(callBack, page, pageSize);
    }
    String cacheListStr;
    private void initCache(){
        SharedPreferences sp=getContext().getSharedPreferences("recommed",Context.MODE_PRIVATE);
        cacheListStr=sp.getString("list","");//拿緩存
        if(TextUtils.isEmpty(cacheListStr)){
            cacheListStr=StringUtils.cacheListDefaultStr;//没有缓存就拿本地数据
        }
        List<Recommand> cacheList=null;
        RecommandResult recommandResult = (RecommandResult) StringUtils.parseObject(cacheListStr,RecommandResult.class);
        if(recommandResult!=null){
            cacheList=recommandResult.getDataResult();
        }
        if(cacheList!=null&&cacheList.size()>0){
            isCache=true;
        }else{
            cacheList=new ArrayList<>();
        }
        initUI(cacheList);
    }

    int pageIndex=1;
    int pageSize=30;
    int totalSize=0;
    int targetPageIndex;
    boolean isCache;
    private void loadMore(){
        if(isCache)return;
        int childCount=adapter.getItemCount();
        if(childCount<totalSize){
            targetPageIndex= childCount/pageSize+1;
            if(targetPageIndex==pageIndex){
                return;
            }
            pageIndex=targetPageIndex;
            getData(pageIndex,pageSize);
        }
    }
    private void initUI(List<RecommandResult.Recommand> datas) {
        recyclerView = new RecyclerView(getContext());
        recyclerView.addItemDecoration(new ReclclerViewBaseAdapter.GridDivider(Util.Div(30)));
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(LayoutParams.WRAP_CONTENT, FrameLayout.LayoutParams.MATCH_PARENT);
        layoutParams.leftMargin = -Util.Div(30);
        layoutParams.topMargin = -Util.Div(5);
        addView(recyclerView, layoutParams);
        GridLayoutManager manager = new GridLayoutManager(getContext(), 4);
        recyclerView.setLayoutManager(manager);
        recyclerView.setClipToPadding(false);
        recyclerView.setClipChildren(false);
        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);

                if(recyclerView.canScrollVertically(-1)){
                    loadMore();
                }else {
                }
            }
        });
        manager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                if (position < 2) {
                    return 2;
                } else {
                    return 1;
                }
            }
        });

        adapter = new RecommendAdapter<RecommandResult.Recommand>() {
            @Override
            public void update(RecyclerView.ViewHolder holder, RecommandResult.Recommand data) {
                ViewHolder viewHolder = (ViewHolder) holder;
                viewHolder.name.setText(data.getProductName());
                int radiu = Util.Div(8);
                if (viewHolder.position < 2) {
                    ImageUtils.loadImage(viewHolder.icon, data.getProductImage(), viewHolder.picW, viewHolder.picH, radiu, radiu, radiu, radiu);
                } else {
                    ImageUtils.loadImage(viewHolder.icon, data.getProductImage(), viewHolder.picW, viewHolder.picH, 0, radiu, radiu, 0);
                }
            }

            @Override
            public void onItemClick(View itemView, int position) {
                if (!XNetworkDialog.isNetConnected(itemView.getContext())) {
                    XNetworkDialog.showConnectNetDialog(getContext());
                    return;
                }
                if (adapter.getItemCount() > 0) {
                    List<RecommandResult.Recommand> list = adapter.getDatas();
                    ActivityUtils.startDetail(getContext(), list.get(position));
                }
            }

            @Override
            public boolean onItemKey(View v, int keyCode, KeyEvent event, int position) {
                 if(mViewBoundaryCallback==null)return false;
                if (event.getAction() == KeyEvent.ACTION_DOWN) {
                    switch (event.getKeyCode()) {
                        case KeyEvent.KEYCODE_DPAD_UP:
                            View nextFocsu = v.focusSearch(FOCUS_UP);
                            if (nextFocsu == null || nextFocsu == v) {
                                 mViewBoundaryCallback.onTopBoundary(v);
                                return true;
                            }
                            break;
                        case KeyEvent.KEYCODE_DPAD_LEFT:
                            nextFocsu = v.focusSearch(FOCUS_LEFT);
                            if (nextFocsu == null || nextFocsu == v) {
                                 mViewBoundaryCallback.onLeftBoundary(v);
                                return true;
                            }

                            break;
                        case KeyEvent.KEYCODE_DPAD_RIGHT:
                            nextFocsu = v.focusSearch(FOCUS_RIGHT);
                            if (nextFocsu == null || nextFocsu == v||position==adapter.getItemCount()-1) {
                                mViewBoundaryCallback.onRightBoundary(v);
                                return true;
                            }
                            break;
                        case KeyEvent.KEYCODE_DPAD_DOWN:
                            int itemCount = adapter.getItemCount();
                            int line = (itemCount - 2) / 4;
                            if ((itemCount - 2) % 4 != 0) {
                                line++;
                            }
                            int lastLinePos = 0;
                            if (line >= 1) {
                                lastLinePos = (line - 1) * 4 + 2;//计算是否是最后一行
                            }
                            nextFocsu = v.focusSearch(FOCUS_DOWN);
                            if (position >= lastLinePos || nextFocsu == null || nextFocsu == v) {
                                 mViewBoundaryCallback.onDownBoundary(v);
                                return true;
                            }
                            break;
                        default:
                            break;
                    }
                }
                return false;
            }
        };
        adapter.setDatas(datas);
        recyclerView.setAdapter(adapter);
        adapter.notifyDataSetChanged();
    }
    class NetworkChangeReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            // 获取管理网络连接的系统服务类的实例
            ConnectivityManager connectivityManager = (ConnectivityManager) getContext().getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
            // 判断网络是否可用
            if (networkInfo != null && networkInfo.isAvailable()){
                if(isCache&&callBack==null){//如果使用的是缓存数据并且没有在请求中，就获取网络数据
                    Log.i(TAG,"网络连接成功，开始刷新数据");
                    getData(pageIndex,pageSize);
                }
            }

        }
    }
}
