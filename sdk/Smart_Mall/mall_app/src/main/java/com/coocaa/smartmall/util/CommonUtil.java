package com.coocaa.smartmall.util;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.graphics.Point;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.view.Display;
import android.view.WindowManager;

import static com.coocaa.smartmall.util.OnClickData.DOWHAT_SEND_BROADCAST;
import static com.coocaa.smartmall.util.OnClickData.DOWHAT_START_ACTIVITY;
import static com.coocaa.smartmall.util.OnClickData.DOWHAT_START_SERVICE;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2019-12-24
 */
public class CommonUtil {

    public static <T> T getMetaData(Context context, String pkgName, String key) {
        return getMetaData(context, pkgName, key, null);
    }

    public static <T> T getMetaData(Context context, String pkgName, String key, T defaultValue) {
        try {
            ApplicationInfo applicationInfo = context.getPackageManager().getApplicationInfo(pkgName, PackageManager.GET_META_DATA);
            if (applicationInfo.metaData != null) {
                return (T) applicationInfo.metaData.get(key);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return defaultValue;
    }

    /**
     * 获取屏幕分辨率
     *
     * @param context
     * @return
     */
    public static String getResolution(Context context) {
        Display display = ((WindowManager) context.getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay();
        if (display == null) {
            return "";
        }
        Point point = new Point();
        display.getRealSize(point);
        int screenWidth = point.x;
        int screenHeight = point.y;
        return screenWidth + "x" + screenHeight;
    }

    /**
     * 检查网络是否可用
     *
     * @param context
     * @return
     */
    public static boolean isNetConnect(Context context) {
        ConnectivityManager manager = (ConnectivityManager) context.getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE);
        if (manager == null) {
            return false;
        }
        NetworkInfo networkinfo = manager.getActiveNetworkInfo();
        return networkinfo != null && networkinfo.isAvailable();
    }

    public static void jumpOperateData(Context context, OnClickData data) {
        try {
            if (data != null && data.dowhat != null && !data.dowhat.equals("") && !data.dowhat.equals("null")) {
                Intent intent = data.buildIntent(context);
                if (intent != null) {
                    try {
                        switch (data.dowhat) {
                            case DOWHAT_START_ACTIVITY:
                                context.startActivity(intent);
                                break;
                            case DOWHAT_START_SERVICE:
                                context.startService(intent);
                                break;
                            case DOWHAT_SEND_BROADCAST:
                                context.sendBroadcast(intent);
                                break;
                            default:
                                break;
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        if (data.exception != null) {
                            jumpOperateData(context, data.exception);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}

