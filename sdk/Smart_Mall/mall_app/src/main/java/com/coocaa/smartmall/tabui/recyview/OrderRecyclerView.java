package com.coocaa.smartmall.tabui.recyview;

import android.content.Context;
import android.graphics.Canvas;
import android.support.annotation.Nullable;
import android.support.v7.widget.RecyclerView;
import android.util.AttributeSet;

public class OrderRecyclerView extends RecyclerView {
    public OrderRecyclerView(Context context) {
        super(context);
    }

    public OrderRecyclerView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public OrderRecyclerView(Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    @Override
    protected int getChildDrawingOrder(int childCount, int i) {
        int position = mSelectedPosition;
        if (position < 0) {
            return i;
        } else {
            if (i == childCount - 1) {
                if (position > i) {
                    position = i;
                }
                return position;
            }
            if (i == position) {
                return childCount - 1;
            }
        }
        return i;
    }
    int mSelectedPosition=0;
    @Override
    public void onDraw(Canvas c) {
        setChildrenDrawingOrderEnabled(true);
        mSelectedPosition = getChildAdapterPosition(getFocusedChild());
        super.onDraw(c);
    }
}
