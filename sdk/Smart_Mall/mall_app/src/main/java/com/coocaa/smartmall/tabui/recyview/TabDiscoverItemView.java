package com.coocaa.smartmall.tabui.recyview;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.coocaa.smartmall.R;
import com.coocaa.smartmall.util.ImageUtils;
import com.coocaa.mylibrary.discover.RecommandResult.Recommand;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;

public abstract class TabDiscoverItemView extends FrameLayout {
    protected final static String TAG = "TabDiscoverItemView";
    protected View mBgView;
//    protected View mFocusView;
    protected View mProductImageView;
    protected TextView mProductDescription;
    protected TextView mProductName;
    protected Recommand mProductData;
    private RelativeLayout mLayout;
    private RelativeLayout mFocusView;

    public boolean mIsLargeItemView;//表示的是主页推荐列表的前两张大图

    public TabDiscoverItemView(Context context , boolean lsLargeItemView) {
        super(context);
        setClipChildren(false);
        setClipToPadding(false);
        mIsLargeItemView = lsLargeItemView;
//        setPadding(Util.Div(8),Util.Div(8),Util.Div(8),Util.Div(8));
        if(mIsLargeItemView){
//            setLayoutParams(new FrameLayout.LayoutParams(Util.Div(860 + 16), Util.Div(400 + 16)));
            mLayout = new RelativeLayout(getContext());
            LayoutParams params = new LayoutParams(Util.Div(860), Util.Div(400));
            params.gravity = Gravity.CENTER;
            addView(mLayout,params);

            mFocusView = new RelativeLayout(getContext());
            LayoutParams mFocusViewparams = new LayoutParams(Util.Div(860+12), Util.Div(400+12));
            mFocusViewparams.gravity = Gravity.CENTER;
            addView(mFocusView,mFocusViewparams);

        } else {
//            setLayoutParams(new FrameLayout.LayoutParams(Util.Div(410 + 16), Util.Div(293 + 16)));
            mLayout = new RelativeLayout(getContext());
            mLayout.setClipChildren(false);
            mLayout.setClipToPadding(false);
            LayoutParams params = new LayoutParams(Util.Div(410), Util.Div(293));
            params.gravity = Gravity.CENTER;
            addView(mLayout,params);

            mFocusView = new RelativeLayout(getContext());
            LayoutParams mFocusViewparams = new LayoutParams(Util.Div(410+12), Util.Div(293+12));
            mFocusViewparams.gravity = Gravity.CENTER;
            addView(mFocusView,mFocusViewparams);
        }
        mLayout.setBackground(getDefultBackGround());
        setClipChildren(false);
        setClipToPadding(false);
        setFocusable(true);
        setPressed(true);
        setClickable(true);
        addLargeImageViewOrItemView();
    }

    private void addLargeImageViewOrItemView(){
        if(mIsLargeItemView){
            //图片
            mProductImageView = ImageLoader.getLoader().getView(getContext());
            LayoutParams params2 = new LayoutParams(Util.Div(860), Util.Div(400));
            mProductImageView.setBackground(getDefultBackGround());
            mLayout.addView(mProductImageView, params2);

            //渐变效果
            GradientDrawable aDrawable =new GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM,
                    new int[]{android.R.color.transparent,R.color.black_50});
            float[] radii = {0f,0f,0f,0f,10f,10f,10f,10f};
            aDrawable.setCornerRadii(radii);

            RelativeLayout contentLayout = new RelativeLayout(getContext());
//            contentLayout.setBackground(aDrawable);
            RelativeLayout.LayoutParams contentRL = new RelativeLayout.LayoutParams(
                    RelativeLayout.LayoutParams.MATCH_PARENT,Util.Div(200));
            contentRL.topMargin = Util.Div(200);
            mLayout.addView(contentLayout,contentRL);

            mProductDescription = new TextView(getContext());
            mProductDescription.setTextColor(Color.WHITE);
            mProductDescription.setTextSize(Util.Dpi(28));
            mProductDescription.setVisibility(GONE);
            RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                    RelativeLayout.LayoutParams.WRAP_CONTENT);
            params.leftMargin = Util.Div(30);
            params.topMargin = Util.Div(338);

            mLayout.addView(mProductDescription, params);

        }else{
            mProductImageView = ImageLoader.getLoader().getView(getContext());
            LayoutParams imageParams = new LayoutParams(Util.Div(410), Util.Div(230));
            mLayout.addView(mProductImageView , imageParams);

            mProductName = new TextView(getContext());
            mProductName.setTextColor(Color.WHITE);
            mProductName.setTextSize(Util.Dpi(22));
            RelativeLayout.LayoutParams textParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                    RelativeLayout.LayoutParams.WRAP_CONTENT);
            textParams.leftMargin = Util.Div(20);
            textParams.topMargin = Util.Div(230+15);
            mLayout.addView(mProductName, textParams);
        }
    }

    private void addDescriptionView(){

    }

    private void addNameAndImageLayout() {

    }

    public void refreshUI(Recommand productData) {

        if (productData == null) {
            return;
        }

        try {

            mProductData = productData;

            if(productData.getProductDescription()!=null && !"".equals(productData.getProductDescription()) && mIsLargeItemView){
                if(mProductDescription!=null){
                    mProductDescription.setText(productData.getProductDescription());
                }
            }

            if(productData.getProductName()!=null && !"".equals(productData.getProductName()) && !mIsLargeItemView){
                if(mProductName!=null){
                    mProductName.setText(productData.getProductName());
                }
            }

            if (mProductImageView!=null && productData.getProductImage()!=null && !"".equals(productData.getProductImage())) {
                Log.d(TAG , "eeeee");
                mProductImageView.setBackground(null);

                if (mIsLargeItemView) {
                    ImageUtils.loadImage(mProductImageView,productData.getProductImage(),Util.Div(860), Util.Div(400),
                            Util.Div(8),Util.Div(8),Util.Div(8),Util.Div(8));
                } else {
                    ImageUtils.loadImage(mProductImageView,productData.getProductImage(),Util.Div(410), Util.Div(230),
                            Util.Div(0),Util.Div(8),Util.Div(0),Util.Div(8));
                }

                if(mIsLargeItemView){
                    Log.d(TAG , "fffff");
                    //ImageLoader.getLoader().with(getContext()).load(productData.getImageUrl()).resize(Util.Div(860), Util.Div(400)).setScaleType(ImageView.ScaleType.FIT_XY).into(mProductImageView);
                }else{
                    Log.d(TAG , "ggggg");
                    //ImageLoader.getLoader().with(getContext()).load(productData.getImageUrl()).resize(Util.Div(410), Util.Div(230)).setScaleType(ImageView.ScaleType.FIT_XY).into(mProductImageView);
                }
            } else {
                Log.d(TAG , "hhhhh");
//                mProductImageView.setBackground(defaultBg);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onFocusChange(View view, boolean hasFocus) {
        if (hasFocus) {
            mFocusView.setBackground(getFocused(getContext(),Util.Div(8)));
        } else {
            mFocusView.setBackgroundColor(getContext().getResources().getColor(android.R.color.transparent));
        }
        Util.focusAnimate(view, hasFocus);
    }

    private GradientDrawable getFocused(Context context,int radius) {
        GradientDrawable FocusedDrawable = new GradientDrawable();
        FocusedDrawable.setShape(GradientDrawable.RECTANGLE);
        FocusedDrawable.setCornerRadius(radius);
        FocusedDrawable.setStroke(Util.Div(4),context.getResources().getColor(R.color.white_100));
        return FocusedDrawable;
    }

    private GradientDrawable getDefultBackGround() {
        GradientDrawable BackDrawable = new GradientDrawable();
        BackDrawable.setShape(GradientDrawable.RECTANGLE);
        BackDrawable.setCornerRadius(Util.Div(8));
        BackDrawable.setColor(getContext().getResources().getColor(R.color.white_10));
        return BackDrawable;
    }

    public void destroy() {
//        defaultBg = null;
    }
}
