package com.coocaa.smartmall.tabadepter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.support.v7.widget.RecyclerView;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.coocaa.smartmall.R;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.fresco.CoocaaDraweeView;

public abstract class RecommendAdapter<T> extends ReclclerViewBaseAdapter<T> {

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new ViewHolder(parent.getContext());
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, @SuppressLint("RecyclerView") int position) {
        ViewHolder viewHolder= (ViewHolder) holder;
        viewHolder.position=position;
        GradientDrawable BackDrawable = new GradientDrawable();
        BackDrawable.setShape(GradientDrawable.RECTANGLE);
        BackDrawable.setColor(viewHolder.icon.getResources().getColor(R.color.black_10));
        float radius= Util.Div(8);
        RelativeLayout.LayoutParams layoutParams= (RelativeLayout.LayoutParams) viewHolder.icon.getLayoutParams();
        if(getItemViewType(position)==0){
            viewHolder.picW=Util.Div(860);
            viewHolder.picH=Util.Div(400);
            viewHolder.name.setVisibility(View.GONE);
            BackDrawable.setCornerRadii(new float[]{radius,radius,radius,radius,radius,radius,radius,radius});
        }else{
            viewHolder.picW=Util.Div(410);
            viewHolder.picH=Util.Div(230);
            BackDrawable.setCornerRadii(new float[]{radius,radius,radius,radius,0,0,0,0});
        }
        layoutParams.width= viewHolder.picW;
        layoutParams.height= viewHolder.picH;
        viewHolder.icon.requestLayout();



        viewHolder.icon.setBackground(BackDrawable);
        update(holder,getData(position));
//        if(position==0){
//            holder.itemView.requestFocus();
//        }
    }

    @Override
    public int getItemViewType(int position) {
        if(position<2)return 0;
        else return 1;
    }


   public class ViewHolder extends ReclclerViewBaseAdapter<T>.ViewHolder {
       public int picW,picH;
       public CoocaaDraweeView icon;
       public TextView name;
       public ViewHolder(Context context) {
           super(new RelativeLayout(context));
           initView((ViewGroup) itemView);
       }
       public  View initView(ViewGroup parent){
           int padd=Util.Div(5);
           parent.setPadding(padd,padd,padd,padd);
           Context context=parent.getContext();
           picW=Util.Div(410);
           picH=Util.Div(230);

           icon=new CoocaaDraweeView(context);
           icon.setId(R.id.icon);
           ViewGroup.LayoutParams layoutParams=new ViewGroup.LayoutParams(picW,picH);
           parent.addView(icon,layoutParams);

           name=new TextView(context);
           name.setId(R.id.name);
           name.setSingleLine();
           name.setEllipsize(TextUtils.TruncateAt.MARQUEE);
           RelativeLayout.LayoutParams layoutParamsText=new RelativeLayout.LayoutParams(picW,ViewGroup.LayoutParams.WRAP_CONTENT);
           int textPadding=Util.Div(22);
           name.setPadding(Util.Div(20),Util.Div(15),Util.Div(20),Util.Div(20));
           name.setTextColor(Color.WHITE);
           name.setTextSize(Util.Dpi(28));
           layoutParamsText.addRule(RelativeLayout.BELOW,icon.getId());
           parent.addView(name,layoutParamsText);
           GradientDrawable BackDrawable = new GradientDrawable();
           BackDrawable.setShape(GradientDrawable.RECTANGLE);
           float radius=Util.Div(8);
           BackDrawable.setColor(context.getResources().getColor(R.color.black_10));
           BackDrawable.setCornerRadii(new float[]{0,0,0,0,radius,radius,radius,radius});
           name.setBackground(BackDrawable);
           textNormalBg=BackDrawable;
           GradientDrawable drawable = new GradientDrawable();
           drawable.setShape(GradientDrawable.RECTANGLE);
           drawable.setColor(context.getResources().getColor(R.color.white));
           drawable.setCornerRadii(new float[]{0,0,0,0,radius,radius,radius,radius});
           textSelectBg=drawable;
           return parent;
       }

       @Override
       public void onFocusChange(View v, boolean hasFocus) {
           super.onFocusChange(v, hasFocus);
           Util.focusAnimate(itemView, hasFocus);
           int coclorWhite=v.getContext().getResources().getColor(R.color.white);
           int colorWhite0=v.getContext().getResources().getColor(R.color.white_0);
           name.setSelected(hasFocus);
           if(hasFocus){
               name.setBackground(textSelectBg);
               name.setTextColor(Color.BLACK);
               v.setBackground(getFocused(Util.Div(3),Util.Div(2),coclorWhite,colorWhite0,v.getContext(),Util.Div(8)));
           }else{
               name.setBackground(textNormalBg);

               name.setTextColor(Color.WHITE);
               v.setBackground(null);
           }
   }
   }

    @Override
    public void onItemClick(View itemView, int position) {

    }
    private Drawable textSelectBg,textNormalBg;
    @Override
    public void onItemFocus(View itemView, boolean hasFocus, int position) {

    }

    @Override
    public boolean onItemKey(View v, int keyCode, KeyEvent event, int position) {
      return false;
    }

    private Drawable getFocused(int borderWidth, int grapWidth, int colorBorder, int colorSolid, Context context, float ...radiu) {

        CCFocusDrawable drawable=new CCFocusDrawable(context).setSolidColor(colorSolid);
        if(borderWidth>0){
            drawable.setBorderWidth(borderWidth);
            drawable.setBorderColor(colorBorder);
        }else{
            drawable.setBorderVisible(false);
        }
        if(grapWidth>0){
            drawable.setGapWidth(grapWidth);
        }
        if(radiu!=null){
            if(radiu.length==1){
                drawable.setRadius(radiu[0]);
            }else if(radiu.length==8){
                drawable.setRadius(radiu);
            }
        }
        return drawable;
    }

}
