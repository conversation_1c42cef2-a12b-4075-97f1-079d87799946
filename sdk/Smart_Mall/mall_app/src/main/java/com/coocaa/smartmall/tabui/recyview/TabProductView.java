package com.coocaa.smartmall.tabui.recyview;

import android.content.Context;
import android.view.View;

import com.coocaa.mylibrary.discover.RecommandResult.Recommand;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;

public class TabProductView extends TabDiscoverItemView implements NewRecycleAdapterItem<Recommand> {

    public TabProductView(Context context, boolean lsLargeItemView) {
        super(context, lsLargeItemView);
    }

    @Override
    public View getView() {
        return this;
    }

    @Override
    public void onUpdateData(Recommand discoverProduct, int i) {
        refreshUI(discoverProduct);
    }

    @Override
    public void clearItem() {
        //do nothing
    }

    @Override
    public void refreshUI() {

    }

    @Override
    public void onFocusChange(View view, boolean hasFocus) {
        super.onFocusChange(view, hasFocus);
    }

    @Override
    public void destroy() {
        super.destroy();
    }
}
