package com.coocaa.smartmall;

import android.os.Bundle;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.coocaa.smartmall.tabui.BaseActivity;
import com.coocaa.smartmall.tabui.DiscoverTabMainPluglayoutV2;
import com.skyworth.util.Util;


public class TvDiscoveryMainActivity extends BaseActivity {
    public static final String TAG = "TvDiscoveryMainActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        DiscoverTabMainPluglayoutV2 tabMainPluglayout = new DiscoverTabMainPluglayoutV2(this, null);
        tabMainPluglayout.createDiscoverTabPluginLayout();
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(30);
        params.gravity= Gravity.CENTER_HORIZONTAL;
        mMainLayout.addView(tabMainPluglayout, params);
    }
}