//package com.coocaa.smartmall.tabui.recyview;
//
//import android.content.Context;
//import android.content.Intent;
//import android.support.annotation.NonNull;
//import android.support.annotation.Nullable;
//import android.text.TextUtils;
//import android.util.AttributeSet;
//import android.util.Log;
//import android.view.Gravity;
//import android.view.KeyEvent;
//import android.view.View;
//import android.widget.FrameLayout;
//import android.widget.ImageView;
//import android.widget.RelativeLayout;
//import android.widget.TextView;
//
//import com.coocaa.smartmall.R;
//import com.coocaa.smartmall.detail.DetailActivity;
//import com.coocaa.smartmall.util.ViewBuilder;
//import com.coocaa.mylibrary.api.HttpApi;
//import com.coocaa.mylibrary.api.HttpSubscribe;
//import com.coocaa.mylibrary.api.HttpThrowable;
//import com.coocaa.mylibrary.discover.RecommandResult.Recommand;
//import com.coocaa.mylibrary.discover.RecommandResult;
//import com.skyworth.smarthome_tv.smarthomeplugininterface.IViewBoundaryCallback;
//import com.skyworth.util.Util;
//import com.skyworth.util.imageloader.ImageLoader;
//
//import java.util.List;
//
//public class CarContentViewLayout extends FrameLayout implements HttpSubscribe<RecommandResult>,
//        View.OnClickListener, View.OnFocusChangeListener, View.OnKeyListener {
//    private static final String TAG = "CarContentViewLayout";
//
//    private View carImageView;
//    private TextView carContentTitleView, carContentNameView, carContentDiscriptionView,
//            carContentPriceView;
//
//    private List<Recommand> mAllDiscoverProducts;
//    private RecommandResult mTabProduct;
//    private Recommand mCarContentDiscoverProduct;
//
//    private Intent mShowDetailInfoIntentPlugin;
//    private IViewBoundaryCallback mCallback;
//    private RelativeLayout parent;
//
//    public CarContentViewLayout(@NonNull Context context, IViewBoundaryCallback callback) {
//        super(context);
//        initCarContentView();
//        mCallback = callback;
//    }
//
//    public CarContentViewLayout(@NonNull Context context, @Nullable AttributeSet attrs, IViewBoundaryCallback callback) {
//        super(context, attrs);
//        initCarContentView();
//        mCallback = callback;
//    }
//
//    public CarContentViewLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, IViewBoundaryCallback callback) {
//        super(context, attrs, defStyleAttr);
//        initCarContentView();
//        mCallback = callback;
//    }
//
//    private void initCarContentView() {
//        setFocusable(true);
//        setPressed(true);
//        setClickable(true);
//        setClipChildren(false);
//        setClipToPadding(false);
//        FrameLayout.LayoutParams carContentViewLayoutParams = new FrameLayout.LayoutParams(Util.Div(410+16), Util.Div(540+16));
//        carContentViewLayoutParams.gravity = Gravity.CENTER;
//        addView(ViewBuilder.getContentCarViewLayout(getContext()), carContentViewLayoutParams);
//
//        parent = findViewById(R.id.car_view_parent_id);
//        carImageView = findViewById(R.id.car_view_image_view);
//        carContentTitleView = findViewById(R.id.car_view_title_text);
//        carContentNameView = findViewById(R.id.car_view_product_name_text);
//        carContentDiscriptionView = findViewById(R.id.car_view_product_discription_text);
//        carContentPriceView = findViewById(R.id.car_view_product_price_text);
//
//        HttpApi.getInstance().getRecommand(CarContentViewLayout.this);
//
//        setOnFocusChangeListener(CarContentViewLayout.this);
//        setOnClickListener(CarContentViewLayout.this);
//        setOnKeyListener(CarContentViewLayout.this);
//    }
//
//
//
//    @Override
//    public void onFocusChange(View view, boolean hasFocus) {
//        if (hasFocus) {
//            parent.setVisibility(View.VISIBLE);
//        } else {
//            parent.setVisibility(View.INVISIBLE);
//        }
//        Util.focusAnimate(view, hasFocus);
//    }
//
//    public void refreshCarContentData(Recommand mCurrentCarViewData) {
//        if (mCurrentCarViewData != null) {
//            try {
//
//                if (carImageView != null && mCurrentCarViewData.getProductImage() != null && !mCurrentCarViewData.getProductImage().equals("")) {
//                    ImageLoader.getLoader()
//                            .with(getContext())
//                            .load(mCurrentCarViewData.getProductImage())
//                            .resize(Util.Div(410), Util.Div(540))
//                            .setScaleType(ImageView.ScaleType.FIT_XY)
//                            .setLeftBottomCorner(Util.Div(18))
//                            .setLeftTopCorner(Util.Div(18))
//                            .setRightBottomCorner(Util.Div(18))
//                            .setRightTopCorner(Util.Div(18))
//                            .into(carImageView);
//                }
//                    String name=mCurrentCarViewData.getProductName();
//                    String desc=mCurrentCarViewData.getProductDescription();
//                if (carContentNameView != null && !TextUtils.isEmpty(name)) {
//                    carContentNameView.setText(name);
//                }
//                if (carContentDiscriptionView != null && !TextUtils.isEmpty(desc)) {
//                    carContentDiscriptionView.setText(desc);
//                }
//                if (carContentPriceView != null && mCurrentCarViewData.getProductDescription() != null && !mCurrentCarViewData.getProductDescription().equals("")) {
//                    carContentPriceView.setText(mCurrentCarViewData.getProductDescription());
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//    }
//
//    @Override
//    public void onSuccess(RecommandResult result) {
//        if (result != null) {
//            mTabProduct = result;
//            mAllDiscoverProducts = mTabProduct.getDataResult();
//            if (mAllDiscoverProducts != null && mAllDiscoverProducts.size() > 0) {
//                mCarContentDiscoverProduct = mAllDiscoverProducts.get(0);
//                refreshCarContentData(mCarContentDiscoverProduct);
//            }
//        }
//    }
//
//    @Override
//    public void onError(HttpThrowable error) {
//        Log.d(TAG, "getErrCode = " + error.getErrCode() + "     getErrMsg = " + error.getErrMsg());
//    }
//
//    @Override
//    public void onClick(View v) {
//        if (mAllDiscoverProducts != null && mAllDiscoverProducts.size() > 0) {
//            mShowDetailInfoIntentPlugin = new Intent(getContext(), DetailActivity.class);
//            mShowDetailInfoIntentPlugin.putExtra("display_type", "images");
//            mShowDetailInfoIntentPlugin.putExtra("product_id", String.valueOf(mAllDiscoverProducts.get(0).getProductId()));
//            mShowDetailInfoIntentPlugin.putExtra("image_url", mAllDiscoverProducts.get(0).getProductImage());
//            getContext().startActivity(mShowDetailInfoIntentPlugin);
//        }
//    }
//
//    @Override
//    public boolean onKey(View view, int keyCode, KeyEvent event) {
//        if (event.getAction() == KeyEvent.ACTION_DOWN && mCallback != null) {
//            switch (event.getKeyCode()) {
//                case KeyEvent.KEYCODE_DPAD_UP:
//                    Log.d(TAG, "KEYCODE_DPAD_UP");
//                    return mCallback.onTopBoundary(view);
//                case KeyEvent.KEYCODE_DPAD_RIGHT:
//                    Log.d(TAG, "KEYCODE_DPAD_RIGHT");
//                    return mCallback.onRightBoundary(view);
//                case KeyEvent.KEYCODE_DPAD_DOWN:
//                    Log.d(TAG, "KEYCODE_DPAD_DOWN");
//                    return mCallback.onDownBoundary(view);
//                case KeyEvent.KEYCODE_DPAD_LEFT:
//                    Log.d(TAG, "KEYCODE_DPAD_LEFT");
//                    return mCallback.onLeftBoundary(view);
//                case KeyEvent.KEYCODE_BACK:
//                    return mCallback.onBackKey(view);
//                default:
//                    break;
//            }
//        }
//        return false;
//    }
//
//}
