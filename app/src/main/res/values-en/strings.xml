<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">小维智联</string>

    <!-- AP自动配网 -->
    <string name="OK">好的</string>
    <string name="Cancel">取消</string>
    <string name="exit">退出</string>
    <string name="confirm">确定</string>
    <string name="apconfig_discover_device_name">发现“%1$s”</string>
    <string name="apconfig_discover_device_tip">添加后可在电视上语音控制该设备</string>
    <string name="apconfig_discover_device_confirm">立即添加</string>
    <string name="apconfig_check_ethernet_use_wifi">请拔出网线，切换至Wi-Fi网络</string>
    <string name="apconfig_check_ethernet_tip">有线网络无法和智能设备配网</string>
    <string name="apconfig_check_ethernet_confirm">已确定拔出</string>
    <string name="apconfig_network_err_title">无线网络</string>
    <string name="apconfig_network_err_input">请输入密码连接%1$s：</string>
    <string name="apconfig_network_err_pwd">密码错误，请重新输入</string>
    <string name="apconfig_network_err_timeout">连接超时，请重试</string>
    <string name="apconfig_network_err_exception">网络信息获取异常，请重新输入</string>
    <string name="apconfig_confignetwork">设备配网中</string>
    <string name="apconfig_confignetwork_tip">请保持电视及设备网络畅通</string>
    <string name="apconfig_switchwifi_title">请连接以下2.4G Wi-Fi：</string>
    <string name="apconfig_switchwifi_title_tip">因部分智能设备不支持5G Wi-Fi</string>
    <string name="apconfig_switchwifi_button_txt">切换Wi-Fi</string>
    <string name="apconfig_exit_config_toast">再次点击返回，退出配网</string>
    <string name="apconfig_exit_bind_toast">再次点击返回，退出绑定</string>
    <string name="apconfig_voice_discover">发现您的环境中有一台%1$s</string>
    <string name="apconfig_voice_config">正在自动为您配网</string>
    <string name="apconfig_voice_success">%1$s配网成功</string>
    <string name="apconfig_voice_failed">%1$s配网失败</string>
    <string name="apconfig_set_pos_title">完善设备信息</string>
    <string name="apconfig_set_pos_name">设备名称</string>
    <string name="apconfig_set_pos_pos">设备位置</string>
    <string name="apconfig_set_pos_button">立即使用</string>

    <string name="apconfig_not_login_tip">您的账号未登录！</string>
    <string name="apconfig_not_bind_mobile_tip">您的手机号未绑定！</string>
    <string name="apconfig_not_bind_midea_tip">您未绑定美的账号！</string>
    <string name="apconfig_not_bind_midea_token_out_of_date">美的账号Token过期，请重新绑定</string>
    <string name="apconfig_not_bind_button_tip">立即绑定</string>
    <string name="apconfig_not_login_button_tip">立即登录</string>

    <string name="add_failed">抱歉，配网失败！</string>
    <string name="add_success">添加成功</string>
    <string name="look_over_device">查看设备</string>
    <string name="binding_device_failed">绑定设备失败</string>
    <string name="immediate_binding">立即绑定</string>
    <string name="already_off_online">已离线</string>
    <string name="online">在线</string>
    <string name="bind_failed_other_bind">绑定设备失败，已被其他账户绑定，请先解绑</string>
    <string name="reconnect">重新配网</string>
    <string name="add_failed_tips">请确认设备已进入配网模式</string>
    <string name="discover_devices_help_titile">发现不了设备怎么办？</string>
    <string name="discover_devices_help_tips">1、确保电视与设备之间的距离足够近（小于10米），并且周围网络环境没有强干扰及厚墙阻挡;\n2、确保设备没有被他人绑定，若不清楚谁绑定了设备，可根据设备的说明书对设备进行重置；\n3、确保电视连上的是2.4G频段的WiFi（WiFi名字形如XXX_2.4G)，请不要连接5G频段的WiFi（WiFi名字形如XXXX_5G）。</string>
    <string name="binding_account_midea_title">扫一扫绑定账号</string>
    <string name="binding_account_midea_tips">请使用微信或QQ扫码</string>

    <string name="control_panel_you_can_speech">您可以语音：</string>
    <string name="control_panel_more_operation">更多操作</string>
    <string name="control_panel_ir_entry">进入万能遥控</string>
    <string name="control_panel_empty">数据暂无</string>

    <!-- 场景和设备列表 -->
    <string name="scene_list_count">共%1$s个场景</string>
    <string name="scene_more">&#160;&#160;更多场景</string>
    <string name="scene_no_data">暂没有场景数据</string>
    <string name="scene_ai_download">下载手机APP</string>
    <string name="scene_execing">正在执行...</string>
    <string name="scene_exec_success">发送成功，执行场景中</string>
    <string name="scene_exec_failure">执行失败</string>
    <string name="scene_ai_more">体验更多玩法</string>
    <string name="device_list_count">共%1$s个设备</string>
    <string name="device_no_data">暂未添加智能设备，请添加设备</string>
    <string name="device_list_not_support_voice">暂未支持控制，敬请等待</string>

    <!-- 添加设备、关联账号 -->
    <string name="tab_title_nearby">附近的设备</string>
    <string name="tab_title_account">关联账号</string>
    <string name="discover_tips">发现设备中，请确保设备处于配网模式</string>
    <string name="look_help">查看帮助</string>
    <string name="discover_btn_txt">发现不了设备？点击查看</string>
    <string name="account_binded">已绑定</string>
    <string name="account_bind">绑定账号</string>
    <string name="account_bind_tips">绑定其他平台账号，语音控制更多设备</string>
    <string name="account_bind_qr_failed">绑定第三方平台账号失败，请重试</string>

    <!-- 播放器 -->
    <string name="video_doorbell_tip">有人来访按【返回键】关闭</string>
    <string name="video_camera_tip">语音 \"小度小度，开门\" 即可开门</string>
    <string name="doorbell_tip_visitor">您有访客到来，视频接入中···</string>
    <string name="video_error">播放异常</string>

    <!-- 智能化率 -->
    <string name="smart_rate_all_house">全屋</string>
    <string name="smart_rate_tips01">你的智能化率已提升至</string>
    <string name="smart_rate_tips02">你的智能化率已提升</string>

    <string name="try_refresh">刷新试试</string>
    <string name="login_button">立即登录</string>
    <string name="voice_tips_title">试试说：小度小度</string>
    <string name="set_device_title">恭喜你，配网成功！</string>
    <string name="set_device_subtitle">完善位置信息更方便日常使用</string>
    <string name="add_device">添加智能设备</string>
    <string name="unbind_acc_msg">确定要解绑%1$s吗？</string>
    <string name="offline">离线</string>
    <string name="all_smart_rate">全屋智能化率</string>
    <string name="smart_rate">智能化率</string>
    <string name="unsupport_control">暂未支持控制</string>
    <string name="not_find_device">暂未发现设备</string>
    <string name="not_find_scene">暂未发现场景</string>
    <string name="scene_tip_unlogin">暂无场景，请登录后查看或控制场景</string>
    <string name="device_tip_unlogin">暂无设备，请登录后添加或同步设备</string>
    <string name="device_tip_adddevice">暂无设备，请在电视或手机上添加新设备</string>
    <string name="scan_bind_thridacc">扫一扫绑定%1$s账号</string>

    <string name="near_devices">附近的设备</string>

    <!--红外设备类型-->
    <string name="smart_home_select_electric_type">选择电器类型</string>
    <string name="smart_home_select_electric_brand">选择电器品牌</string>
    <string name="smart_home_select_electric_brand_subtitle">请保持电器与红外电视靠近，相互之间没有遮挡</string>
    <string name="smart_home_select_electric_hot_rand_subtitle">热门品牌</string>
    <string name="all_power_remote_control">万能遥控器</string>
    <string name="tv_all_power_remote_control">电视万能遥控器</string>
    <string name="add">添加</string>
    <string name="remote_control_data_empty_tips">暂未添加遥控器，添加后可控制</string>
    <string name="immediate_add">立即添加</string>
    <string name="congratulate_add_success">恭喜你，添加成功</string>
    <string name="loading">加载中...</string>

    <string name="try_voice_control">电视上仅支持语音控制，更多按键可在手机APP进行控制</string>
    <string name="infrared_control">红外可控</string>
    <string name="virtual_remote_control">虚拟遥控器</string>
    <string name="ir_config_match_key">匹配型号（%1$d/%2$d）</string>
    <string name="ir_config_sending_key">红外指令正在发送...</string>
    <string name="ir_config_confirm_send_key_tip">\"%1$s\"指令已发送</string>
    <string name="ir_config_confirm_tip">请查看你的%1$s是否响应？</string>
    <string name="ir_config_confirm_not_sure">不确定，再试一次</string>
    <string name="ir_config_confirm_yes">是</string>
    <string name="ir_config_confirm_no">否</string>
    <string name="ir_config_error">抱歉，红外匹配失败！</string>
    <string name="ir_config_error_button">重试</string>
    <string name="ir_config_error_learn">手动学习</string>
    <string name="ir_config_ready_title">请关闭您的%1$s</string>
    <string name="ir_config_ready_tip">温馨提示：暂不支持蓝牙遥控的%1$s</string>
    <string name="ir_config_ready_button">确认已关闭，开始匹配</string>
    <string name="ir_config_success_speech">现在您就可以使用语音控制%1$s了，试试说%2$s，开启您的智能生活</string>
    <!-- 控制面板 -->
    <string name="control_panel_not_selected">未选择</string>
    <string name="control_panel_scene_panel_title">按键#%1$d</string>

    <!-- 4.1.2优化需求 -->
    <string name="discover_devices_voice_tip">您的环境中暂未发现可配网的智能设备，请在局域网扫描时，确保智能设备处于配网模式</string>
    <string name="unknow_device">未知设备</string>
    <string name="unbind">未绑定</string>
    <string name="binded">已绑定</string>
    <string name="added">已添加</string>
    <string name="un_apconfig_network">未配网</string>
    <string name="launcher_need_login">欢迎使用酷开智屏，请先登录账户</string>
    <string name="launcher_need_bind_mobile">欢迎使用酷开智屏，请先绑定手机号码</string>

    <!---4.4 优化需求 -->
    <string name="manual_study">手动学习</string>
    <string name="delete_ir_device_tips">按【菜单】键可删除设备</string>
    <string name="delete_failed">删除失败</string>
    <string name="ir_devices_tv">红外电视</string>
    <string name="add_ir_device_tips">请在%s上，去添加遥控器</string>

    <!-- 红外学习 -->
    <string name="ir_learn_ready_title">需要您准备好%1$s遥控器</string>
    <string name="ir_learn_ready_tip">手动学习后，可语音控制该设备</string>
    <string name="ir_learn_ready_button">已准备好遥控器，开始学习</string>
    <string name="ir_learn_start_title">手动学习（%1$d/%2$d）</string>
    <string name="ir_learn_start_tip1">请将%1$s遥控器对准此%2$s，按下遥控器上</string>
    <string name="ir_learn_start_tip2">键进行学习</string>
    <string name="ir_learn_start_learning">电视已处于学习状态</string>
    <string name="ir_learn_success_title">\"%1$s\" 学习成功!</string>
    <string name="ir_learn_success_tip">正在前往下一按键</string>
    <string name="ir_learn_fail_title">抱歉，学习失败！</string>
    <string name="ir_learn_fail_retry">重试</string>
    <string name="ir_learn_fail_next">下一个</string>
    <string name="ir_learn_finish_tip">恭喜你，学习成功</string>

    <!---4.6 优化需求 -->
    <string name="ir_device_name">电视万能遥控器</string>
    <string name="ir_device_desc">可控制传统家电</string>
    <string name="add_device_result_tips_01">绑定智能设备后，可遥控或语音控制</string>
    <string name="add_device_result_tips_02">请确保设备处于配网模式</string>
    <string name="add_device_result_fail_title">抱歉，设备绑定失败</string>
    <string name="add_device_bind">绑定</string>
    <string name="add_device_reconnect">重连</string>
    <string name="user_mobile_phone_bind">使用手机绑定</string>
    <string name="login_and_bind">登录并绑定</string>
    <string name="got_it">知道了</string>
    <string name="voice_speech_not_login">需要您登录酷开账号，才能控制设备</string>
    <string name="voice_speech_not_bind_phone">要您绑定手机号，才能控制设备</string>

    <!---4.6 优化需求 -->
    <string name="goto_bind_phone">去绑定手机号</string>
    <string name="not_have_no_apconfig_device">暂未发现未配网的设备</string>

    <!-- 启动第三方App -->
    <string name="third_app_loading_tip">正在加载模块，请稍后%1$s</string>

    <!--   离线帮助 -->
    <string name="offline_help_theme">离线帮助</string>
    <string name="offline_help_check">您的设备已离线，请依次检查</string>
    <string name="offline_help_content">1. 设备是否通电（通电后稍等一会儿）\n2. 设备连接的路由器是否正常，网络通畅\n3. 是否修改了路由器密码或名称，可尝试重新绑定\n4. 设备是否与路由器距离过远</string>
    <string name="offline_help_rebinding">重新绑定</string>
    <string name="offline_help_cancle">取消</string>
    <string name="offline_help_content_2">1. 设备是否通电（通电后稍等一会儿）\n2. 连接此设备的网关是否在线\n3. 设备是否与网关距离过远</string>
    <string name="offline_help_sure">确定</string>

    <!--   设备指南 -->
    <string name="guide_add_devices_tips">快速发现，一键妙连</string>
    <string name="guide_add_devices_btn">电视上添加设备</string>
    <string name="guide_add_devices_syn">可关联其他平台账号同步设备</string>
    <string name="guide_add_devices_relevance">关联账号</string>

    <!--   个人中心 -->
    <string name="person_center_logo">个人中心</string>
    <string name="person_center_family_name">小明的家</string>
    <string name="person_center_devices">共%1$s台设备</string>
    <string name="person_center_logout">退出登录</string>
    <string name="person_center_change_family">切换家庭</string>
    <string name="person_center_relate_account">关联账号</string>
    <string name="person_center_unbind_device">解绑设备</string>
    <string name="person_center_phone_number">手机号</string>
    <string name="person_center_bind_device">去绑定</string>
    <string name="person_center_version_info">版本信息</string>
    <string name="person_center_order">我的订单</string>
    <string name="person_center_wait_bind">待绑定手机号</string>
    <string name="person_center_family_nothing">无</string>
    <string name="person_center_find_device">自动发现设备</string>
    <string name="person_center_find_device_close">关闭</string>
    <string name="person_center_find_device_open">开启</string>
    <string name="person_center_V">V %1$s</string>

    <!-- 解绑设备 -->
    <string name="unbind_devices_theme">解绑设备</string>
    <string name="unbind_devices_menu">按菜单键解绑</string>
    <string name="unbind_devices_nodevices_tips">暂无设备，请添加</string>
    <string name="unbind_devices_add_device">添加设备</string>
    <string name="unbind_devices_unbind">解绑</string>
    <string name="unbind_devices_dialog_theme">解绑后将不能控制此设备，您创建的场景也会受影响，是否解绑？</string>
    <string name="more_information" tools:ignore="ExtraTranslation">更多信息请到手机APP进行查看</string>

</resources>
