package com.skyworth.smarthome_tv.home.custom;

import android.content.Context;

import com.coocaa.operate6_0.presenter.IPresenter;
import com.coocaa.operate6_0.presenter.IPresenterFactory;
import com.coocaa.operate6_0.utils.SupportUtil;
import com.skyworth.smarthome.home.custom.panel.DevicePanelPresenter;
import com.skyworth.smarthome.home.custom.panel.ScenePanelPresenter;
import com.skyworth.smarthome_tv.home.custom.block.PluginBlockPresenter;
import com.skyworth.smarthome_tv.home.custom.panel.PluginPanelPresenter;
import com.skyworth.smarthome_tv.home.custom.panel.SmartMallPresenter;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/17
 */
public class HomePresenterFactory implements IPresenterFactory {

    public HomePresenterFactory(){
        SupportUtil.addSupportType(Custom.TYPE_DEVICE, String.class);
        SupportUtil.addSupportType(Custom.TYPE_SCENE_MANUAL, String.class);
        SupportUtil.addSupportType(Custom.TYPE_MALL, String.class);
        SupportUtil.addSupportType(Custom.TYPE_MALL_OLD, String.class);
        SupportUtil.addSupportType(Custom.TYPE_MESSAGE, String.class);
        SupportUtil.addSupportType(Custom.TYPE_IOT_CHANNEL, String.class);
        SupportUtil.addSupportType(Custom.TYPE_VIDEO_CALL, String.class);
        SupportUtil.addSupportType(Custom.TYPE_SCENE_AUTO, String.class);

    }

    @Override
    public IPresenter createPresenter(String s, Context context) {
        switch (s) {
            case Custom.TYPE_DEVICE:
                return new DevicePanelPresenter(context);
            case Custom.TYPE_SCENE_MANUAL :
                return new ScenePanelPresenter(context, ScenePanelPresenter.Mode.MANUAL);
            case Custom.TYPE_MESSAGE:
            case Custom.TYPE_IOT_CHANNEL:
                return new PluginBlockPresenter(context, s);
            case Custom.TYPE_MALL:
            case Custom.TYPE_MALL_OLD:
                return new SmartMallPresenter(context);
            case Custom.TYPE_VIDEO_CALL:
                return new PluginPanelPresenter(context, s);
            case Custom.TYPE_SCENE_AUTO:
                return new ScenePanelPresenter(context, ScenePanelPresenter.Mode.AUTO);
            default:
                return null;
        }
    }
}
