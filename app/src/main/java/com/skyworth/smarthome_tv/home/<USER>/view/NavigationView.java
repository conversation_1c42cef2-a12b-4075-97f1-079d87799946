package com.skyworth.smarthome_tv.home.main.view;

import android.content.Context;
import android.support.v7.widget.NewRecycleAdapter;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import com.skyworth.smarthome_tv.home.main.view.data.NavigationData;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
import com.skyworth.ui.newrecycleview.NewRecycleLayout;
import com.skyworth.ui.newrecycleview.OnBoundaryListener;
import com.skyworth.ui.newrecycleview.OnItemClickListener;
import com.skyworth.ui.newrecycleview.OnItemFocusChangeListener;
import com.skyworth.util.Util;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/4
 */
public class NavigationView extends FrameLayout implements OnBoundaryListener, OnItemFocusChangeListener, OnItemClickListener {
    private NewRecycleLayout<NavigationData> mRecycleLayout;
    private NewRecycleAdapter<NavigationData> mAdapter;
    private List<NavigationData> mDataList = new ArrayList<>();
    private IHomeView.ILayoutListener mLayoutListener;
    private int mCurrentIndex = 0;

    public NavigationView(Context context, IHomeView.ILayoutListener layoutListener) {
        super(context);
        mLayoutListener = layoutListener;
        mRecycleLayout = new NewRecycleLayout<>(context);
        mRecycleLayout.setOrientation(LinearLayout.HORIZONTAL);
        mRecycleLayout.setSpanCount(1);
        mRecycleLayout.setmItemFocusChangeListener(this);
        mRecycleLayout.setmBoundaryListener(this);
        mRecycleLayout.setmItemClickListener(this);
        mRecycleLayout.setClipChildren(false);
        mRecycleLayout.setClipToPadding(false);
        mRecycleLayout.setItemSpace(Util.Div(8), 0);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        addView(mRecycleLayout, params);
    }

    public void refreshUI(List<NavigationData> dataList, final int selectIndex) {
        mDataList.clear();
        mDataList.addAll(dataList);
        if (mAdapter == null) {
            mAdapter = new NewRecycleAdapter<NavigationData>(mDataList, 1) {
                @Override
                public NewRecycleAdapterItem<NavigationData> onCreateItem(Object type) {
                    return new NavigationItemView(getContext());
                }
            };
            mRecycleLayout.setRecyclerAdapter(mAdapter);
        } else {
            mRecycleLayout.notifyDataSetChanged();
        }
        post(new Runnable() {
            @Override
            public void run() {
                mRecycleLayout.setSelection(selectIndex);
                refreshSelectStatus(selectIndex);
            }
        });
    }

    @Override
    public boolean onLeftBoundary(View leaveView, int position) {
        return true;
    }

    @Override
    public boolean onTopBoundary(View leaveView, int position) {
        return false;
    }

    @Override
    public boolean onDownBoundary(View leaveView, int position) {
        return mLayoutListener.getFocus(HomeView.FOCUS_TO_CONTENT, HomeView.FROM_TAB);
    }

    @Override
    public boolean onRightBoundary(View leaveView, int position) {
        return true;
    }

    @Override
    public boolean onOtherKeyEvent(View v, int position, int keyCode) {
        return false;
    }

    @Override
    public void click(View v, int position) {
        if (mCurrentIndex == position) {
            return;
        }
        mCurrentIndex = position;
        mLayoutListener.switchTab(position);
        refreshSelectStatus(position);
    }

    private void refreshSelectStatus(int currPos) {
        int itemCount = mRecycleLayout.getChildItemCount();
        for (int i = 0; i < itemCount; i++) {
            NavigationItemView itemView = ((NavigationItemView) mRecycleLayout.getItemByPosition(i));
            if (itemView != null) {
                itemView.select(i == currPos);
            }
        }
    }

    public boolean getFocus(int pos) {
        if (mRecycleLayout.getChildItemCount() >= pos) {
            mRecycleLayout.setSelection(pos);
        }
        return true;
    }

    public boolean getFocus() {
        return getFocus(mCurrentIndex);
    }

    public boolean itemHasFocus() {
        int itemCount = mRecycleLayout.getChildItemCount();
        for (int i = 0; i < itemCount; i++) {
            if (mRecycleLayout.getItemByPosition(i).hasFocus()) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void focusChange(View v, int position, boolean hasFocus) {
        if (hasFocus) {
            if (mCurrentIndex != position) {
                mCurrentIndex = position;
                mLayoutListener.switchTab(position);
                refreshSelectStatus(position);
            }
        }
        ((NavigationItemView) v).onFocusChange(v, hasFocus);
    }

    public void destroy() {

    }
}
