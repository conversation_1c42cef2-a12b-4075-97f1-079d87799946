package com.skyworth.smarthome_tv.home.main.presenter;

import android.content.Context;
import android.content.Intent;

import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.http.HttpServiceManager;
import com.coocaa.app.core.utils.FuncKt;
import com.coocaa.operate6_0.model.AppendData;
import com.coocaa.operate6_0.model.Container;
import com.coocaa.operate6_0.model.MainData;
import com.coocaa.operate6_0.presenter.LoadMorePresenter;
import com.coocaa.operate6_0.utils.SupportUtil;
import com.skyworth.smarthome.common.model.SPCacheData;
import com.skyworth.smarthome.common.util.CommonUtil;
import com.skyworth.smarthome.service.model.ISmartHomeModel;
import com.skyworth.smarthome_tv.common.bean.HomeTabBean;
import com.skyworth.smarthome_tv.common.http.HomePageHttpService;
import com.skyworth.smarthome_tv.common.model.LocalDefaultConfig;
import com.skyworth.smarthome_tv.home.HomeUtil;
import com.skyworth.smarthome_tv.home.family.FamilyPresenter;
import com.skyworth.smarthome_tv.home.main.view.IHomeView;
import com.skyworth.smarthome_tv.home.main.view.data.NavigationData;
import com.smarthome.common.model.SmartBaseData;
import com.smarthome.common.model.SmartBaseListData;
import com.smarthome.common.sal.SalImpl;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XNetworkDialog;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;
import swaiotos.sal.network.INetwork;

import static com.coocaa.operate6_0.utils.SupportUtil.changeToAppendData;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/4
 */
public class HomePresenter implements IHomePresenter {

    private final static String TAG = "HomePresenter";
    private Context mContext;
    private IHomeView mView;
    private FamilyPresenter mFamilyPresenter;
    private IotChannelPresenter mIotChannelPresenter;
    private int mSysVersionCode = 8;
    private List<NavigationData> mTabList = new ArrayList<>();
    private final static int SMART_HOME_TAG_ID = 106430;// 配置的固定分类id
    private final static String SMART_HOME_TAG_NAME = "智家";// 配置的固定分类名称
    private final static int SMART_DEVICE_TAG_ID = 106431;//设备tab的id

    private INetwork.INetworkListener iNetworkListener = new INetwork.INetworkListener() {
        @Override
        public void onNetChanged(boolean b, int i) {
            CCLog.i(TAG, "onNetChanged : " + b);
            if (b) {
                XNetworkDialog.dismiss();
                loadSmartDeviceData();
            }
            mIotChannelPresenter.onNetChanged(b);
        }
    };

    @Override
    public void create(final Context context, IHomeView view) {
        mContext = context;
        mView = view;
        mFamilyPresenter = new FamilyPresenter(mView.getFamilyView());
        mIotChannelPresenter = new IotChannelPresenter(mContext);
        SalImpl.getSAL(mContext).addNetListener(iNetworkListener);
        mSysVersionCode = CommonUtil.getSysVersionCode(mContext);
    }

    @Override
    public void start(Intent intent) {
        //优先加载本地数据
        loadLocalData();
        loadAiotData();
        //拉平跨屏互动逻辑
        mIotChannelPresenter.start();
    }

    private void loadLocalData() {
        CCLog.i(TAG, "loadLocalData");
        boolean needLoadDefaultData = true;
        boolean isSystem8 = mSysVersionCode > 7;
        //首先加载本地sp缓存的数据
        if (isSystem8) {
            String homeTags = SPCacheData.getHomeTags(mContext);
            if (EmptyUtils.isNotEmpty(homeTags)) {
                mTabList = JSONObject.parseArray(homeTags, NavigationData.class);
                if (EmptyUtils.isNotEmpty(mTabList)) {
                    setSmartDeviceTabIndex();
                    mView.refreshTabUI(mTabList);
                    for (int i = 0; i < mTabList.size(); i++) {
                        NavigationData data = mTabList.get(i);
                        String mainDataJson = SPCacheData.getMainData(mContext, String.valueOf(data.tabId));
                        MainData mainData = JSONObject.parseObject(mainDataJson, MainData.class);
                        if (mainData != null) {
                            needLoadDefaultData = false;
                            createContentView(mainData, data.tabId);
                        }
                    }
                }
            }
        }
        CCLog.i(TAG, "loadLocalData needLoadDefaultData:" + needLoadDefaultData);
        if (needLoadDefaultData) {
            //加载本地配置的数据
            mTabList.addAll(LocalDefaultConfig.getHomeTags(isSystem8));
            setSmartDeviceTabIndex();
            mView.refreshTabUI(mTabList);
            for (int i = 0; i < mTabList.size(); i++) {
                NavigationData data = mTabList.get(i);
                createContentView(LocalDefaultConfig.getMainData(data.tabId), data.tabId);
            }
        }
        if (isSystem8) {
            //加载完缓存数据后，如果是8.0的系统，再获取网络数据更新
            loadServerHomeTabs();
        }
    }

    private void setSmartDeviceTabIndex() {
        for (int i = 0; i < mTabList.size(); i++) {
            NavigationData data = mTabList.get(i);
            if (data.tabId == SMART_DEVICE_TAG_ID || data.tabId == LocalDefaultConfig.TAG_ID_SMARTDEVICE) {
                HomeUtil.setSmartDeviceTabIndex(i);
                break;
            }
        }
    }

    private void loadServerHomeTabs() {
        HttpServiceManager.Companion.call(HomePageHttpService.SERVICE.getHomeTabs(), new Function1<HttpServiceManager.ERROR, Unit>() {
            @Override
            public Unit invoke(HttpServiceManager.ERROR error) {
                CCLog.i(TAG, "loadServerHomeTabs onError: " + error.getMsg());
//                loadLocalData();
                return Unit.INSTANCE;
            }
        }, new Function1<SmartBaseData<HomeTabBean>, Unit>() {
            @Override
            public Unit invoke(SmartBaseData<HomeTabBean> baseData) {
                CCLog.i(TAG, "loadServerHomeTabs: onSuccess.");
                try {
                    if (baseData.data != null && EmptyUtils.isNotEmpty(baseData.data.array)) {
                        boolean needRefreshTabUI = true;
                        List<NavigationData> datas = new ArrayList<>();
                        for (HomeTabBean.TabData tabData : baseData.data.array) {
                            if (tabData.tag_id == SMART_HOME_TAG_ID || tabData.tag_name_cn.equals(SMART_HOME_TAG_NAME)) {
                                if (EmptyUtils.isNotEmpty(tabData.sub_tags)) {
                                    for (HomeTabBean.SubTag tag : tabData.sub_tags) {
                                        NavigationData data = new NavigationData();
                                        data.tabId = tag.tag_id;
                                        data.tabName = tag.tag_name_cn;
                                        datas.add(data);
                                    }
                                }
                                break;
                            }
                        }
                        if (datas.size() > 0) {
                            if (JSONObject.toJSONString(datas).equals(JSONObject.toJSONString(mTabList))) {
                                CCLog.i(TAG, "loadServerHomeTabs same data.");
                                needRefreshTabUI = false;
                            } else {
                                mTabList.clear();
                                mTabList.addAll(datas);
                            }
                            if (needRefreshTabUI) {
                                setSmartDeviceTabIndex();
                                CCLog.i(TAG, "loadServerHomeTabs refreshTabUI.");
                                SPCacheData.setHomeTags(mContext, JSONObject.toJSONString(mTabList));
                                mView.refreshTabUI(mTabList);
                            }
                            loadAllServerMainData();
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return Unit.INSTANCE;
            }
        });
    }

    private int getTabIndexById(int tabId) {
        for (int i = 0; i < mTabList.size(); i++) {
            NavigationData data = mTabList.get(i);
            if (data.tabId == tabId) {
                return i;
            }
        }
        return 0;
    }

    private void loadAllServerMainData() {
        CCLog.i(TAG, "loadAllServerMainData.");
        for (int i = 0; i < mTabList.size(); i++) {
            loadServerMainData(mTabList.get(i).tabId);
        }
    }

    private void loadServerMainData(final int tagId) {
//        String url = "http://api-home.ptskysrt.gitv.tv/v1/tvos/getTvosSixContent?layout_style=8&tag_id=" + tagId + "&md5=&page=1&page_size=6&fixed_pagesize=3";
        HttpServiceManager.Companion.call(HomePageHttpService.SERVICE.getOperateFirstPageData(String.valueOf(tagId), "8"), new Function1<HttpServiceManager.ERROR, Unit>() {
            @Override
            public Unit invoke(HttpServiceManager.ERROR error) {
                CCLog.i(TAG, "loadServerMainData onError: " + error.getMsg());
                return Unit.INSTANCE;
            }
        }, new Function1<SmartBaseData<MainData>, Unit>() {
            @Override
            public Unit invoke(SmartBaseData<MainData> baseData) {
                CCLog.i(TAG, "loadServerMainData: onSuccess.");
                if (baseData.data != null && baseData.data.content != null) {
                    try {
                        String mainDataCacheJson = SPCacheData.getMainData(mContext, String.valueOf(tagId));
                        MainData mainDataCache = JSONObject.parseObject(mainDataCacheJson, MainData.class);
                        if (mainDataCache != null && EmptyUtils.isNotEmpty(mainDataCache.md5) && EmptyUtils.isNotEmpty(baseData.data.md5) && mainDataCache.md5.equals(baseData.data.md5)) {
                            CCLog.i(TAG, "loadServerMainData: same data.");
                            return Unit.INSTANCE;
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    CCLog.i(TAG, "loadServerMainData: refreshUI. tagId:" + tagId);
                    SPCacheData.setMainData(mContext, String.valueOf(tagId), JSONObject.toJSONString(baseData.data));
                    createContentView(baseData.data, tagId);
                }
                return Unit.INSTANCE;
            }
        });
    }

    private void createContentView(MainData mainData, int tagId) {
        if (EmptyUtils.isNotEmpty(mainData.content.contents)) {
            //去除第一个panel的title
            String extra = mainData.content.contents.get(0).extra;
            if (EmptyUtils.isNotEmpty(extra)) {
                JSONObject extraJson = JSONObject.parseObject(extra);
                String title = extraJson.getString("title");
                if (EmptyUtils.isNotEmpty(title)) {
                    extraJson.put("title", "");
                    mainData.content.contents.get(0).extra = extraJson.toJSONString();
                }
            }
        }
        mainData.content.parseContent();
        //tag_id是请求后台数据的参数
        SupportUtil.parseContent(mainData.content, tagId, changeToAppendData(mainData));
        mView.createContentView(mainData.content, getTabIndexById(tagId));
    }

    @Override
    public void loadNextPageData(String url, final int id, final AppendData appendData, final LoadMorePresenter.LoadMoreCallback loadMoreCallback) {
        HttpServiceManager.Companion.call(HomePageHttpService.SERVICE.getOperateNextPageData(url), new Function1<HttpServiceManager.ERROR, Unit>() {
            @Override
            public Unit invoke(HttpServiceManager.ERROR error) {
                CCLog.i(TAG, "loadNextPageData onError: " + error.getMsg());
                loadMoreCallback.onLoad(null);
                return Unit.INSTANCE;
            }
        }, new Function1<SmartBaseListData<Container>, Unit>() {
            @Override
            public Unit invoke(final SmartBaseListData<Container> baseData) {
                CCLog.i(TAG, "loadNextPageData: onSuccess.");
                if (baseData.data != null) {
                    Container con = new Container();
                    con.contents = baseData.data;
                    con.parseContent();
                    //tag_id是请求后台数据的参数
                    SupportUtil.parseContent(con, id, appendData);
                    FuncKt.runOnUiThread(new Function0<Unit>() {
                        @Override
                        public Unit invoke() {
                            loadMoreCallback.onLoad(baseData.data);
                            return Unit.INSTANCE;
                        }
                    });
                } else {
                    loadMoreCallback.onLoad(null);
                }
                return Unit.INSTANCE;
            }
        });
    }

    private void loadAiotData() {
        loadSmartDeviceData();
    }

    private void loadSmartDeviceData() {
        ISmartHomeModel.INSTANCE.getSmartDeviceList(SPCacheData.getFamilyId(mContext));
        ISmartHomeModel.INSTANCE.getFamilyList();
        ISmartHomeModel.INSTANCE.getSceneList();
    }


    @Override
    public void destroy() {
        if (mFamilyPresenter != null) {
            mFamilyPresenter.destroy();
        }
        mTabList.clear();
        SalImpl.getSAL(mContext).removeNetListener(iNetworkListener);
    }
}
