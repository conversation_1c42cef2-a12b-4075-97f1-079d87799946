package com.skyworth.smarthome_tv.home.main.view;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome_tv.home.main.view.data.NavigationData;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
import com.skyworth.util.Util;
import com.smarthome.common.utils.XThemeUtils;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/4
 */
public class NavigationItemView extends FrameLayout implements NewRecycleAdapterItem<NavigationData> {

    private TextView tagName;
    private View line;
    private boolean isSelect = false;
    private CCFocusDrawable focusDrawable;

    public NavigationItemView(Context context) {
        super(context);
        setFocusable(true);
        setFocusableInTouchMode(true);
        setPadding(Util.Div(8), Util.Div(8), Util.Div(8), Util.Div(8));
        focusDrawable = new CCFocusDrawable(getContext()).setRadius(Util.Div(50));

        tagName = new TextView(getContext());
        tagName.setTextColor(Color.parseColor("#FFFFFF"));
        tagName.setTextSize(Util.Dpi(40));
        tagName.setGravity(Gravity.CENTER);
        tagName.setPadding(Util.Div(30), Util.Div(7), Util.Div(30), Util.Div(7));
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, Util.Div(70));
        params.gravity = Gravity.CENTER;
        addView(tagName, params);

        line = new View(getContext());
        line.setBackground(XThemeUtils.getDrawable(Color.parseColor("#FFFFFF"), 0, 0, Util.Div(2)));
        params = new LayoutParams(Util.Div(40), Util.Div(4));
        params.gravity = Gravity.CENTER_HORIZONTAL;
        params.topMargin = Util.Div(66);
        addView(line, params);
        line.setVisibility(GONE);
    }

    @Override
    public View getView() {
        return this;
    }

    @Override
    public void onUpdateData(NavigationData data, int position) {
        tagName.setText(data.tabName);
    }

    @Override
    public void clearItem() {

    }

    @Override
    public void refreshUI() {

    }

    public void select(boolean select) {
        isSelect = select;
        if (select) {
            line.setVisibility(VISIBLE);
            tagName.getPaint().setFakeBoldText(true);
        } else {
            line.setVisibility(GONE);
            tagName.getPaint().setFakeBoldText(false);
        }
        if (hasFocus()) {
            tagName.setTextColor(getContext().getResources().getColor(R.color.black));
        } else {
            tagName.setTextColor(getContext().getResources().getColor(R.color.white));
        }
        tagName.postInvalidate();
    }

    public void onFocusChange(View v, boolean hasFocus) {
        if (hasFocus) {
            line.setVisibility(GONE);
            tagName.getPaint().setFakeBoldText(true);
            tagName.setTextColor(getContext().getResources().getColor(R.color.black));
            setBackground(focusDrawable);
        } else {
            setBackground(null);
            select(isSelect);
        }
    }

    @Override
    public void destroy() {

    }
}
