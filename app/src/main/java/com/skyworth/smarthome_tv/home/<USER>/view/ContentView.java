package com.skyworth.smarthome_tv.home.main.view;

import android.content.Context;
import android.provider.Settings;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.utils.FuncKt;
import com.coocaa.operate6_0.model.AppendData;
import com.coocaa.operate6_0.model.Block;
import com.coocaa.operate6_0.model.Container;
import com.coocaa.operate6_0.model.OnClickData;
import com.coocaa.operate6_0.presenter.IExpanderPresenter;
import com.coocaa.operate6_0.presenter.IPresenter;
import com.coocaa.operate6_0.presenter.LoadMorePresenter;
import com.coocaa.operate6_0.presenter.OnBoundaryListener;
import com.coocaa.operate6_0.presenter.OnItemClickListener;
import com.coocaa.operate6_0.presenter.OnItemFocusChangeListener;
import com.coocaa.operate6_0.presenter.PresenterFactory;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.model.SPCacheData;
import com.skyworth.smarthome.common.util.CommonUtil;
import com.skyworth.smarthome.service.model.ISmartHomeModel;
import com.skyworth.smarthome.service.push.local.DeviceDataPushUtil;
import com.skyworth.util.Util;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.callback.LifecycleInterface;

import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/17
 */
public class ContentView extends FrameLayout implements LifecycleInterface {

    private IHomeView.ILayoutListener mLayoutListener;
    private IPresenter mViewPresenter;
    private boolean isPause = false;

    private OnBoundaryListener onBoundaryListener = new OnBoundaryListener() {
        @Override
        public boolean onLeftBoundary(View view, Container container, int i) {
            Util.instance().nope_X(view).start();
            return true;
        }

        @Override
        public boolean onTopBoundary(View view, Container container, int i) {
            return mLayoutListener.getFocus(HomeView.FOCUS_TO_TAB, HomeView.FROM_BUTTOM);
        }

        @Override
        public boolean onDownBoundary(View view, Container container, int i) {
            Util.instance().nope_Y(view).start();
            return false;
        }

        @Override
        public boolean onRightBoundary(View view, Container container, int i) {
            Util.instance().nope_X(view).start();
            return true;
        }

        @Override
        public boolean onTopItemFocus(boolean b) {
            CCLog.i("ContentView", "-----------onTopItemFocus: " + b);
            if (b) {
                mLayoutListener.showTopView();
            } else {
                mLayoutListener.hideTopView();
            }
            return false;
        }
    };

    private OnItemClickListener onItemClickListener = new OnItemClickListener() {
        @Override
        public void click(View view, List<Container> list, List<Integer> list1) {
            if (!CommonUtil.isNetConnected(getContext())) {
                return;
            }
            if (EmptyUtils.isNotEmpty(list)) {
                try {
                    Container con = list.get(0);
                    if (con.contentObject != null) {
                        Block block = (Block) con.contentObject;
                        if (block.block_content_info.parsedAction != null) {
                            CommonUtil.click(getContext(), block.block_content_info.parsedAction);
                        } else {
                            if (!TextUtils.isEmpty(block.block_content_info.action)) {
                                OnClickData data = null;
                                try {
                                    data = JSONObject.parseObject(block.block_content_info.action, OnClickData.class);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                if (data != null) {
                                    block.block_content_info.parsedAction = data;
                                    CommonUtil.click(getContext(), data);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    };

    public ContentView(@NonNull Context context, IHomeView.ILayoutListener layoutListener) {
        super(context);
        mLayoutListener = layoutListener;
        setClipChildren(false);
        setClipToPadding(false);
        setPadding(Util.Div(80), Util.Div(30), Util.Div(80), 0);
    }

    public void create(Container container) {
        if (mViewPresenter != null) {
            onDestroy();
        }
        mViewPresenter = PresenterFactory.getInstance().createPresenter(container.getConfirmType(), getContext());
        if (mViewPresenter instanceof IExpanderPresenter) {
            //设置加载更多回调
            ((IExpanderPresenter) mViewPresenter).setLoadMoreListener(new LoadMorePresenter.LoadMoreListener() {
                @Override
                public void load(LoadMorePresenter.LoadMoreCallback loadMoreCallback, String url, int formId, AppendData appendData) {
                    mLayoutListener.loadNextPageData(url, formId, appendData, loadMoreCallback);
                }
            });
        }
        mViewPresenter.setOnItemFocusChangeListener(new OnItemFocusChangeListener() {
            @Override
            public void focusChange(View view, boolean b, List<Container> list, List<Integer> list1) {

            }
        });
        mViewPresenter.setOnItemClickListener(onItemClickListener);
        mViewPresenter.setOnBoundaryListener(onBoundaryListener);
        //根据数据生成View
        mViewPresenter.setContainer(container);
        FuncKt.runOnUiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                LayoutParams params = new LayoutParams(Util.Div(1760), ViewGroup.LayoutParams.WRAP_CONTENT);
                params.gravity = Gravity.CENTER_HORIZONTAL;
                View page = mViewPresenter.getView();
                addView(page, params);
                mViewPresenter.onLayoutShow();
                return Unit.INSTANCE;
            }
        });
    }

    public void onLayoutShow() {
        if (mViewPresenter != null) {
            mViewPresenter.onLayoutShow();
        }
    }

    public void onLayoutHide() {
        if (mViewPresenter != null) {
            mViewPresenter.onLayoutHide(isPause);
        }
    }

    public boolean getFocus() {
        if (mViewPresenter != null) {
            return mViewPresenter.obtainFocus();
        }
        return true;
    }

    public boolean onKeyBack() {
        //暂不做这个功能
//        if (mViewPresenter != null && mViewPresenter.getTopFirstView() != null) {
//            try {
//                if (!mViewPresenter.getTopFirstView().isFocused()) {
//                    mViewPresenter.onLayoutBack();
//                    mViewPresenter.resetDefaultScrollState();
//                    post(new Runnable() {
//                        @Override
//                        public void run() {
//                            mViewPresenter.obtainFocus();
//                        }
//                    });
//                    return true;
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
        return false;
    }

    @Override
    public void onResume() {
        isPause = false;
        try {
            int netSwitch = Settings.System.getInt(getContext().getContentResolver(), "netSwitch");
            if (netSwitch == 1) {
                AppData.getInstance().setDeviceInfoList(null);
                DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.DEVICE_LIST, "a");
            }
        } catch (Settings.SettingNotFoundException e) {
            e.printStackTrace();
        }
        if (mViewPresenter != null) {
            mViewPresenter.onResume();
        }
    }

    @Override
    public void onPause() {
        isPause = true;
        if (mViewPresenter != null) {
            mViewPresenter.onPause();
        }
    }

    @Override
    public void onStop() {
        if (mViewPresenter != null) {
            mViewPresenter.onStop();
        }
    }

    @Override
    public void onDestroy() {
        removeAllViews();
        if (mViewPresenter != null) {
            mViewPresenter.onDestroy();
            mViewPresenter = null;
        }
    }
}
