package com.skyworth.smarthome_tv.home.custom;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.util.CommonUtil;
import com.skyworth.smarthome_tv.pluginmanager.boundarycallback.IBoundaryCallback;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.util.Util;
import com.smarthome.common.dataer.LogSDK;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XThemeUtils;
import com.smarthome.common.utils.XToast;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/7/14
 */
public abstract class BasePluginLayout extends FrameLayout {

    protected IBoundaryCallback mBoundaryCallback;
    protected TextView mTitle;
    protected FrameLayout mContentLayout;
    protected TextView mDefaultView;
    protected View mDefaultFocusView;
    protected String mCurrentPkg;
    protected int mWidth = Util.Div(1760);
    protected int mHeight = Util.Div(360);

    public BasePluginLayout(Context context) {
        super(context);
        setClipChildren(false);
        setClipToPadding(false);
    }

    public void setBoundaryCallback(IBoundaryCallback boundaryCallback) {
        mBoundaryCallback = boundaryCallback;
    }

    public void setContentView(View view) {
        removeAllViews();
        if (view != null && view.getParent() != null) {
            CCLog.i("BasePluginLayout", "setContentView:" + view.hashCode() + "    has parent...");
            ((ViewGroup) view.getParent()).removeView(view);
        }
        addView(view);
    }

    private void addTitle(String title) {
        mTitle = new TextView(getContext());
        mTitle.setTextColor(Color.WHITE);
        mTitle.setTextSize(Util.Dpi(48));
        mTitle.getPaint().setFakeBoldText(true);
        mTitle.setText(title);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        addView(mTitle, params);
    }

    public void setDefaultView(String msg) {
        createDefaultView("", "");
        mDefaultView.setText(msg);
        mDefaultView.setBackground(XThemeUtils.getDrawable(getContext().getResources().getColor(R.color.white_10), 0, 0, Util.Div(10)));
    }

    public void createDefaultView(String title, String pkg) {
        removeAllViews();
        mCurrentPkg = pkg;
        int topMargin = 0;
        if (EmptyUtils.isNotEmpty(title)) {
            topMargin = Util.Div(70);
            addTitle(title);
        }
        mContentLayout = new FrameLayout(getContext());
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = topMargin;
        addView(mContentLayout, params);

        mDefaultView = new TextView(getContext());
        mDefaultView.setPressed(true);
        mDefaultView.setFocusable(true);
        mDefaultView.setFocusableInTouchMode(true);
        params = new LayoutParams(mWidth, mHeight);
        params.gravity = Gravity.CENTER;
        mContentLayout.addView(mDefaultView, params);
        mDefaultFocusView = new View(getContext());
        mDefaultFocusView.setBackground(new CCFocusDrawable(getContext()).setRadius(Util.Div(10)).setSolidVisible(false));
        params = new LayoutParams(mWidth + Util.Div(10), mHeight + Util.Div(10));
        params.gravity = Gravity.CENTER;
        mContentLayout.addView(mDefaultFocusView, params);
        mDefaultFocusView.setVisibility(INVISIBLE);
        mDefaultView.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View view, boolean b) {
                if (b) {
                    mDefaultFocusView.setVisibility(VISIBLE);
                } else {
                    mDefaultFocusView.setVisibility(INVISIBLE);
                }
                Util.focusAnimate(mContentLayout, b);
            }
        });
        mDefaultView.setOnKeyListener(new OnKeyListener() {
            @Override
            public boolean onKey(View view, int keyCode, KeyEvent keyEvent) {
                if (keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                    switch (keyCode) {
                        case KeyEvent.KEYCODE_DPAD_UP:
                            return mBoundaryCallback.onTop(mContentLayout);
                        case KeyEvent.KEYCODE_DPAD_DOWN:
                            return mBoundaryCallback.onDown(mContentLayout);
                        case KeyEvent.KEYCODE_DPAD_LEFT:
                            return mBoundaryCallback.onLeft(mContentLayout);
                        case KeyEvent.KEYCODE_DPAD_RIGHT:
                            return mBoundaryCallback.onRight(mContentLayout);
                        case KeyEvent.KEYCODE_BACK:
                            return mBoundaryCallback.onBackKey(mContentLayout);
                        default:
                            break;
                    }
                }
                return false;
            }
        });
    }

    @SuppressLint("ClickableViewAccessibility")
    public void setDefaultBackground(int resId, final String type, final String pkg, final String pluginName) {
        mDefaultView.setBackgroundResource(resId);
        mDefaultView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                String reason = "";
                Map<String, String> params = new HashMap<>();
                if (EmptyUtils.isNotEmpty(pkg)) {
                    if (!CommonUtil.isNetConnected(getContext())) {
                        return;
                    }
                    reason = "已安装没插件";
                    try {
                        Intent intent = getContext().getPackageManager().getLaunchIntentForPackage(pkg);
                        getContext().startActivity(intent);
                    } catch (Exception e) {
                        e.printStackTrace();
                        if (pkg.equals(Custom.PKG_IOT_CHANNEL)) {
                            XToast.showToast(getContext(), "跨屏互动模块正在加载，请稍候...");
                        } else {
//                        XToast.showToast(getContext(), "未安装应用:" + pkg);
                            try {
                                reason = "未安装";
                                Intent appstore = new Intent("coocaa.intent.action.APP_STORE_DETAIL");
                                appstore.putExtra("id", pkg);
                                getContext().startActivity(appstore);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                        }
                    }
                }
                params.put("plugin_name", pluginName);
                params.put("reason", reason);
                LogSDK.submit(LogSDK.EVENT_ID_APP_NO_PLUGIN_CLICK, params);
            }
        });
        mDefaultView.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                    if (view.isFocusable() && view.isFocusableInTouchMode()) {
                        view.requestFocus();
                    }
                } else if (motionEvent.getAction() == MotionEvent.ACTION_UP) {
                    return view.callOnClick();
                }
                return false;
            }
        });
    }

    public void destroy() {
        CCLog.i("BasePluginLayout", "destroy:" + mCurrentPkg);
        removeAllViews();
    }
}
