package com.skyworth.smarthome_tv.home.custom;

import android.content.Context;
import android.view.View;

import com.skyworth.smarthome_tv.common.util.CCPackageManager;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome_tv.pluginmanager.IPluginViewLoadListener;
import com.skyworth.smarthome_tv.pluginmanager.PluginManager;
import com.skyworth.smarthome_tv.pluginmanager.lifecyclecallback.ILifeCycleCallback;
import com.skyworth.smarthome_tv.pluginmanager.type.IPluginViewType;
import com.skyworth.smarthome_tv.pluginmanager.type.PluginViewTypePanelView;
import com.smarthome.common.dataer.LogSDK;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.EmptyUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/7/14
 */
public abstract class BasePluginPresenter extends BaseCustomPresenter {

    protected Context mContext;
    protected String mType;
    protected String currentPkg = "";
    protected BasePluginLayout mLayout;
    protected ILifeCycleCallback mLifeCycleCallback;
    protected String mPluginName = "";
    protected Map<String, String> PKGS = new HashMap<>();

    private CCPackageManager.InstallListener mInstallListener = new CCPackageManager.InstallListener() {

        @Override
        public void onPackageAdded(String pkg) {
            log("onPackageAdded:" + pkg);
            CCPackageManager.INSTANCE.removeListener(pkg);
            loadPluginView(currentPkg);
        }

        @Override
        public void onPackageRemoved(String pkg) {
            log("onPackageRemoved:" + pkg);
        }
    };

    public BasePluginPresenter(Context context, String type) {
        super(context);
        mContext = context;
        mType = type;
        init();
        initPluginName();
        mLayout = getPluginLayout();
        mLayout.setBoundaryCallback(this);
        String pkg = PKGS.get(type);
        currentPkg = pkg;
        setDefaultView(type);
        if (EmptyUtils.isNotEmpty(pkg)) {
            loadPluginView(pkg);
        } else {
            log("loadPluginView: unknow type:" + type);
        }
    }

    private void initPluginName() {
        switch (mType) {
            case Custom.TYPE_MESSAGE:
                mPluginName = "留言板";
                break;
            case Custom.TYPE_VIDEO_CALL:
                mPluginName = "视频通话";
                break;
            case Custom.TYPE_IOT_CHANNEL:
                mPluginName = "跨屏互动";
                break;
            default:
                break;
        }
    }

    private void setDefaultView(String type) {
        switch (type) {
            case Custom.TYPE_MESSAGE:
                mLayout.createDefaultView("", currentPkg);
                mLayout.setDefaultBackground(R.drawable.plugin_default_bg_message, type, currentPkg, mPluginName);
                break;
            case Custom.TYPE_VIDEO_CALL:
                mLayout.createDefaultView("视频通话", currentPkg);
                mLayout.setDefaultBackground(R.drawable.plugin_default_bg_videocall, type, currentPkg, mPluginName);
                break;
            case Custom.TYPE_IOT_CHANNEL:
                mLayout.createDefaultView("", currentPkg);
                mLayout.setDefaultBackground(R.drawable.plugin_default_bg_iot_channel, type, currentPkg, mPluginName);
                break;
            default:
                mLayout.setDefaultView("plugin unknow type:" + type);
                break;
        }
    }

    protected abstract void init();

    protected abstract BasePluginLayout getPluginLayout();

    private void loadPluginView(final String pkg) {
        PluginManager.getInstance().getView(pkg, PluginViewTypePanelView.TYPE, new IPluginViewLoadListener() {
            @Override
            public void onLoadSuccess(String packageName, IPluginViewType type, View view, ILifeCycleCallback callback) {
                log("loadPluginView onLoadSuccess: " + packageName);
                mLifeCycleCallback = callback;
                try {
                    mLayout.setContentView(view);
                } catch (Exception e) {
                    log("loadPluginView onLoadSuccess  addView is error: " + packageName);
                    e.printStackTrace();
                }
                reportLogger("yes", "");
            }

            @Override
            public void onLoadFail(final String packageName, IPluginViewType type, String failReason) {
                log("loadPluginView onLoadFail: " + packageName + "--" + failReason);
                CCPackageManager.INSTANCE.addListener(packageName, mInstallListener);
                String reason = "";
                if (FAIL_REASON_LOAD_APK_ERROR.equals(failReason)) {
                    reason = "未安装";
                } else if (FAIL_REASON_LOAD_INTERFACE_ERROR.equals(failReason)) {
                    reason = "已安装没插件";
                }
                reportLogger("no", reason);
            }
        }, this);
    }

    private void reportLogger(String hasPlugin, String reason) {
        Map<String, String> params = new HashMap<>();
        params.put("plugin_name", mPluginName);
        params.put("if_plugin", hasPlugin);
        params.put("reason", reason);
        LogSDK.submit(LogSDK.EVENT_ID_APP_PLUGIN_SHOW, params);
    }

    private void log(String msg) {
        CCLog.i("BasePluginPresenter", msg);
    }

    @Override
    public void onResume() {
        super.onResume();
        log("onResume:" + currentPkg);
        if (mLifeCycleCallback != null) {
            mLifeCycleCallback.onResume();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        log("onPause:" + currentPkg);
        if (mLifeCycleCallback != null) {
            mLifeCycleCallback.onPause();
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        log("onStop:" + currentPkg);
        if (mLifeCycleCallback != null) {
            mLifeCycleCallback.onStop();
        }
    }

    @Override
    public View getView() {
        return mLayout;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        log("onDestroy:" + currentPkg);
        if (mLayout != null) {
            mLayout.destroy();
        }
        if (mLifeCycleCallback != null) {
            mLifeCycleCallback.onDestroy();
        }
    }

}
