package com.skyworth.smarthome_tv.home.custom;

/**
 * @Description: 智慧家庭所有自定义的组件类型
 * @Author: wzh
 * @CreateDate: 2020/7/14
 */
public class Custom {

    //panel type
    public final static String TYPE_MALL = "SMART_HOME_PANEL_MALL";//发现--购物
    public final static String TYPE_MALL_OLD = "SMART_HOME_CUSTOM_PANEL_MALL";//发现--购物（兼容旧的定义）
    public final static String TYPE_VIDEO_CALL = "SMART_HOME_PANEL_VIDEO_CALL";//视频通话
    public final static String TYPE_DEVICE = "SMART_HOME_PANEL_DEVICE";//设备列表
    public final static String TYPE_SCENE_MANUAL = "SMART_HOME_PANEL_SCENE_MANUAL";//场景列表-手动
    public final static String TYPE_SCENE_AUTO = "SMART_HOME_PANEL_SCENE_AUTO";//场景列表-自动

    //block type
    public final static String TYPE_MESSAGE = "SMART_HOME_BLOCK_MESSAGE";//留言板
    public final static String TYPE_IOT_CHANNEL = "SMART_HOME_BLOCK_IOT_CHANNEL";//跨屏互动

    //pkg 各插件包名
    public final static String PKG_MESSAGE = "com.trensai.msgboard";//留言板包名
    public final static String PKG_IOT_CHANNEL = "swaiotos.channel.iot";//跨屏互动包名
    public final static String PKG_VIDEO_CALL = "com.coocaa.tvpitv";//视频通话包名

}
