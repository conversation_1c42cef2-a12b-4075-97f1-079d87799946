package com.skyworth.smarthome_tv;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.provider.Settings;
import android.util.Log;
import android.view.KeyEvent;

import com.coocaa.server.v2.HostAddrLoaderManager;
import com.coocaa.server.v2.api.ServerApiV2;
import com.coocaa.server.v2.api.ServerCallBack;
import com.skyworth.smarthome_tv.common.base.BaseActivity;
import com.skyworth.smarthome_tv.common.util.CCPackageManager;
import com.skyworth.smarthome_tv.common.util.LoggerConnector;
import com.skyworth.smarthome_tv.home.HomeUtil;
import com.skyworth.smarthome_tv.home.main.presenter.HomePresenter;
import com.skyworth.smarthome_tv.home.main.presenter.IHomePresenter;
import com.skyworth.smarthome_tv.home.main.view.HomeView;
import com.skyworth.smarthome_tv.home.main.view.IHomeView;
import com.skyworth.smarthome_tv.pluginmanager.PluginManager;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.Constants;
import com.tianci.webservice.framework.CommonHeader;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/3
 */
public class HomeActivity extends BaseActivity {

    private IHomeView mView;
    private IHomePresenter mPresenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        CCPackageManager.INSTANCE.init(this);
        PluginManager.getInstance().init(getApplicationContext(), null);
        PluginManager.getInstance().setConnector2(new LoggerConnector(getApplicationContext()));
        String deviceId = getIntent().getStringExtra(Constants.KEY_DEVICE_ID);
        HomeUtil.setOpenCtrlPanelDeviceId(deviceId);
        mView = new HomeView(this);
        mPresenter = new HomePresenter();
        mView.create(this, mPresenter);
        mPresenter.create(this, mView);
        setContentView(mView.getView());
        mPresenter.start(getIntent());
        load();
    }

    private HostAddrLoaderManager.HeaderLoader headerLoader = new HostAddrLoaderManager.HeaderLoader() {
        @Override
        public Map<String, String> load() {
                HashMap<String,String> map = CommonHeader.getInstance().getCommonHeaderMap(HomeActivity.this,HomeActivity.this.getPackageName());

            return  map;

        }
    };

    private void load() {

        new Thread(new Runnable() {
            @Override
            public void run() {
                final String key1 = "beta-voice.tvos.skysrt.com";
                final Map<String, List<String>> map = ServerApiV2.getApi(HomeActivity.this,headerLoader).getAllConfig();

                final String license = ServerApiV2.getApi(HomeActivity.this, headerLoader).getLiscense();

                Log.d("test1", "getServerNameList, key=" + key1 + ", add=" + map);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {

                    }
                });
            }
        }).start();

        final String key2 = "beta-api-home.skysrt.com";
        ServerApiV2.getApi(HomeActivity.this,headerLoader).getAllConfigAsync(new ServerCallBack() {
            @Override
            public void onServerConfigLoaded(List<String> addressList,final Map<String, List<String>> result) {
                Log.d("test2", "onServerConfigLoaded, key=" + key2 + ", add=" + addressList);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                    }
                });
            }
        });



        new Thread(new Runnable() {
            @Override
            public void run() {
                String key3 = "beta-tc.skysrt.com";
                List<String > add3 = ServerApiV2.getApi(HomeActivity.this,headerLoader).getServerNameList(null);
                Log.d("test3", "key3=" + key3 + ", add3=" + add3);
            }
        }).start();

    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        mView.onNewIntent(intent);
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            boolean ret = mView.onKeyDown(event.getKeyCode());
            if (ret) {
                return true;
            }
            if (event.getKeyCode() == KeyEvent.KEYCODE_BACK || event.getKeyCode() == KeyEvent.KEYCODE_HOME) {
                if (CCPackageManager.INSTANCE.hasPackageChange()) {
                    CCLog.i("hasPackageChange, killMainProcess");
                    SmartHomeApplication.killMainProcess();
                }
            }
        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    protected void onResume() {
        super.onResume();
        mView.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        mView.onPause();
    }

    @Override
    protected void onStop() {
        super.onStop();
        mView.onStop();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mView != null) {
            mView.onDestroy();
        }
        if (mPresenter != null) {
            mPresenter.destroy();
        }
        CCPackageManager.INSTANCE.destroy(this);
        PluginManager.getInstance().destroy();
    }
}
