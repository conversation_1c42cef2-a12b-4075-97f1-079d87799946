package com.skyworth.smarthome_tv;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.os.Process;
import android.provider.Settings;
import android.support.multidex.MultiDex;
import android.text.TextUtils;
import android.util.Log;

import com.coocaa.operate6_0.presenter.PresenterFactory;
import com.skyworth.framework.skysdk.ipc.SkyApplication;
import com.skyworth.smarthome_tv.home.custom.HomePresenterFactory;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.smarthome.common.sal.SalImpl;
import com.smarthome.common.utils.Android;
import com.smarthome.common.utils.CCLog;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Author: AwenZeng
 * @CreateDate: 2020/11/27
 */
public class SmartHomeApplication extends SkyApplication {
    private static final String TAG = "SmartHomeApplicationTag";
    private static Context mContext;
    private static byte[] lock = new byte[0];
    public static List<Activity> activityList = new ArrayList<>();
    public static final boolean DEBUG = true;

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        try {
            MultiDex.install(this);
        } catch (Exception e) {
            e.printStackTrace();
            System.exit(0);
        }
        fixWatchDogDaemonBug();
        Thread.setDefaultUncaughtExceptionHandler(new Thread.UncaughtExceptionHandler() {
            @Override
            public void uncaughtException(Thread t, Throwable e) {
                e.printStackTrace();
            }
        });
    }

    @Override
    public void onCreate() {
        super.onCreate();
        String packageName = getPackageName();
        Log.d(TAG, "onCreate: ");
        if (TextUtils.equals(packageName, getCurrentProcessName())) {
            Log.d(TAG, "onCreate: same");
            try {
                Settings.System.getInt(getContentResolver(), "netSwitch");
            } catch (Settings.SettingNotFoundException e) {
                Log.d(TAG, "error: ");
                Settings.System.putInt(getContentResolver(), "netSwitch", 1);
                e.printStackTrace();
            }
        }
        init();
    }

    private void init() {
        mContext = this;
        try {
            Log.d(TAG,"SmartHomeApp");
            SalImpl.getSAL(mContext);
            if (Android.isMainProcess(this)) {//主进程才初始化
                registerActivityLifecycleCallbacks();
                PresenterFactory.getInstance().addExtraFactory(new HomePresenterFactory());//瀑布流框架自定义组件初始化
            }
            SmartHomeTvLib.init(this);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String getCurrentProcessName() {
        try {
            File file = new File("/proc/" + Process.myPid() + "/" + "cmdline");
            BufferedReader bufferedReader = new BufferedReader(new FileReader(file));
            String processName = bufferedReader.readLine().trim();
            bufferedReader.close();
            return processName;
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }


    public static Context getContext() {
        return mContext;
    }

    /**
     * 修复系统GC时，守护线程FinalizerDaemon挂掉的问题导致的异常
     */
    private void fixWatchDogDaemonBug() {
        try {
            final Class clazz = Class.forName("java.lang.Daemons$FinalizerWatchdogDaemon");
            final Field field = clazz.getDeclaredField("INSTANCE");
            field.setAccessible(true);
            final Object watchdog = field.get(null);
            try {
                final Field thread = clazz.getSuperclass().getDeclaredField("thread");
                thread.setAccessible(true);
                thread.set(watchdog, null);
            } catch (final Throwable t) {
                t.printStackTrace();
                try {
                    // 直接调用stop方法，在Android 6.0之前会有线程安全问题
                    final Method method = clazz.getSuperclass().getDeclaredMethod("stop");
                    method.setAccessible(true);
                    method.invoke(watchdog);
                } catch (final Throwable e) {
                    t.printStackTrace();
                }
            }
        } catch (final Throwable t) {
            t.printStackTrace();
        }

    }

    /**
     * 关掉所有的Activity
     */
    public static void finishActivity() {
        try {
            synchronized (lock) {
                for (Activity a : activityList) {
                    if (!a.isFinishing()) a.finish();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void killMainProcess() {
        finishActivity();
        try {
            if (Android.isMainProcess(getContext())) {
                int pid = android.os.Process.myPid();
                CCLog.i("killMainProcess :" + pid);
                android.os.Process.killProcess(pid);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    protected void registerActivityLifecycleCallbacks() {
        registerActivityLifecycleCallbacks(new ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
                synchronized (lock) {
                    if (!activityList.contains(activity)) activityList.add(activity);
                }
            }

            @Override
            public void onActivityStarted(Activity activity) {

            }

            @Override
            public void onActivityResumed(Activity activity) {

            }

            @Override
            public void onActivityPaused(Activity activity) {

            }

            @Override
            public void onActivityStopped(Activity activity) {

            }

            @Override
            public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

            }

            @Override
            public void onActivityDestroyed(Activity activity) {
                synchronized (lock) {
                    if (activityList.contains(activity)) activityList.remove(activity);
                }
            }
        });
    }
}
