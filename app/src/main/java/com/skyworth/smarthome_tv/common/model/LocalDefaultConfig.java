package com.skyworth.smarthome_tv.common.model;

import com.alibaba.fastjson.JSONObject;
import com.coocaa.operate6_0.model.MainData;
import com.skyworth.smarthome_tv.home.main.view.data.NavigationData;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 智慧家庭本地默认配置数据（拿不到后台数据或者异常的情况下使用，保证进入APP不会为空）
 * @Author: wzh
 * @CreateDate: 2020/7/9
 */
public class LocalDefaultConfig {

    public final static int TAG_ID_MALL = 1;
    public final static int TAG_ID_SCENES = 2;
    public final static int TAG_ID_SMARTDEVICE = 3;

    /**
     * 导航栏tab数据
     *
     * @return
     */
    public static List<NavigationData> getHomeTags(boolean isSystem_8) {
        List<NavigationData> datas = new ArrayList<>();
        datas.add(new NavigationData(TAG_ID_SMARTDEVICE, "设备"));
        datas.add(new NavigationData(TAG_ID_SCENES, "场景"));
        datas.add(new NavigationData(TAG_ID_MALL, "发现"));
        return datas;
    }

    public static MainData getMainData(int tagId) {
        switch (tagId) {
            case TAG_ID_MALL:
                return getMall();
            case TAG_ID_SMARTDEVICE:
                return getSmartDevice();
            case TAG_ID_SCENES:
                return getScenes();
            default:
                return new MainData();
        }
    }

    /**
     * 发现-购物版面数据(插件形式)
     *
     * @return
     */
    public static MainData getMall() {
        String json = "{\n" +
                "    \"content\": {\n" +
                "        \"bg\": \"\",\n" +
                "        \"contents\": [\n" +
                "            {\n" +
                "                \"bg\": \"\",\n" +
                "                \"contents\": null,\n" +
                "                \"extra\": {\n" +
                "                    \"data_from\": {\n" +
                "                        \"info\": null,\n" +
                "                        \"type\": \"mark\"\n" +
                "                    },\n" +
                "                    \"dmp_info\": null,\n" +
                "                    \"focus_config\": \"\",\n" +
                "                    \"focus_shape\": 0,\n" +
                "                    \"panel_id\": \"10234433\",\n" +
                "                    \"panel_name\": \"智慧家庭-购物组件\",\n" +
                "                    \"panel_source_type\": \"0\",\n" +
                "                    \"panel_version\": \"V2\",\n" +
                "                    \"rec_stream_info\": null,\n" +
                "                    \"sub_panels\": null,\n" +
                "                    \"title\": null\n" +
                "                },\n" +
                "                \"focusable\": 0,\n" +
                "                \"height\": 0,\n" +
                "                \"id\": \"1025729\",\n" +
                "                \"parents\": \"\",\n" +
                "                \"type\": \"SMART_HOME_PANEL_MALL\",\n" +
                "                \"width\": 0,\n" +
                "                \"x\": 0,\n" +
                "                \"y\": 0\n" +
                "            }\n" +
                "        ],\n" +
                "        \"extra\": {\n" +
                "            \"bgLong\": \"\",\n" +
                "            \"orientation\": 1,\n" +
                "            \"space\": 40,\n" +
                "            \"use_bg\": 0\n" +
                "        },\n" +
                "        \"focusable\": 0,\n" +
                "        \"height\": 0,\n" +
                "        \"id\": \"\",\n" +
                "        \"parents\": \"\",\n" +
                "        \"type\": \"Expander\",\n" +
                "        \"width\": 0,\n" +
                "        \"x\": 0,\n" +
                "        \"y\": 0\n" +
                "    },\n" +
                "    \"cycle_time\": 120,\n" +
                "    \"md5\": \"Y7ZtiN4SifOQP9Dm\",\n" +
                "    \"refresh_info\": null,\n" +
                "    \"tab_config\": {\n" +
                "        \"block_field_focus_color\": \"\",\n" +
                "        \"block_line_focus_color\": \"\",\n" +
                "        \"block_title_focus_color\": \"\",\n" +
                "        \"block_title_unfocus_color\": \"\",\n" +
                "        \"panel_title_color\": \"\"\n" +
                "    }\n" +
                "}";
        return JSONObject.parseObject(json, MainData.class);
    }

    /**
     * 智能设备版面数据(插件形式，默认只有设备列表和场景列表)
     *
     * @return
     */
    public static MainData getSmartDevice() {
        String json = "{\n" +
                "    \"content\": {\n" +
                "        \"bg\": \"\",\n" +
                "        \"contents\": [\n" +
                "            {\n" +
                "                \"bg\": \"\",\n" +
                "                \"contents\": null,\n" +
                "                \"extra\": {\n" +
                "                    \"data_from\": {\n" +
                "                        \"info\": null,\n" +
                "                        \"type\": \"mark\"\n" +
                "                    },\n" +
                "                    \"dmp_info\": null,\n" +
                "                    \"focus_config\": \"\",\n" +
                "                    \"focus_shape\": 0,\n" +
                "                    \"panel_id\": \"10234436\",\n" +
                "                    \"panel_name\": \"智慧家庭-设备列表组件\",\n" +
                "                    \"panel_source_type\": \"0\",\n" +
                "                    \"panel_version\": \"V1\",\n" +
                "                    \"rec_stream_info\": null,\n" +
                "                    \"sub_panels\": null,\n" +
                "                    \"title\": null\n" +
                "                },\n" +
                "                \"focusable\": 0,\n" +
                "                \"height\": 0,\n" +
                "                \"id\": \"1025733\",\n" +
                "                \"parents\": \"\",\n" +
                "                \"type\": \"SMART_HOME_PANEL_DEVICE\",\n" +
                "                \"width\": 0,\n" +
                "                \"x\": 0,\n" +
                "                \"y\": 0\n" +
                "            },\n" +
                "            {\n" +
                "                \"bg\": \"\",\n" +
                "                \"contents\": [\n" +
                "                    {\n" +
                "                        \"bg\": \"\",\n" +
                "                        \"contents\": null,\n" +
                "                        \"extra\": {\n" +
                "                            \"block_new_title_info\": {\n" +
                "                                \"sub_title\": {\n" +
                "                                    \"show\": 0,\n" +
                "                                    \"text\": \"\"\n" +
                "                                },\n" +
                "                                \"title\": {\n" +
                "                                    \"show\": 0,\n" +
                "                                    \"text\": \"智能生态\"\n" +
                "                                }\n" +
                "                            },\n" +
                "                            \"block_content_info\": {\n" +
                "                                \"imgs\": {\n" +
                "                                    \"corner_icons\": [],\n" +
                "                                    \"focus_img_url\": \"\",\n" +
                "                                    \"poster\": {\n" +
                "                                        \"images\": [\n" +
                "                                            \"http://img.sky.fs.skysrt.com/tvos6_imgs_master/20200819/20200819104212329694_560*220.png\"\n" +
                "                                        ],\n" +
                "                                        \"special_poster_flag\": 0\n" +
                "                                    }\n" +
                "                                },\n" +
                "                                \"action\": \"{\\\"byvalue\\\":\\\"coocaa.intent.action.browser\\\",\\\"packagename\\\":\\\"com.coocaa.app_browser\\\",\\\"dowhat\\\":\\\"startActivity\\\",\\\"versioncode\\\":\\\"4000451\\\",\\\"params\\\":{\\\"url\\\":\\\"https://webapp.skysrt.com/cc7.0/guide2/index.html?source=aiot&index=0\\\"},\\\"bywhat\\\":\\\"action\\\",\\\"exception\\\":{\\\"name\\\":\\\"onclick_exception\\\",\\\"value\\\":{\\\"packagename\\\":\\\"com.tianci.appstore\\\",\\\"dowhat\\\":\\\"startActivity\\\",\\\"versioncode\\\":\\\"-1\\\",\\\"params\\\":{\\\"id\\\":\\\"com.coocaa.app_browser\\\"},\\\"byvalue\\\":\\\"coocaa.intent.action.APP_STORE_DETAIL\\\",\\\"bywhat\\\":\\\"action\\\"}}}\",\n" +
                "                                \"title\": \"智能生态\"\n" +
                "                            },\n" +
                "                            \"block_type_info\": {\n" +
                "                                \"lucency_flag\": \"0\"\n" +
                "                            },\n" +
                "                            \"params\": \"{}\",\n" +
                "                            \"vector_tag_flag\": 0,\n" +
                "                            \"normal_special_flag\": 1\n" +
                "                        },\n" +
                "                        \"focusable\": 1,\n" +
                "                        \"height\": 220,\n" +
                "                        \"id\": \"103563799\",\n" +
                "                        \"parents\": \"\",\n" +
                "                        \"type\": \"Block\",\n" +
                "                        \"width\": 560,\n" +
                "                        \"x\": 0,\n" +
                "                        \"y\": 0\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"bg\": \"\",\n" +
                "                        \"contents\": null,\n" +
                "                        \"extra\": {\n" +
                "                            \"block_new_title_info\": {\n" +
                "                                \"sub_title\": {\n" +
                "                                    \"show\": 0,\n" +
                "                                    \"text\": \"\"\n" +
                "                                },\n" +
                "                                \"title\": {\n" +
                "                                    \"show\": 0,\n" +
                "                                    \"text\": \"设备互联互通\"\n" +
                "                                }\n" +
                "                            },\n" +
                "                            \"block_content_info\": {\n" +
                "                                \"imgs\": {\n" +
                "                                    \"corner_icons\": [],\n" +
                "                                    \"focus_img_url\": \"\",\n" +
                "                                    \"poster\": {\n" +
                "                                        \"images\": [\n" +
                "                                            \"http://img.sky.fs.skysrt.com/tvos6_imgs_master/20200819/20200819104953575550_560*220.png\"\n" +
                "                                        ],\n" +
                "                                        \"special_poster_flag\": 0\n" +
                "                                    }\n" +
                "                                },\n" +
                "                                \"action\": \"{\\\"byvalue\\\":\\\"coocaa.intent.action.browser\\\",\\\"packagename\\\":\\\"com.coocaa.app_browser\\\",\\\"dowhat\\\":\\\"startActivity\\\",\\\"versioncode\\\":\\\"4000451\\\",\\\"params\\\":{\\\"url\\\":\\\"https://webapp.skysrt.com/cc7.0/guide2/index.html?source=aiot&index=1\\\"},\\\"bywhat\\\":\\\"action\\\",\\\"exception\\\":{\\\"name\\\":\\\"onclick_exception\\\",\\\"value\\\":{\\\"packagename\\\":\\\"com.tianci.appstore\\\",\\\"dowhat\\\":\\\"startActivity\\\",\\\"versioncode\\\":\\\"-1\\\",\\\"params\\\":{\\\"id\\\":\\\"com.coocaa.app_browser\\\"},\\\"byvalue\\\":\\\"coocaa.intent.action.APP_STORE_DETAIL\\\",\\\"bywhat\\\":\\\"action\\\"}}}\",\n" +
                "                                \"title\": \"设备互联互通\"\n" +
                "                            },\n" +
                "                            \"block_type_info\": {\n" +
                "                                \"lucency_flag\": \"0\"\n" +
                "                            },\n" +
                "                            \"params\": \"{}\",\n" +
                "                            \"vector_tag_flag\": 0,\n" +
                "                            \"normal_special_flag\": 2\n" +
                "                        },\n" +
                "                        \"focusable\": 1,\n" +
                "                        \"height\": 220,\n" +
                "                        \"id\": \"103563800\",\n" +
                "                        \"parents\": \"\",\n" +
                "                        \"type\": \"Block\",\n" +
                "                        \"width\": 560,\n" +
                "                        \"x\": 600,\n" +
                "                        \"y\": 0\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"bg\": \"\",\n" +
                "                        \"contents\": null,\n" +
                "                        \"extra\": {\n" +
                "                            \"block_new_title_info\": {\n" +
                "                                \"sub_title\": {\n" +
                "                                    \"show\": 0,\n" +
                "                                    \"text\": \"\"\n" +
                "                                },\n" +
                "                                \"title\": {\n" +
                "                                    \"show\": 0,\n" +
                "                                    \"text\": \"绑定SwaiotPANEL\"\n" +
                "                                }\n" +
                "                            },\n" +
                "                            \"block_content_info\": {\n" +
                "                                \"imgs\": {\n" +
                "                                    \"corner_icons\": [],\n" +
                "                                    \"focus_img_url\": \"\",\n" +
                "                                    \"poster\": {\n" +
                "                                        \"images\": [\n" +
                "                                            \"http://img.sky.fs.skysrt.com/tvos6_imgs_master/20210317/20210317163608638803_560*220.png\"\n" +
                "                                        ],\n" +
                "                                        \"special_poster_flag\": 0\n" +
                "                                    }\n" +
                "                                },\n" +
                "                                \"action\": \"{\\\"byvalue\\\":\\\"swaiotos.channel.iot.tv.MainActivity\\\",\\\"packagename\\\":\\\"swaiotos.channel.iot\\\",\\\"dowhat\\\":\\\"startActivity\\\",\\\"versioncode\\\":\\\"-1\\\",\\\"params\\\":{},\\\"bywhat\\\":\\\"class\\\",\\\"exception\\\":{\\\"name\\\":\\\"onclick_exception\\\",\\\"value\\\":{\\\"packagename\\\":\\\"com.tianci.appstore\\\",\\\"dowhat\\\":\\\"startActivity\\\",\\\"versioncode\\\":\\\"-1\\\",\\\"params\\\":{\\\"id\\\":\\\"swaiotos.channel.iot\\\"},\\\"byvalue\\\":\\\"coocaa.intent.action.APP_STORE_DETAIL\\\",\\\"bywhat\\\":\\\"action\\\"}}}\",\n" +
                "                                \"title\": \"绑定SwaiotPANEL\"\n" +
                "                            },\n" +
                "                            \"block_type_info\": {\n" +
                "                                \"lucency_flag\": \"0\"\n" +
                "                            },\n" +
                "                            \"params\": \"\",\n" +
                "                            \"vector_tag_flag\": 0,\n" +
                "                            \"normal_special_flag\": 3\n" +
                "                        },\n" +
                "                        \"focusable\": 1,\n" +
                "                        \"height\": 220,\n" +
                "                        \"id\": \"103563801\",\n" +
                "                        \"parents\": \"\",\n" +
                "                        \"type\": \"Block\",\n" +
                "                        \"width\": 560,\n" +
                "                        \"x\": 1200,\n" +
                "                        \"y\": 0\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"extra\": {\n" +
                "                    \"data_from\": {\n" +
                "                        \"info\": null,\n" +
                "                        \"type\": \"ops\"\n" +
                "                    },\n" +
                "                    \"dmp_info\": null,\n" +
                "                    \"focus_shape\": 0,\n" +
                "                    \"panel_id\": \"10395720\",\n" +
                "                    \"panel_name\": \"玩法精选\",\n" +
                "                    \"panel_source_type\": \"0\",\n" +
                "                    \"panel_version\": \"V9\",\n" +
                "                    \"rec_stream_info\": null,\n" +
                "                    \"title\": {\n" +
                "                        \"align\": 1,\n" +
                "                        \"color\": \"\",\n" +
                "                        \"size\": 0,\n" +
                "                        \"text\": \"玩法精选\"\n" +
                "                    }\n" +
                "                },\n" +
                "                \"focusable\": 0,\n" +
                "                \"height\": 0,\n" +
                "                \"id\": \"1025687\",\n" +
                "                \"parents\": \"\",\n" +
                "                \"type\": \"Panel\",\n" +
                "                \"width\": 0,\n" +
                "                \"x\": 0,\n" +
                "                \"y\": 0\n" +
                "            }\n" +
                "        ],\n" +
                "        \"extra\": {\n" +
                "            \"bgLong\": \"\",\n" +
                "            \"only_show_first_screen\": 0,\n" +
                "            \"orientation\": 1,\n" +
                "            \"space\": 40,\n" +
                "            \"use_bg\": 0\n" +
                "        },\n" +
                "        \"focusable\": 0,\n" +
                "        \"height\": 0,\n" +
                "        \"id\": \"\",\n" +
                "        \"parents\": \"\",\n" +
                "        \"type\": \"Expander\",\n" +
                "        \"width\": 0,\n" +
                "        \"x\": 0,\n" +
                "        \"y\": 0\n" +
                "    }\n" +
                "}";
        return JSONObject.parseObject(json, MainData.class);
    }

    public static MainData getScenes() {
        String json = "{\n" +
                "    \"content\": {\n" +
                "        \"bg\": \"\",\n" +
                "        \"contents\": [\n" +
                "            {\n" +
                "               \"bg\": \"\",\n" +
                "                \"contents\": null,\n" +
                "                \"extra\": {\n" +
                "                    \"data_from\": {\n" +
                "                        \"info\": null,\n" +
                "                        \"type\": \"mark\"\n" +
                "                    },\n" +
                "                    \"dmp_info\": null,\n" +
                "                    \"focus_config\": \"\",\n" +
                "                    \"focus_shape\": 0,\n" +
                "                    \"panel_id\": \"10234438\",\n" +
                "                    \"panel_name\": \"智慧家庭-场景列表组件\",\n" +
                "                    \"panel_source_type\": \"0\",\n" +
                "                    \"panel_version\": \"V1\",\n" +
                "                    \"rec_stream_info\": null,\n" +
                "                    \"sub_panels\": null,\n" +
                "                    \"title\": null\n" +
                "                },\n" +
                "                \"focusable\": 0,\n" +
                "                \"height\": 0,\n" +
                "                \"id\": \"1025734\",\n" +
                "                \"parents\": \"\",\n" +
                "                \"type\": \"SMART_HOME_PANEL_SCENE_MANUAL\",\n" +
                "                \"width\": 0,\n" +
                "                \"x\": 0,\n" +
                "                \"y\": 0\n" +
                "            },\n" +
                "            {\n" +
                "                \"bg\": \"\",\n" +
                "                \"contents\": [\n" +
                "            {\n" +
                "               \"bg\": \"\",\n" +
                "                \"contents\": null,\n" +
                "                \"extra\": {\n" +
                "                    \"data_from\": {\n" +
                "                        \"info\": null,\n" +
                "                        \"type\": \"mark\"\n" +
                "                    },\n" +
                "                    \"dmp_info\": null,\n" +
                "                    \"focus_config\": \"\",\n" +
                "                    \"focus_shape\": 0,\n" +
                "                    \"panel_id\": \"10234438\",\n" +
                "                    \"panel_name\": \"智慧家庭-场景列表组件\",\n" +
                "                    \"panel_source_type\": \"0\",\n" +
                "                    \"panel_version\": \"V1\",\n" +
                "                    \"rec_stream_info\": null,\n" +
                "                    \"sub_panels\": null,\n" +
                "                    \"title\": null\n" +
                "                },\n" +
                "                \"focusable\": 0,\n" +
                "                \"height\": 0,\n" +
                "                \"id\": \"1025734\",\n" +
                "                \"parents\": \"\",\n" +
                "                \"type\": \"SMART_HOME_PANEL_SCENE_AUTO\",\n" +
                "                \"width\": 0,\n" +
                "                \"x\": 0,\n" +
                "                \"y\": 0\n" +
                "            },\n" +
                "                ],\n" +
                "                \"extra\": {\n" +
                "                    \"data_from\": {\n" +
                "                        \"info\": null,\n" +
                "                        \"type\": \"ops\"\n" +
                "                    },\n" +
                "                    \"dmp_info\": null,\n" +
                "                    \"focus_shape\": 0,\n" +
                "                    \"panel_id\": \"10395720\",\n" +
                "                    \"panel_name\": \"自动执行\",\n" +
                "                    \"panel_source_type\": \"0\",\n" +
                "                    \"panel_version\": \"V1\",\n" +
                "                    \"rec_stream_info\": null,\n" +
                "                },\n" +
                "                \"focusable\": 0,\n" +
                "                \"height\": 0,\n" +
                "                \"id\": \"1027324\",\n" +
                "                \"parents\": \"\",\n" +
                "                \"type\": \"Panel\",\n" +
                "                \"width\": 0,\n" +
                "                \"x\": 0,\n" +
                "                \"y\": 0\n" +
                "            }\n" +
                "        ],\n" +
                "        \"extra\": {\n" +
                "            \"bgLong\": \"\",\n" +
                "            \"orientation\": 1,\n" +
                "            \"space\": 40,\n" +
                "            \"use_bg\": 0\n" +
                "        },\n" +
                "        \"focusable\": 0,\n" +
                "        \"height\": 0,\n" +
                "        \"id\": \"\",\n" +
                "        \"parents\": \"\",\n" +
                "        \"type\": \"Expander\",\n" +
                "        \"width\": 0,\n" +
                "        \"x\": 0,\n" +
                "        \"y\": 0\n" +
                "    }\n" +
                "}";
        return JSONObject.parseObject(json, MainData.class);
    }

}
