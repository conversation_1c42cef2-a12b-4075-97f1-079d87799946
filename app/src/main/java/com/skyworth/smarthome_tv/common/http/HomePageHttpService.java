package com.skyworth.smarthome_tv.common.http;

import com.coocaa.app.core.http.HttpServiceManager;
import com.coocaa.operate6_0.model.Container;
import com.coocaa.operate6_0.model.MainData;
import com.skyworth.smarthome.common.http.HttpManager;
import com.skyworth.smarthome_tv.common.bean.HomeTabBean;
import com.smarthome.common.model.SmartBaseData;
import com.smarthome.common.model.SmartBaseListData;

import java.util.Map;

import retrofit2.Call;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/10
 */
public class HomePageHttpService extends HttpManager<HomePageHttpMethod> {

    public static final HomePageHttpService SERVICE = new HomePageHttpService();

//    public HomePageHttpService(String server) {
//        super(HomePageHttpConfig.getServer(server), HomePageHttpConfig.SMARTHOME_HEADER_LOADER);
//    }

//    @Override
//    protected Class<HomePageHttpMethod> getServiceClazz() {
//        return HomePageHttpMethod.class;
//    }

    public Call<SmartBaseData<HomeTabBean>> getHomeTabs() {
        return getHttpService().getHomeTabs("1");
    }

    public Call<SmartBaseData<HomeTabBean>> getBusinessTabs() {
        return getHttpService().getBusinessTabs(30);
    }

    public Call<SmartBaseData<MainData>> getOperateFirstPageData(String tagId, String layoutStyle) {
        return getHttpService().getOperateFirstPageData(tagId, layoutStyle, "1", "9", "3");
    }

    public Call<SmartBaseListData<Container>> getOperateNextPageData(String url) {
        return getHttpService().getOperateNextPageData(url);
    }

    @Override
    protected Class<HomePageHttpMethod> getServiceClass() {
        return HomePageHttpMethod.class;
    }

    @Override
    protected Map<String, String> getHeaders() {
        return HomePageHttpConfig.SMARTHOME_HEADER_LOADER.getHeader();
    }

    @Override
    protected String getBaseUrl() {
        return HomePageHttpConfig.getServer(HomePageHttpConfig.HOMEPAGE_SERVER);
    }
}
