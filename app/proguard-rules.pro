# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in D:\sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html

-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# Optimization is turned off by default. Dex does not like code run
# through the ProGuard optimize and preverify steps (and performs some
# of these optimizations on its own).
-dontoptimize
-dontpreverify
#保持无用的类不被清理掉
#-dontshrink
# Note that if you want to enable optimization, you cannot just
# include optimization flags in your own project configuration file;
# instead you will need to point to the
# "proguard-android-optimize.txt" file instead of this one from your
# project.properties file.

-keepattributes *Annotation*
-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService

# For native methods, see http://proguard.sourceforge.net/manual/examples.html#native
-keepclasseswithmembernames class * {
    native <methods>;
}

# keep setters in Views so that animations can still work.
# see http://proguard.sourceforge.net/manual/examples.html#beans
-keepclassmembers public class * extends android.view.View {
   void set*(***);
   *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick
-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

# For enumeration classes, see http://proguard.sourceforge.net/manual/examples.html#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator CREATOR;
}

-keepclassmembers class **.R$* {
    public static <fields>;
}

# The support library contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version.  We know about them, and they are safe.
-dontwarn android.support.**

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

# 保护注解
-keepattributes *Annotation*
-keep class * extends java.lang.annotation.Annotation {*;}

# 泛型与反射
-keepattributes Signature
-keepattributes EnclosingMethod

# 不混淆内部类
-keepattributes InnerClasses


#基本的方法
-keep public class * extends android.app.Fragment
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
-keep public class * extends android.support.v7.app.AppCompatActivity


#support-v4
-keep public class * extends android.support.v4.app.Fragment
# 如果引用了v4或者v7包
-dontwarn android.support.**

-keep class * implements java.io.Serializable {*;}

#---------------------smarthome-app----------start-------
-keep class com.skyworth.smarthome.common.bean.**{*;}
-keep class com.skyworth.smarthome.common.http.**{*;}
-keep class com.skyworth.smarthome.common.event.**{*;}
-keep class com.skyworth.smarthome.common.ui.**{*;}
-keep class com.skyworth.smarthome.home.smartdevice.controlpanel.common.**{*;}

-keep class com.smarthome.common.account.**{*;}
-keep class com.smarthome.common.model.**{*;}

-dontwarn com.skyworth.smarthome_tv.devices.discover.view.NotScannedHelpView
-dontwarn com.skyworth.smarthome_tv.infrared.controldevices.view.InfraredGuideView

#kotlin
-dontwarn kotlin.**
-keep class kotlin.** {*;}

#---MovieStartApi------------
-keep class com.coocaa.moviestartapi.data.**{*;}

#---zxing.jar---
-keep class com.google.zxing.**{*;}

#fresco
-keep class com.facebook.**{*;}

## LogSDK
-dontwarn com.squareup.okhttp.**
-keep class com.squareup.okhttp.**{*;}
-keep public class * implements java.io.Serializable {*;}

#酷控SDK
-dontwarn com.kookong.**
-dontwarn com.lidroid.xutils.**
-dontwarn org.w3c.dom.bootstrap.DOMImplementationRegistry

#保持插件类不被清理掉
-keep class com.smarthome.plugin.**{*;}

#---------------------smarthome-app----------end-------

##---------------------smarthome-aiot-lib----------start-------
-keep class com.swaiot.aiotlib.common.bean.**{*;}
-keep class com.swaiot.aiotlib.common.http.**{*;}

#okhttp3
-dontwarn okhttp3.**
-dontwarn com.squareup.okhttp3.**
-keep class com.squareup.okhttp3.** { *;}
-dontwarn okio.**

#美的Sdk
-dontwarn com.midea.iot.sdk.**

#Gson
-dontwarn com.google.gson.**
-keep class com.google.gson.** {*;}

# 账号
-keep class retrofit2.converter.fastjson.** {*;}
-keep class com.alibaba.fastjson.**{*;}

-keep class * implements java.io.Serializable {*;}

-keep class * implements Android.os.Parcelable {
  public static final Android.os.Parcelable$Creator *;
}

#EventBus
-keep class org.greenrobot.eventbus.**{*;}
-keepattributes *Annotation*
-keepclassmembers class ** {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keepclassmembers class ** {
    public void onEvent(**);
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }
# Only required if you use AsyncExecutor
-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(Java.lang.Throwable);
}

#rustlib库
-keep class com.swaiot.lib.** {*;}

#iot-channel
-keep class swaiotos.channel.iot.**{*;}

#----wisefy-------
-keep class com.isupatches.wisefy.**{*;}

#smarthome-aiot-sdk
-keep class com.swaiot.aiotlib.common.**{*;}
-keep class com.swaiot.aiotlib.IBinderPool{*;}
-keep class com.swaiot.aiotlib.devcie.**{*;}
-keep class com.swaiot.aiotlib.family.**{*;}
-keep class com.swaiot.aiotlib.scene.**{*;}
-keep class com.swaiot.aiotlib.push.**{*;}

##---------------------smarthome-aiot-lib----------end-------

#智家插件
-dontwarn com.skyworth.smarthome_tv.smarthomeplugininterface.**
-keep class com.skyworth.smarthome_tv.smarthomeplugininterface.**{*;}

# 主页插件过滤混淆规则
-dontwarn com.ccos.tvlauncher.sdk.**
-keep class com.ccos.tvlauncher.sdk.** {*;}
-keep class * implements java.io.Serializable {*;}
-keep class android.content.pm.**{*;}
-keep class android.content.res.**{*;}
-keep class * extends java.lang.annotation.Annotation { *; }
-keep interface * extends java.lang.annotation.Annotation { *; }

-keep class * implements com.ccos.tvlauncher.sdk.ITvLauncherPlugin {*;}
-keep class * extends com.ccos.tvlauncher.sdk.BaseTvLauncherPlugin {*;}
-keep class * implements com.ccos.tvlauncher.sdk.IPluginConnector {*;}
-keep class * implements com.ccos.tvlauncher.sdk.TvLauncherPluginCallback {*;}
-keep class * implements com.ccos.tvlauncher.sdk.TvLauncherPluginBoundaryCallback {*;}




