apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'

def ver_major = 5
def ver_minor = 5
def ver_build = getDate()

android {
    compileSdkVersion COMPILE_SDK_VERSION
    buildToolsVersion BUILDTOOLS_VERSION

    defaultConfig {
        multiDexEnabled true
        applicationId "com.skyworth.smarthome_tv"
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION
        versionCode 506102212
        versionName 5 + "." + 6 + "." + 102212

        ndk {
            abiFilters "armeabi-v7a"
        }

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        release {
            keyAlias sign_config["keystore.alias"]
            keyPassword sign_config["keystore.alias_password"]
            storeFile file("../${sign_config["keystore.path"]}")
            storePassword sign_config["keystore.password"]
        }
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            manifestPlaceholders = [DEVICE_SERVER_VALUE  : "https://api-sit.skyworthiot.com/",
                                    HOMEPAGE_SERVER_VALUE: "http://beta-api-home.skysrt.com/",
                                    APPSTORE_SERVER_VALUE: "http://beta-tc.skysrt.com/appstore/appstorev3/"]
        }
        release {
            debuggable false
            minifyEnabled true
            shrinkResources true
            zipAlignEnabled true
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            manifestPlaceholders = [DEVICE_SERVER_VALUE  : "https://api.skyworthiot.com/",
                                    HOMEPAGE_SERVER_VALUE: "http://api.home.skysrt.com/v1/",
                                    APPSTORE_SERVER_VALUE: "https://tc.skysrt.com/appstore/appstorev3/"]
        }
    }

    android.applicationVariants.all {
        variant ->
            variant.outputs.all {
                outputFileName = "newtv_smarthome_v${defaultConfig.versionCode}_${buildType.name}.apk"
            }
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }


    tasks.withType(JavaCompile) {
        options.encoding = "UTF-8"
    }

}

configurations.all {
    resolutionStrategy {
        force 'com.alibaba:fastjson:1.2.48'
    }
}

repositories {
    flatDir {
        dirs 'libs'
    }
}

static def getDate() {
    def date = new Date()
    def formattedDate = date.format('MMddHH')
    return formattedDate
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(path: ':iot-channel')
    testImplementation 'junit:junit:4.12'
    //noinspection GradleCompatible
    implementation 'com.android.support:appcompat-v7:28.0.0'
    //others
    implementation 'org.greenrobot:eventbus:3.0.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    //local sdk
    implementation project(':Common')
    implementation project(':smarthome-tv')
    implementation project(":smarthome-aiot-sdk")
    implementation project(":MainPagePlugin")
    implementation project(path: ':PluginManager')
    implementation project(":mall_app")
    implementation project(":SmartHomePluginInterface")

    //swaiotos maven
    implementation 'swaiotos.support:appcore:1.0.57'
    implementation 'swaiotos.ui:common:1.0.57'
    implementation 'swaiotos.ui:app-v6:1.0.57'
    implementation 'swaiotos.support:log:1.0.57'
    implementation 'swaiotos:sal:1.0.57'
    implementation 'swaiotos.ui:imageloader:1.0.57'
    implementation 'swaiotos.base:okhttp:1.0.57'
    def swaiotosVersion = "1.0.241"
    implementation "swaiotos.service:server-verify:${swaiotosVersion}"
//    implementation "swaiotos:sal:${swaiotosVersion}"
}
