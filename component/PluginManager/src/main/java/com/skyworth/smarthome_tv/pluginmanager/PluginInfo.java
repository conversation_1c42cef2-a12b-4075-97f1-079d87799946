package com.skyworth.smarthome_tv.pluginmanager;

import com.ccos.plugin.Plugin;
import com.skyworth.smarthome_tv.smarthomeplugininterface.ISmartHomePluginInterface;

/**
 * @ClassName: PluginInfo
 * @Author: Xu<PERSON>eXiao
 * @CreateDate: 2020/6/12 10:56
 * @Description:
 */
public class PluginInfo {
    public static final int PLUGIN_STATE_NOT_LOAD = 0;
    public static final int PLUGIN_STATE_LOADING = 1;
    public static final int PLUGIN_STATE_READY = 2;

    public Plugin plugin = null;
    public ISmartHomePluginInterface i = null;
    public int state = 0;

    public PluginInfo(Plugin plugin, ISmartHomePluginInterface i, int state) {
        this.plugin = plugin;
        this.i = i;
        this.state = state;
    }
}
