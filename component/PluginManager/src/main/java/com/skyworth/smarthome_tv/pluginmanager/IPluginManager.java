package com.skyworth.smarthome_tv.pluginmanager;

import android.content.Context;
import android.os.Bundle;
import android.view.WindowManager;

import com.ccos.tvlauncher.sdk.IPluginConnector;
import com.skyworth.smarthome_tv.pluginmanager.boundarycallback.IBoundaryCallback;
import com.skyworth.smarthome_tv.smarthomeplugininterface.ISmartHomeConnector;
import com.skyworth.smarthome_tv.smarthomeplugininterface.ShortcutState;

/**
 * @ClassName: IPluginManager
 * @Author: XuZeXiao
 * @CreateDate: 2020/6/12 10:51
 * @Description:
 */
public interface IPluginManager {
    /**
     * 初始化方法
     *
     * @param context
     * @param windowManager 如果插件中需要弹框，则需要传
     */
    void init(Context context, WindowManager windowManager);

    /**
     * 设置新TV主页connector
     *
     * @param connector
     */
    void setConnector(IPluginConnector connector);

    /**
     * 设置智家宿主connector
     *
     * @param connector
     */
    void setConnector2(ISmartHomeConnector connector);

    /**
     * 从插件中获取View，调用时不需要切换线程
     * 异步方法，获取的View在回调中返回，回调方法在主线程中。
     *
     * @param packageName
     * @param type
     * @param listener
     */
    void getView(String packageName, String type, IPluginViewLoadListener listener, IBoundaryCallback boundaryCallback);

    /**
     * 提供其他进程通过主页通知插件，传数据的能力
     *
     * @param bundle
     */
    void onDeliverPluginMessage(Bundle bundle);

    /**
     * 内容区域状态变化（展开/收缩）
     */
    void onShortcutStateChanged(@ShortcutState int state);

    /**
     * 退出应用时清理已加载的插件
     */
    void destroy();
}
