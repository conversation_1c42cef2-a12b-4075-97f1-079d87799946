package com.skyworth.smarthome_tv.pluginmanager.type;

import android.view.View;

import com.skyworth.smarthome_tv.pluginmanager.PluginRequestData;
import com.skyworth.smarthome_tv.smarthomeplugininterface.ISmartHomePluginInterface;

/**
 * @ClassName: PluginTypePanelView
 * @Author: XuZeXiao
 * @CreateDate: 2020/6/15 17:41
 * @Description:
 */
public class PluginViewTypePanelView implements IPluginViewType {
    public static final String TYPE = "PANEL_VIEW";
    private static final PluginViewTypePanelView INSTANCE = new PluginViewTypePanelView();

    private PluginViewTypePanelView() {

    }

    public static PluginViewTypePanelView get() {
        return INSTANCE;
    }

    @Override
    public LoadedView loadView(ISmartHomePluginInterface iSmartHomePluginInterface, PluginRequestData requestData) {
        if (iSmartHomePluginInterface == null || requestData == null) {
            return null;
        }
        LoadedView loadedView = new LoadedView();
        try {
            loadedView.view = iSmartHomePluginInterface.getPanelView(requestData.boundaryCallback);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        try {
            loadedView.lifeCycleCallback = iSmartHomePluginInterface.getPanelLifeCycleCallback();
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return loadedView;
    }
}
