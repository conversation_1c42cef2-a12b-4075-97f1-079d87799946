package com.skyworth.smarthome_tv.pluginmanager.conector;

import com.ccos.tvlauncher.sdk.IPluginConnector;
import com.skyworth.smarthome_tv.smarthomeplugininterface.ISmartHomeConnector;

import java.util.Map;

/**
 * @ClassName: PluginConnectorProxy
 * @Author: XuZeXiao
 * @CreateDate: 2020/7/9 11:09
 * @Description:
 */
public class PluginConnectorProxy implements ISmartHomeConnector {
    private IPluginConnector connector = null;
    private Iot iot = null;
    private User user = null;
    private Executor executor = null;
    private Logger logger = null;

    public PluginConnectorProxy(IPluginConnector connector) {
        this.connector = connector;
    }

    @Override
    public User user() {
        if (user == null) {
            user = new User() {
                @Override
                public UserInfo getUserInfo() {
                    IPluginConnector.User.UserInfo userInfo = connector.user().getUserInfo();
                    UserInfo result = new UserInfo();
                    result.info = userInfo.info;
                    result.token = userInfo.token;
                    return result;
                }

                @Override
                public boolean hasLogin() {
                    return connector.user().hasLogin();
                }

                @Override
                public boolean login(Map<String, String> params, boolean needFinish, String from) {
                    return connector.user().login(params, needFinish, from);
                }

                @Override
                public void addUserChangeListener(UserChangeListener listener) {

                }

                @Override
                public void removeUserChangeListener(UserChangeListener listener) {

                }
            };
        }
        return user;
    }

    @Override
    public Executor executor() {
        if (executor == null) {
            executor = new Executor() {
                @Override
                public void execute(Runnable runnable) {
                    connector.executor().execute(runnable);
                }

                @Override
                public void execute(Runnable runnable, long delay) {
                    connector.executor().execute(runnable, delay);
                }
            };
        }
        return executor;
    }

    @Override
    public Logger logger() {
        if (logger == null) {
            logger = new Logger() {
                @Override
                public void pageResumeEvent(String pageName, Map<String, String> params) {
                    connector.logger().pageResumeEvent(pageName, params);
                }

                @Override
                public void pagePausedEvent(String pageName, Map<String, String> params) {
                    connector.logger().pagePausedEvent(pageName, params);
                }

                @Override
                public void pageFailEvent(String pageName, String result, int errorCode) {
                    connector.logger().pageFailEvent(pageName, result, errorCode);
                }

                @Override
                public void pageCustomEvent(String eventId, Map<String, String> params) {
                    connector.logger().pageCustomEvent(eventId, params);
                }

                @Override
                public void baseEvent(String eventId, Map<String, String> params) {
                    connector.logger().baseEvent(eventId, params);
                }

                @Override
                public void baseEventSync(String eventId, Map<String, String> params) {
                    connector.logger().baseEventSync(eventId, params);
                }

                @Override
                public void submitBaseEventWithPolicy(String eventId, Map<String, String> params, int policyTime, int policyMaxLine) {
                    connector.logger().submitBaseEventWithPolicy(eventId, params, policyTime, policyMaxLine);
                }
            };
        }
        return logger;
    }

    @Override
    public boolean debugMode() {
        return connector.debugMode();
    }

    @Override
    public int homeVersion() {
        return connector.homeVersion();
    }

    @Override
    public Iot iot() {
        if (iot == null) {
            iot = new Iot() {
                @Override
                public String accessToken() {
                    return connector.iot().accessToken();
                }

                @Override
                public void addListener(final IotListener listener) {
                }

                @Override
                public void removeListener(final IotListener listener) {
                }
            };
        }
        return iot;
    }
}
