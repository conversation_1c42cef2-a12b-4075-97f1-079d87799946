package com.smarthome.plugin;

import android.content.Context;

import org.junit.Test;
import org.junit.runner.RunWith;

import static org.junit.Assert.*;

/**
 * Instrumented test, which will execute on an Android device.
 *
 * @see <a href="http://d.android.com/tools/testing">Testing documentation</a>
 */
@RunWith(AndroidJUnit4.class)
public class ExampleInstrumentedTest {
    @Test
    public void useAppContext() {
        // Context of the app under test.1
        Context appContext = InstrumentationRegistry.getInstrumentation().getTargetContext();

        assertEquals("com.smarthome.plugin.test", appContext.getPackageName());
    }
}
