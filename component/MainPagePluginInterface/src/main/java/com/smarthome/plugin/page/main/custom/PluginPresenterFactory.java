package com.smarthome.plugin.page.main.custom;

import android.content.Context;

import com.coocaa.uisdk.listener.IPresenterFactoryV8;
import com.coocaa.uisdk.listener.IPresenterV8;
import com.coocaa.uisdk.model.Plugin;
import com.coocaa.uisdk.utils.SupportUtilV8;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/8/24
 */
public class PluginPresenterFactory implements IPresenterFactoryV8 {

    public PluginPresenterFactory() {
        SupportUtilV8.addSupportType(PluginBlockPresenter.TYPE, Plugin.class);
    }

    @Override
    public IPresenterV8 createPresenter(String type, Context context) {
        if (PluginBlockPresenter.TYPE.equals(type)) {
            return new PluginBlockPresenter(context);
        }
        return null;
    }
}
