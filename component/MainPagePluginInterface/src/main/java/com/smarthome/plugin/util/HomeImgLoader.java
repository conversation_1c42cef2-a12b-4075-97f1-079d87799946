package com.smarthome.plugin.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.ColorFilter;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.widget.ImageView;

import com.coocaa.uisdk.listener.IImageLoaderV8;
import com.coocaa.uisdk.listener.OnBitmapLoadListenerV8;
import com.coocaa.uisdk.listener.OnFinalCallback;
import com.coocaa.uisdk.utils.CircleParamsV8;
import com.skyworth.util.imageloader.CircleParams;
import com.skyworth.util.imageloader.FinalCallback;
import com.skyworth.util.imageloader.ImageLoader;
import com.skyworth.util.imageloader.OnBitmapLoadListener;
import com.skyworth.util.imageloader.fresco.CoocaaDraweeView;

/**
 * @Author: yuzhan
 */
public class HomeImgLoader implements IImageLoaderV8 {
    @Override
    public void init(Context context) {

    }

    @Override
    public void setDebugMode(boolean debugMode) {
        ImageLoader.getLoader().setDebugMode(debugMode);
    }

    @Override
    public void destroy() {
        ImageLoader.getLoader().destroy();
    }

    @Override
    public View getView(Context context) {
        return ImageLoader.getLoader().getView(context);
    }

    @Override
    public View getView(Context context, boolean original) {
        return ImageLoader.getLoader().getView(context, original);
    }

    @Override
    public IImageLoaderV8 with(Context context) {
        ImageLoader.getLoader().with(context);
        return this;
    }

    @Override
    public IImageLoaderV8 setPlaceHolder(int id) {
        ImageLoader.getLoader().setPlaceHolder(id);
        return this;
    }

    @Override
    public IImageLoaderV8 setPlaceHolder(Drawable drawable) {
        ImageLoader.getLoader().setPlaceHolder(drawable);
        return this;
    }

    @Override
    public IImageLoaderV8 load(String url) {
        ImageLoader.getLoader().load(url);
        return this;
    }

    @Override
    public IImageLoaderV8 circle(CircleParamsV8 params) {
        CircleParams p = null;
        if(params.asCircle()) {
            p = new CircleParams(true);
        } else {
            p = new CircleParams(params.getRadius());
        }
        ImageLoader.getLoader().circle(p);
        return this;
    }

    @Override
    public IImageLoaderV8 resize(int width, int height) {
        ImageLoader.getLoader().resize(width, height);
        return this;
    }

    @Override
    public IImageLoaderV8 showAnimation(boolean show) {
        ImageLoader.getLoader().showAnimation(show);
        return this;
    }

    @Override
    public IImageLoaderV8 showFade(boolean fade) {
        ImageLoader.getLoader().showFade(fade);
        return this;
    }

    @Override
    public IImageLoaderV8 showFade(boolean fade, int duration) {
        ImageLoader.getLoader().showFade(fade, duration);
        return this;
    }

    @Override
    public void into(View view) {
        ImageLoader.getLoader().into(view);
    }

    @Override
    public IImageLoaderV8 setScaleType(ImageView.ScaleType scaleType) {
        ImageLoader.getLoader().setScaleType(scaleType);
        return this;
    }

    @Override
    public IImageLoaderV8 wrapContent() {
        ImageLoader.getLoader().wrapContent();
        return this;
    }

    @Override
    public IImageLoaderV8 finalCallback(final OnFinalCallback callback) {
        if(callback == null)
            return this;
        FinalCallback finalCallback = new FinalCallback() {
            @Override
            public void onFinal(String s, int i, int i1) {
                callback.onFinal(s, i, i1);
            }

            @Override
            public void onFailed(String s, Throwable throwable) {
                callback.onFailed(s, throwable);
            }
        };
        ImageLoader.getLoader().finalCallback(finalCallback);
        return this;
    }

    @Override
    public void getBitmap(final OnBitmapLoadListenerV8 listener) {
        if(listener == null)
            return ;
        OnBitmapLoadListener onBitmapLoadListener = new OnBitmapLoadListener() {
            @Override
            public void loadSuccess(Bitmap bitmap, String s) {
                listener.loadSuccess(bitmap, s);
            }

            @Override
            public void loadFailed(String s) {
                listener.loadFailed(s);
            }
        };
        ImageLoader.getLoader().getBitmap(onBitmapLoadListener);
    }

    @Override
    public void startAnimation(View view) {
        ImageLoader.getLoader().startAnimation(view);
    }

    @Override
    public void stopAnimation(View view) {
        ImageLoader.getLoader().stopAnimation(view);
    }

    @Override
    public IImageLoaderV8 setLeftTopCorner(float corner) {
        ImageLoader.getLoader().setLeftTopCorner(corner);
        return this;
    }

    @Override
    public IImageLoaderV8 setLeftBottomCorner(float corner) {
        ImageLoader.getLoader().setLeftBottomCorner(corner);
        return this;
    }

    @Override
    public IImageLoaderV8 setRightTopCorner(float corner) {
        ImageLoader.getLoader().setRightTopCorner(corner);
        return this;
    }

    @Override
    public IImageLoaderV8 setRightBottomCorner(float corner) {
        ImageLoader.getLoader().setRightBottomCorner(corner);
        return this;
    }

    @Override
    public IImageLoaderV8 setColorFilter(ColorFilter cf) {
        ImageLoader.getLoader().setColorFilter(cf);
        return this;
    }

    @Override
    public void clearCacheFromMemory(String url) {
        ImageLoader.getLoader().clearCacheFromMemory(url);
    }

    @Override
    public void reset(View view) {
        if(view instanceof CoocaaDraweeView) {
            ImageLoader.getLoader().reset(view);
        }
    }

    @Override
    public void clearCache(String url) {
        ImageLoader.getLoader().clearCache(url);
    }

    @Override
    public void updateThreadPriority(int priopity) {
        ImageLoader.getLoader().updateThreadPriority(priopity);
    }

    @Override
    public IImageLoaderV8 setAnimLoopCount(int count) {
        ImageLoader.getLoader().setAnimLoopCount(count);
        return this;
    }

    @Override
    public Context getContext() {
        return ImageLoader.getLoader().getContext();
    }
}
