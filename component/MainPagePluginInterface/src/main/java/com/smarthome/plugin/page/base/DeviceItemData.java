package com.smarthome.plugin.page.base;

import com.smarthome.plugin.model.DeviceBean;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/6
 */
public class DeviceItemData extends DeviceBean {

    public String itemType = ITEM_TYPE_DEVICE;//ITEM_TYPE_DEVICE.设备item，ITEM_TYPE_ADD.添加设备item

    public void copy(DeviceBean deviceBean) {
        this.device_id = deviceBean.device_id;
        this.familyId = deviceBean.familyId;
        this.device_type_id = deviceBean.device_type_id;
        this.device_icon = deviceBean.device_icon;
        this.device_icon_for_ir = deviceBean.device_icon_for_ir;
        this.product_type_id = deviceBean.product_type_id;
        this.product_type = deviceBean.product_type;
        this.product_brand = deviceBean.product_brand;
        this.product_brand_id = deviceBean.product_brand_id;
        this.product_model = deviceBean.product_model;
        this.module_chip = deviceBean.module_chip;
        this.device_name = deviceBean.device_name;
        this.online_status = deviceBean.online_status;
        this.device_position = deviceBean.device_position;
        this.device_status_desc = deviceBean.device_status_desc;
        this.is_virtual = deviceBean.is_virtual;
        this.is_new = deviceBean.is_new;
        this.acess_type = deviceBean.acess_type;
        this.report_status = deviceBean.report_status;
        this.voice_tips = deviceBean.voice_tips;
        this.detail_layout = deviceBean.detail_layout;
        this.status_show = deviceBean.status_show;
        this.pasue_start = deviceBean.pasue_start;
    }

    public static final String ITEM_TYPE_DEVICE = "device";
    public static final String ITEM_TYPE_ADD = "add";
}
