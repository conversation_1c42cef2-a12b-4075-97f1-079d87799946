package com.smarthome.plugin.page.main;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.FrameLayout;

import com.alibaba.fastjson.JSONObject;
import com.ccos.tvlauncher.sdk.IDirection;
import com.ccos.tvlauncher.sdk.ITvLauncherPlugin;
import com.ccos.tvlauncher.sdk.PluginContentData;
import com.ccos.tvlauncher.sdk.TvLauncherPluginBoundaryCallback;
import com.coocaa.uisdk.listener.IPresenterV8;
import com.coocaa.uisdk.listener.OnBoundaryListenerV8;
import com.coocaa.uisdk.listener.OnItemClickListenerV8;
import com.coocaa.uisdk.model.BlockV8;
import com.coocaa.uisdk.model.ContainerV8;
import com.coocaa.uisdk.model.OnClickDataV8;
import com.coocaa.uisdk.model.Plugin;
import com.coocaa.uisdk.presenter.PresenterFactoryV8;
import com.coocaa.uisdk.utils.SupportUtilV8;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.callback.LifecycleInterface;
import com.smarthome.plugin.page.main.carditem.BaseCardItemView;
import com.smarthome.plugin.page.main.carditem.DeviceCardItemView;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/5/29
 */
public class SmartHomePluginView extends FrameLayout implements LifecycleInterface {

    private TvLauncherPluginBoundaryCallback mBoundaryCallback;
    private ITvLauncherPlugin mTvLauncherPlugin;
    private IPresenterV8 mPresenterV8;
    private List<BaseCardItemView> mCardViews = new ArrayList<>();

    private OnBoundaryListenerV8 mBoundaryListenerV8 = new OnBoundaryListenerV8() {
        @Override
        public boolean onLeftBoundary(View leaveView, ContainerV8 c, int position) {
            return mBoundaryCallback.onPluginContentBoundary(mTvLauncherPlugin, IDirection.LEFT);
        }

        @Override
        public boolean onTopBoundary(View leaveView, ContainerV8 c, int position) {
            return mBoundaryCallback.onPluginContentBoundary(mTvLauncherPlugin, IDirection.TOP);
        }

        @Override
        public boolean onDownBoundary(View leaveView, ContainerV8 c, int position) {
            return mBoundaryCallback.onPluginContentBoundary(mTvLauncherPlugin, IDirection.DOWN);
        }

        @Override
        public boolean onRightBoundary(View leaveView, ContainerV8 c, int position) {
            return mBoundaryCallback.onPluginContentBoundary(mTvLauncherPlugin, IDirection.RIGHT);
        }

        @Override
        public boolean onBackPressed() {
            return mBoundaryCallback.onPluginContentBoundary(mTvLauncherPlugin, IDirection.BACK);
        }
    };

    public SmartHomePluginView(Context context) {
        super(context);
        setClipChildren(false);
        setClipToPadding(false);
    }

    public void setCallback(TvLauncherPluginBoundaryCallback callback, ITvLauncherPlugin tvLauncherPlugin) {
        mBoundaryCallback = callback;
        mTvLauncherPlugin = tvLauncherPlugin;
    }

    public void create(PluginContentData contentData) {
        try {
            if (EmptyUtils.isEmpty(contentData) || EmptyUtils.isEmpty(contentData.content)) {
                //todo test
//                contentData = new PluginContentData();
//                contentData.content = testJson;
            }
            CCLog.i("SmartHomePluginView", "contentData:" + contentData.toString());
            if (EmptyUtils.isNotEmpty(contentData) && EmptyUtils.isNotEmpty(contentData.content)) {
                ContainerV8 c = JSONObject.parseObject(contentData.content, ContainerV8.class);
                c.parseContent();
                //id是版面id，主页会传过来
                SupportUtilV8.parseContent(c, 0);
                mPresenterV8 = PresenterFactoryV8.getInstance().createPresenter(c.rawType, getContext());
                mPresenterV8.setContainer(c);
                mPresenterV8.setOnBoundaryListener(mBoundaryListenerV8);
                mPresenterV8.setOnItemClickListener(new OnItemClickListenerV8() {
                    @Override
                    public void click(View v, ContainerV8 c) {
                        try {
                            Object contentObject = c.contentObject;
                            OnClickDataV8 onClickData = null;
                            //根据自己的类型选中Block或者Plugin
                            if (contentObject instanceof BlockV8) {
                                ((BlockV8) contentObject).block_content.parseAction();
                                onClickData = ((BlockV8) contentObject).block_content.parsedAction;
                            }
                            if (contentObject instanceof Plugin) {
                                //todo
                                //                            ((Plugin) contentObject).parseAction();
                                //                            onClickData = ((Plugin) contentObject).parsedAction;
                            }
                            if (onClickData != null) {
                                Intent intent = onClickData.buildIntent(getContext());
                                getContext().startActivity(intent);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
                addView(mPresenterV8.getView());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onLayoutShow() {
        if (mPresenterV8 != null) {
            mPresenterV8.onLayoutShow();
        }
    }

    public void setPageChangeDirection(int direction) {
        if (mPresenterV8 != null) {
            mPresenterV8.onPageChanedDirection(direction);
        }
    }

    public boolean obtainFocus() {
        if (mPresenterV8 != null) {
            return mPresenterV8.obtainFocus();
        }
        return false;
    }

    public void onDeliverPluginMessage(Bundle bundle) {
        if (bundle != null) {
            for (BaseCardItemView itemView : mCardViews) {
                if (itemView instanceof DeviceCardItemView) {
                    ((DeviceCardItemView) itemView).getPresenter().pushCallback(bundle);
                    break;
                }
            }
        }
    }

    public void onShow() {
        onLayoutShow();
        for (BaseCardItemView itemView : mCardViews) {
            itemView.onShow();
        }
    }

    public void onHide() {
        if (mPresenterV8 != null) {
            mPresenterV8.onLayoutHide(false);
        }
        for (BaseCardItemView itemView : mCardViews) {
            itemView.onHide();
        }
    }

    @Override
    public void onResume() {
        for (BaseCardItemView itemView : mCardViews) {
            itemView.onResume();
        }
    }

    @Override
    public void onPause() {
        for (BaseCardItemView itemView : mCardViews) {
            itemView.onPause();
        }
    }

    @Override
    public void onStop() {
        for (BaseCardItemView itemView : mCardViews) {
            itemView.onStop();
        }
    }

    @Override
    public void onDestroy() {
        try {
            for (BaseCardItemView itemView : mCardViews) {
                itemView.onDestroy();
            }
            mCardViews.clear();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String testJson = "{\n" +
            "    \"contents\": [\n" +
            "        {\n" +
            "            \"contents\": [\n" +
            "                {\n" +
            "                    \"contents\": null,\n" +
            "                    \"extra\": {\n" +
            "                        \"block_content\": {\n" +
            "                            \"action\": \"{\\\"bywhat\\\": \\\"action\\\", \\\"dowhat\\\": \\\"startActivity\\\", \\\"params\\\": {\\\"product_id\\\": \\\"22\\\"}, \\\"byvalue\\\": \\\"com.coocaa.smartmall.detail\\\", \\\"exception\\\": {\\\"name\\\": \\\"onclick_exception\\\", \\\"value\\\": {\\\"bywhat\\\": \\\"action\\\", \\\"dowhat\\\": \\\"startActivity\\\", \\\"params\\\": {\\\"id\\\": \\\"com.coocaa.smartmall\\\"}, \\\"byvalue\\\": \\\"coocaa.intent.action.APP_STORE_DETAIL\\\", \\\"packagename\\\": \\\"com.tianci.appstore\\\", \\\"versioncode\\\": \\\"-1\\\"}}, \\\"packagename\\\": \\\"com.coocaa.smartmall\\\", \\\"versioncode\\\": \\\"-1\\\"}\",\n" +
            "                            \"imgs\": {\n" +
            "                                \"corner_icons\": [],\n" +
            "                                \"poster\": {\n" +
            "                                    \"images\": [\n" +
            "                                        \"http://img.sky.fs.skysrt.com/tvos8_imgs_local/20200824/20200824200440004661_410*540\"\n" +
            "                                    ]\n" +
            "                                }\n" +
            "                            },\n" +
            "                            \"title\": \"商品1\"\n" +
            "                        },\n" +
            "                        \"block_seq\": null,\n" +
            "                        \"block_title\": {\n" +
            "                            \"sub_title\": {\n" +
            "                                \"size\": null,\n" +
            "                                \"text\": \"\"\n" +
            "                            },\n" +
            "                            \"title\": {\n" +
            "                                \"size\": null,\n" +
            "                                \"text\": \"\"\n" +
            "                            }\n" +
            "                        },\n" +
            "                        \"params\": \"openMode==app\"\n" +
            "                    },\n" +
            "                    \"height\": 540,\n" +
            "                    \"id\": 1660,\n" +
            "                    \"parents\": \"\",\n" +
            "                    \"plugin_info\": null,\n" +
            "                    \"type\": \"Block\",\n" +
            "                    \"width\": 410,\n" +
            "                    \"x\": 0,\n" +
            "                    \"y\": 0\n" +
            "                },\n" +
            "                {\n" +
            "                    \"contents\": null,\n" +
            "                    \"extra\": {\n" +
            "                        \"block_content\": {\n" +
            "                            \"action\": \"{\\\"bywhat\\\": \\\"action\\\", \\\"dowhat\\\": \\\"startActivity\\\", \\\"params\\\": {\\\"product_id\\\": \\\"29\\\"}, \\\"byvalue\\\": \\\"com.coocaa.smartmall.detail\\\", \\\"exception\\\": {\\\"name\\\": \\\"onclick_exception\\\", \\\"value\\\": {\\\"bywhat\\\": \\\"action\\\", \\\"dowhat\\\": \\\"startActivity\\\", \\\"params\\\": {\\\"id\\\": \\\"com.coocaa.smartmall\\\"}, \\\"byvalue\\\": \\\"coocaa.intent.action.APP_STORE_DETAIL\\\", \\\"packagename\\\": \\\"com.tianci.appstore\\\", \\\"versioncode\\\": \\\"-1\\\"}}, \\\"packagename\\\": \\\"com.coocaa.smartmall\\\", \\\"versioncode\\\": \\\"-1\\\"}\",\n" +
            "                            \"imgs\": {\n" +
            "                                \"corner_icons\": [],\n" +
            "                                \"poster\": {\n" +
            "                                    \"images\": [\n" +
            "                                        \"http://img.sky.fs.skysrt.com/tvos8_imgs_local/20200824/20200824200440004661_410*540\"\n" +
            "                                    ]\n" +
            "                                }\n" +
            "                            },\n" +
            "                            \"title\": \"商品2\"\n" +
            "                        },\n" +
            "                        \"block_seq\": null,\n" +
            "                        \"block_title\": {\n" +
            "                            \"sub_title\": {\n" +
            "                                \"size\": null,\n" +
            "                                \"text\": \"\"\n" +
            "                            },\n" +
            "                            \"title\": {\n" +
            "                                \"size\": null,\n" +
            "                                \"text\": \"\"\n" +
            "                            }\n" +
            "                        },\n" +
            "                        \"params\": \"openMode==app\"\n" +
            "                    },\n" +
            "                    \"height\": 540,\n" +
            "                    \"id\": 1661,\n" +
            "                    \"parents\": \"\",\n" +
            "                    \"plugin_info\": null,\n" +
            "                    \"type\": \"Block\",\n" +
            "                    \"width\": 410,\n" +
            "                    \"x\": 450,\n" +
            "                    \"y\": 0\n" +
            "                },\n" +
            "                {\n" +
            "                    \"contents\": null,\n" +
            "                    \"extra\": {\n" +
            "                        \"block_content\": {\n" +
            "                            \"action\": \"{\\\"bywhat\\\": \\\"class\\\", \\\"dowhat\\\": \\\"startActivity\\\", \\\"params\\\": {\\\"starttype\\\": \\\"2\\\"}, \\\"byvalue\\\": \\\"swaiotos.channel.iot.tv.MainActivity\\\", \\\"exception\\\": {\\\"name\\\": \\\"onclick_exception\\\", \\\"value\\\": {\\\"bywhat\\\": \\\"action\\\", \\\"dowhat\\\": \\\"startActivity\\\", \\\"params\\\": {\\\"id\\\": \\\"swaiotos.channel.iot\\\"}, \\\"byvalue\\\": \\\"coocaa.intent.action.APP_STORE_DETAIL\\\", \\\"packagename\\\": \\\"com.tianci.appstore\\\", \\\"versioncode\\\": \\\"-1\\\"}}, \\\"packagename\\\": \\\"swaiotos.channel.iot\\\", \\\"versioncode\\\": \\\"-1\\\"}\",\n" +
            "                            \"imgs\": {\n" +
            "                                \"corner_icons\": [],\n" +
            "                                \"poster\": {\n" +
            "                                    \"images\": [\n" +
            "                                        \"http://img.sky.fs.skysrt.com/tvos8_imgs_local/20200824/20200824200440716513_410*540.png\"\n" +
            "                                    ]\n" +
            "                                }\n" +
            "                            },\n" +
            "                            \"title\": \"小维\"\n" +
            "                        },\n" +
            "                        \"block_seq\": null,\n" +
            "                        \"block_title\": {\n" +
            "                            \"sub_title\": {\n" +
            "                                \"size\": null,\n" +
            "                                \"text\": \"\"\n" +
            "                            },\n" +
            "                            \"title\": {\n" +
            "                                \"size\": null,\n" +
            "                                \"text\": \"\"\n" +
            "                            }\n" +
            "                        },\n" +
            "                        \"params\": \"openMode==app\"\n" +
            "                    },\n" +
            "                    \"height\": 540,\n" +
            "                    \"id\": 1662,\n" +
            "                    \"parents\": \"\",\n" +
            "                    \"plugin_info\": null,\n" +
            "                    \"type\": \"Block\",\n" +
            "                    \"width\": 410,\n" +
            "                    \"x\": 900,\n" +
            "                    \"y\": 0\n" +
            "                },\n" +
            "                {\n" +
            "                    \"contents\": null,\n" +
            "                    \"extra\": {\n" +
            "                        \"block_content\": {\n" +
            "                            \"action\": \"{\\\"bywhat\\\": \\\"action\\\", \\\"dowhat\\\": \\\"startActivity\\\", \\\"params\\\": {\\\"url\\\": \\\"https://webapp.skysrt.com/cc7.0/guide2/index.html?source=aiot&index=0\\\"}, \\\"byvalue\\\": \\\"coocaa.intent.action.browser.no_trans\\\", \\\"exception\\\": {}, \\\"packagename\\\": \\\"com.coocaa.app_browser\\\", \\\"versioncode\\\": \\\"102007\\\"}\",\n" +
            "                            \"imgs\": {\n" +
            "                                \"corner_icons\": [],\n" +
            "                                \"poster\": {\n" +
            "                                    \"images\": [\n" +
            "                                        \"http://img.sky.fs.skysrt.com/tvos8_imgs_local/20200824/20200824200440931905_410*540\"\n" +
            "                                    ]\n" +
            "                                }\n" +
            "                            },\n" +
            "                            \"title\": \"家居生态\"\n" +
            "                        },\n" +
            "                        \"block_seq\": null,\n" +
            "                        \"block_title\": {\n" +
            "                            \"sub_title\": {\n" +
            "                                \"size\": null,\n" +
            "                                \"text\": \"\"\n" +
            "                            },\n" +
            "                            \"title\": {\n" +
            "                                \"size\": null,\n" +
            "                                \"text\": \"\"\n" +
            "                            }\n" +
            "                        },\n" +
            "                        \"params\": \"openMode==webpage\"\n" +
            "                    },\n" +
            "                    \"height\": 540,\n" +
            "                    \"id\": 1663,\n" +
            "                    \"parents\": \"\",\n" +
            "                    \"plugin_info\": null,\n" +
            "                    \"type\": \"Block\",\n" +
            "                    \"width\": 410,\n" +
            "                    \"x\": 1350,\n" +
            "                    \"y\": 0\n" +
            "                }\n" +
            "            ],\n" +
            "            \"extra\": {\n" +
            "                \"panel_name\": \"智慧屏\",\n" +
            "                \"panel_title\": {\n" +
            "                    \"size\": null,\n" +
            "                    \"text\": \"\"\n" +
            "                },\n" +
            "                \"panel_version\": \"6\"\n" +
            "            },\n" +
            "            \"height\": null,\n" +
            "            \"id\": 1050,\n" +
            "            \"parents\": \"\",\n" +
            "            \"plugin_info\": null,\n" +
            "            \"type\": \"Panel\",\n" +
            "            \"width\": null,\n" +
            "            \"x\": null,\n" +
            "            \"y\": null\n" +
            "        }\n" +
            "    ],\n" +
            "    \"extra\": {\n" +
            "        \"bg\": \"\",\n" +
            "        \"orientation\": 1,\n" +
            "        \"space\": 40,\n" +
            "        \"title\": {\n" +
            "            \"size\": null,\n" +
            "            \"text\": \"\"\n" +
            "        }\n" +
            "    },\n" +
            "    \"height\": null,\n" +
            "    \"id\": null,\n" +
            "    \"parents\": \"\",\n" +
            "    \"plugin_info\": null,\n" +
            "    \"type\": \"Expander\",\n" +
            "    \"width\": null,\n" +
            "    \"x\": null,\n" +
            "    \"y\": null\n" +
            "}";
}
