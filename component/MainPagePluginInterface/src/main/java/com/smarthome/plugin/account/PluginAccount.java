package com.smarthome.plugin.account;

import android.content.Context;
import android.text.TextUtils;

import com.ccos.tvlauncher.sdk.IPluginConnector;
import com.smarthome.common.account.AccountInfo;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.EmptyUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/7/2
 */
public class PluginAccount {

    private Context mContext;
    private AccountInfo mAccountInfo;
    private IPluginConnector mConnector;
    private IPluginConnector.Executor mExecutor;
    private IPluginConnector.User mUserApi;
    private Map<String, String> mHeader;
    private static PluginAccount instance = null;
    private List<IPluginConnector.UserChangeListener> mUserChangeListenerList = new ArrayList<>();
    private IPluginConnector.UserChangeListener mUserChangeListener = new IPluginConnector.UserChangeListener() {
        @Override
        public void onUserChanged() {
            CCLog.i("onUserChanged change, hasLogin:" + hasLogin());
            loadAccountInfo();
            synchronized (mUserChangeListenerList) {
                for (IPluginConnector.UserChangeListener userChangeListener : mUserChangeListenerList) {
                    userChangeListener.onUserChanged();
                }
            }
        }
    };

    public static synchronized PluginAccount getInstance() {
        if (instance == null) {
            instance = new PluginAccount();
        }
        return instance;
    }

    private PluginAccount() {

    }

    public void init(Context context, IPluginConnector connector) {
        mContext = context;
        mConnector = connector;
        mUserApi = connector.user();
        mExecutor = connector.executor();
        loadAccountInfo();
        mUserApi.addUserChangeListener(mUserChangeListener);
    }

    public void setHeader(Map<String, String> header) {
        mHeader = header;
    }

    public Context getContext() {
        return mContext;
    }

    public IPluginConnector getConnector() {
        return mConnector;
    }

    public IPluginConnector.Executor executor() {
        return mExecutor;
    }

    public Map<String, String> getHeader() {
        if (mHeader == null) {
            return new HashMap<>();
        }
        return mHeader;
    }

    public void removeUserChangeListener() {
        mUserApi.removeUserChangeListener(mUserChangeListener);
    }

    public synchronized void registerUserChangeListener(IPluginConnector.UserChangeListener userChangeListener) {
        if (userChangeListener != null && !mUserChangeListenerList.contains(userChangeListener)) {
            mUserChangeListenerList.add(userChangeListener);
        }
    }

    public synchronized void unregisterUserChangeListener(IPluginConnector.UserChangeListener userChangeListener) {
        if (userChangeListener != null) {
            mUserChangeListenerList.remove(userChangeListener);
        }
    }

    public void loadAccountInfo() {
        IPluginConnector.User.UserInfo userInfo = mUserApi.getUserInfo();
        if (userInfo != null) {
            Map<String, Object> infoMap = userInfo.info;
            if (EmptyUtils.isNotEmpty(infoMap)) {
                mAccountInfo = new AccountInfo();
                mAccountInfo.address = getAccountValue(infoMap, "address");
                mAccountInfo.birthday = getAccountValue(infoMap, "birthday");
                mAccountInfo.gender = Integer.parseInt(getAccountValue(infoMap, "gender"));
                mAccountInfo.slogan = getAccountValue(infoMap, "slogan");
                mAccountInfo.mobile = getAccountValue(infoMap, "mobile");
                mAccountInfo.avatar = getAccountValue(infoMap, "avatar");
                if (TextUtils.isEmpty(mAccountInfo.avatar)) {
                    if (mAccountInfo.gender == 2) {
                        mAccountInfo.avatar = "USER_ICON_FEMALE";
                    } else {
                        mAccountInfo.avatar = "USER_ICON_MALE";
                    }
                }
                mAccountInfo.user_id = getAccountValue(infoMap, "open_id");
                mAccountInfo.token = userInfo.token;
                mAccountInfo.nick_name = getAccountValue(infoMap, "nick_name");
                return;
            }
        }
        mAccountInfo = null;
    }

    public boolean hasLogin() {
        if (mUserApi != null) {
            return mUserApi.hasLogin();
        }
        return false;
    }

    public boolean isBindMobile() {
        return mAccountInfo != null && EmptyUtils.isNotEmpty(mAccountInfo.mobile);
    }

    public void login() {
        if (mUserApi != null) {
            mUserApi.login(new HashMap<String, String>(), true, "");
        }
    }

    public AccountInfo getAccountInfo() {
        return mAccountInfo;
    }

    private String getAccountValue(Map<String, ?> info, String key) {
        String value = "";
        if (info != null && info.containsKey(key)) {
            Object obj = info.get(key);
            if (obj != null) {
                value = obj.toString();
            }
        }
        return value;
    }

}
