package com.smarthome.plugin.page.source;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.utils.XThemeUtils;
import com.smarthome.plugin.page.base.DeviceItemData;
import com.smarthome.plugin.page.main.deviceitem.SmallDeviceItemView;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/7/7
 */
public class SourceDeviceItemView extends SmallDeviceItemView implements NewRecycleAdapterItem<DeviceItemData> {

//    private View mFocusView;
    private View mLine;
    private CCFocusDrawable mFocusBg;
    private final static int FOCUS_W = Util.Div(16);

    public SourceDeviceItemView(Context context) {
        super(context);
        mFocusBg = new CCFocusDrawable(context).setRadius(Util.Div(8)).setBorderVisible(false).setSolidColor(Color.TRANSPARENT);
        setBackground(mFocusBg);
        addFocusView();
        addLineView();
        requestLayout();
    }

    private void addFocusView() {
//        mFocusView = new View(getContext());
//        mFocusView.setBackground(XThemeUtils.getDrawable(0, Color.WHITE, Util.Div(4), Util.Div(16)));
//        LayoutParams params = new LayoutParams(Util.Div(560) + FOCUS_W, Util.Div(160) + FOCUS_W);
//        params.gravity = Gravity.CENTER;
//        addView(mFocusView, params);
//        mFocusView.setVisibility(INVISIBLE);
    }

    private void addLineView() {
        mLine = new View(getContext());
        mLine.setBackgroundColor(Color.parseColor("#10ffffff"));
        LayoutParams params = new LayoutParams(Util.Div(560), Util.Div(1));
        params.topMargin = Util.Div(160 + 4);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mLine, params);
    }

    @Override
    public void addBg() {
        mBgView = new View(getContext());
        LayoutParams params = new LayoutParams(Util.Div(560), Util.Div(160));
        params.gravity = Gravity.CENTER;
        addView(mBgView, params);
    }

    @Override
    public void addIcon() {
        mIcon = ImageLoader.getLoader().getView(getContext());
        LayoutParams params = new LayoutParams(Util.Div(100), Util.Div(100));
        params.gravity = Gravity.CENTER_VERTICAL;
        params.leftMargin = Util.Div(40) + FOCUS_W;
        addView(mIcon, params);
    }

    @Override
    public void addName() {
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.leftMargin = Util.Div(180) + FOCUS_W;
        params.topMargin = Util.Div(34) + FOCUS_W;
        mDeviceName = new TextView(getContext());
        mDeviceName.setTextColor(Color.parseColor("#aaFFFFFF"));
        mDeviceName.setTextSize(Util.Dpi(28));
        mDeviceName.getPaint().setFakeBoldText(true);
        addView(mDeviceName, params);
    }

    @Override
    public void addStatus() {
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.leftMargin = Util.Div(180) + FOCUS_W;
        params.topMargin = Util.Div(92) + FOCUS_W;
        mStatus = new TextView(getContext());
        mStatus.setTextColor(Color.parseColor("#66FFFFFF"));
        mStatus.setTextSize(Util.Dpi(24));
        addView(mStatus, params);
    }

    @Override
    public View getView() {
        return this;
    }

    @Override
    public void onFocusChange(View view, boolean b) {
        if (b) {
//            mBgView.setBackground(XThemeUtils.getDrawable(Color.parseColor("#FFFFFF"), 0, 0, Util.Div(8)));
            mDeviceName.setTextColor(Color.BLACK);
            mStatus.setTextColor(Color.BLACK);
//            mFocusView.setVisibility(VISIBLE);
        } else {
            mBgView.setBackground(null);
            mDeviceName.setTextColor(Color.parseColor("#aaFFFFFF"));
            mStatus.setTextColor(Color.parseColor("#66FFFFFF"));
//            mFocusView.setVisibility(INVISIBLE);
        }
        mFocusBg.setBorderVisible(b).setSolidColor(b ? Color.WHITE : Color.TRANSPARENT);
    }

    @Override
    public void onUpdateData(DeviceItemData deviceItemData, int i) {
        refreshUI(deviceItemData);
    }

    @Override
    public void clearItem() {

    }

    @Override
    public void refreshUI() {

    }

    @Override
    public void destroy() {

    }
}
