package com.smarthome.plugin.util;

import android.content.Context;
import android.content.Intent;

import com.smarthome.common.dataer.LogSDK;
import com.smarthome.common.utils.Android;
import com.smarthome.common.utils.Constants;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XNetworkDialog;
import com.smarthome.plugin.page.base.DeviceItemData;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/7/4
 */
public class PluginUtil {

    public static void gotoAddDevice(Context context) {
        try {
            Intent intent = new Intent("com.smarthome.action.HOME");
            intent.putExtra(Constants.KEY_DEVICE_ID, Constants.DO_WHAT_ADD_DEVICE);
            context.startActivity(intent);

            Map<String, String> params = new HashMap<>();
            params.put("source", "信号源点击");
            PluginLogger.submit(LogSDK.EVENT_ID_ADD_DEVICE, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void startSmartHome(Context context, DeviceItemData data) {
        startSmartHome(context, "", data);
    }

    public static void startSmartHome(Context context, String source, DeviceItemData data) {
        try {
            if (!isNetConnected(context)) {
                return;
            }
            Intent intent = new Intent("com.smarthome.action.HOME");
            intent.putExtra(Constants.KEY_DEVICE_ID, data != null ? data.device_id : "null");
            context.startActivity(intent);
            submitClickLog(source, data);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void submitClickLog(String source, DeviceItemData data) {
        String eventID;
        Map<String, String> params = new HashMap<>();
        if (data != null && EmptyUtils.isNotEmpty(source) && source.equals("source")) {
            eventID = LogSDK.EVENT_ID_LIST_CLICK;
            params.put(data.is_virtual ? "virtual_device" : "real_device", "true");
            params.put("device_id", data.device_id);
            params.put("device_brand", data.product_brand);
            params.put("device_name", data.device_name);
        } else {
            eventID = LogSDK.EVENT_ID_PLUGIN_CLICK;
            params.put("plugin_name", "智能设备");
        }
        PluginLogger.submit(eventID, params);
    }

    /**
     * 返回网络连接状态，并且显示去连网弹窗
     *
     * @param context
     * @return
     */
    public static boolean isNetConnected(final Context context) {
        if (Android.isNetConnected(context)) {
            return true;
        }
        XNetworkDialog.showConnectNetDialog(context);
        return false;
    }
}
