package com.smarthome.plugin.aiotdata;

import android.content.Context;

/**
 * @ClassName: IAIOTDataModel
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2020/6/30 16:38
 * @Description:
 */
public interface IAIOTDataModel {
    IAIOTDataModel INSTANCE = new AIOTDataModel();

    /**
     * 初始接口
     */
    void init(Context context);

    void registerAiotCallback(IAiotCallback callback);

    void unRegisterAiotCallback(IAiotCallback callback);

    /**
     * 获取设备列表
     *
     * @param familyId
     */
    void getDeviceList(String familyId);
}
