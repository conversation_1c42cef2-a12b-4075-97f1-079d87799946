package com.smarthome.plugin.page.base;

import android.content.Context;
import android.view.KeyEvent;
import android.view.View;
import android.widget.FrameLayout;

import com.ccos.tvlauncher.sdk.IDirection;
import com.smarthome.common.utils.callback.LifecycleInterface;
import com.smarthome.plugin.page.main.SmartHomeBoundaryCallback;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/30
 */
public abstract class BaseItemView<T> extends FrameLayout implements LifecycleInterface {

    protected int mCardPosition;
    protected SmartHomeBoundaryCallback mSmartHomeBoundaryCallback;

    public BaseItemView(Context context, int pos) {
        super(context);
        setClipChildren(false);
        setClipToPadding(false);
        mCardPosition = pos;
    }

    public void setBoundaryCallback(SmartHomeBoundaryCallback boundaryCallback) {
        mSmartHomeBoundaryCallback = boundaryCallback;
    }

    public boolean onKeyDown(View leaveView, int keyCode) {
        switch (keyCode) {
            case KeyEvent.KEYCODE_DPAD_UP:
                return onBoundary(leaveView, IDirection.TOP);
            case KeyEvent.KEYCODE_DPAD_DOWN:
                return onBoundary(leaveView, IDirection.DOWN);
            case KeyEvent.KEYCODE_DPAD_LEFT:
                return onBoundary(leaveView, IDirection.LEFT);
            case KeyEvent.KEYCODE_DPAD_RIGHT:
                return onBoundary(leaveView, IDirection.RIGHT);
            case KeyEvent.KEYCODE_BACK:
                return onBoundary(leaveView, IDirection.BACK);
            default:
                break;
        }
        return false;
    }

    public boolean onBoundary(View leaveView, @IDirection int direction) {
        return mSmartHomeBoundaryCallback.onBoundary(leaveView, mCardPosition, direction);
    }

    public void refreshUI(T data) {

    }

    public boolean getFocus() {
        return false;
    }

    public void onShow() {

    }

    public void onHide() {

    }

    @Override
    public void onResume() {

    }

    @Override
    public void onPause() {

    }

    @Override
    public void onStop() {

    }

    @Override
    public void onDestroy() {

    }
}
