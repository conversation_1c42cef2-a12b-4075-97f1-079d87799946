package com.smarthome.plugin.page.main.carditem;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;

import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.utils.XThemeUtils;
import com.smarthome.plugin.page.base.BaseItemView;

/**
 * @Description: 运营卡片
 * @Author: wzh
 * @CreateDate: 2020/6/5
 */
public abstract class BaseCardItemView<T> extends BaseItemView<T> {
    protected FrameLayout mContentLayout;
    protected View mPoster;
    protected final static int ITEM_CORNER = Util.Div(16);

    public BaseCardItemView(Context context, int pos) {
        super(context, pos);
        mContentLayout = new FrameLayout(context);
        mContentLayout.setClipChildren(false);
        mContentLayout.setClipToPadding(false);
        LayoutParams params = new LayoutParams(Util.Div(410 + 10 + 40), Util.Div(540 + 10));
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mContentLayout, params);

        mPoster = ImageLoader.getLoader().getView(context);
        mPoster.setBackground(XThemeUtils.getDrawable(Color.parseColor("#19ffffff"), 0, 0, ITEM_CORNER));
        params = new LayoutParams(Util.Div(410), Util.Div(540));
        params.gravity = Gravity.CENTER;
        mContentLayout.addView(mPoster, params);
    }
}
