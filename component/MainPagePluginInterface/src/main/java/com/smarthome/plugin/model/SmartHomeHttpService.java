package com.smarthome.plugin.model;

import com.coocaa.app.core.http.HttpServiceManager;
import com.smarthome.common.model.SmartBaseData;

import java.util.Map;

import retrofit2.Call;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/10
 */
public class SmartHomeHttpService extends HttpServiceManager<SmartHomeHttpMethod> {

    public static final SmartHomeHttpService SERVICE = new SmartHomeHttpService();

    public SmartHomeHttpService() {
        super(SmartHomeHttpConfig.getDeviceServer(), SmartHomeHttpConfig.SMARTHOME_HEADER_LOADER);
    }

    @Override
    protected Class<SmartHomeHttpMethod> getServiceClazz() {
        return SmartHomeHttpMethod.class;
    }

    public Call<SmartBaseData<OperationCardBean>> getOperationCardList(String name) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("name", name);
        return getHttpService().getOperationCardList(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("name"),
                SmartHomeHttpConfig.getSign(map));
    }
}
