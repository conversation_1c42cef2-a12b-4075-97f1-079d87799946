package com.smarthome.plugin.page.main.custom;

import android.content.Context;
import android.view.View;

import com.skyworth.smarthome_tv.pluginmanager.boundarycallback.IBoundaryCallback;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/8/24
 */
public class PluginBlockPresenter extends Base<PERSON>lockPresenter {
    public final static String TYPE = "BLOCK_LOAD_PLUGIN";

    private BaseBlockLayout mView;

    private IBoundaryCallback mBoundaryCallback = new IBoundaryCallback() {
        @Override
        public boolean onTop(View v) {
            return onTopBoundary(v, mContainer, mPosition);
        }

        @Override
        public boolean onLeft(View v) {
            return onLeftBoundary(v, mContainer, mPosition);
        }

        @Override
        public boolean onRight(View v) {
            return onRightBoundary(v, mContainer, mPosition);
        }

        @Override
        public boolean onDown(View v) {
            return onDownBoundary(v, mContainer, mPosition);
        }

        @Override
        public boolean onBackKey(View v) {
            return onBackPressed();
        }
    };

    public PluginBlockPresenter(Context context) {
        super(context);
        mView = makeView(context);
    }

    @Override
    public BaseBlockLayout makeView(Context context) {
        return new PluginBlockLayout(context, mBoundaryCallback);
    }

    @Override
    public View getView() {
        return mView;
    }

    @Override
    public boolean obtainFocus() {
        return mView.obtainFocus();
    }

    @Override
    public View getTopFirstView() {
        return mView.getFocusView();
    }

    @Override
    public void onDestroy() {
        mView.onDestroy();
    }
}