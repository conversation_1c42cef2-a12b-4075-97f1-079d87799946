package com.smarthome.plugin.page.source;

import android.content.Context;
import android.os.Bundle;
import android.view.View;

import com.alibaba.fastjson.JSONObject;
import com.ccos.tvlauncher.sdk.IPluginConnector;
import com.ccos.tvlauncher.sdk.sourcepage.ISourceView;
import com.coocaa.app.core.utils.FuncKt;
import com.smarthome.common.sal.SalImpl;
import com.smarthome.common.utils.Android;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.plugin.account.PluginAccount;
import com.smarthome.plugin.aiotdata.IAIOTDataModel;
import com.smarthome.plugin.aiotdata.IAiotCallback;
import com.smarthome.plugin.model.DevcieStatusBean;
import com.smarthome.plugin.model.DeviceBean;
import com.smarthome.plugin.model.PluginCacheData;
import com.smarthome.plugin.page.base.DeviceItemData;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import swaiotos.sal.network.INetwork;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/7/7
 */
public class SourcePluginPresenter implements IAiotCallback {
    private final static String TAG = "SourcePluginPresenter";
    private Context mContext;
    private SourcePluginView mView;
    private ISourceView mISourceView;
    private String mScreenId;
    private List<DeviceItemData> mDeviceList = new ArrayList<>();
    private boolean needReload = false;
    private IPluginConnector.UserChangeListener mUserChangeListener = new IPluginConnector.UserChangeListener() {
        @Override
        public void onUserChanged() {
            CCLog.i(TAG, "onUserChanged hasLogin:" + PluginAccount.getInstance().hasLogin());
            IAIOTDataModel.INSTANCE.init(mContext);
            loadData();
        }
    };
    private INetwork.INetworkListener iNetworkListener = new INetwork.INetworkListener() {
        @Override
        public void onNetChanged(boolean b, int i) {
            if (b && needReload) {
                CCLog.i(TAG, "onNetChanged : reload data.");
                loadData();
            }
        }
    };

    public SourcePluginPresenter(Context context, SourcePluginView view, ISourceView sourceView) {
        mContext = context;
        mView = view;
        mISourceView = sourceView;
        IAIOTDataModel.INSTANCE.registerAiotCallback(this);
        PluginAccount.getInstance().registerUserChangeListener(mUserChangeListener);
        SalImpl.getSAL(mContext).addNetListener(iNetworkListener);
    }

    public void loadData() {
        CCLog.i(TAG, "loadData");
        mScreenId = PluginCacheData.getScreenId(mContext);
        String familyId = PluginCacheData.getFamilyId(mContext);
        if (!Android.isNetConnected(mContext)) {
            needReload = true;
            mView.showErrorView("网络未连接", "去连网", new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    SalImpl.getSAL(mContext).showNetSettings();
                    mISourceView.dismissDialog();
                }
            });
            return;
        }
        needReload = false;
        IAIOTDataModel.INSTANCE.getDeviceList(familyId);
        createAddItemData();
    }

    public void onDeliverPluginMessage(Bundle bundle) {
        if (bundle != null) {
            String type = bundle.getString("PUSH_TYPE");
            String data = bundle.getString("PUSH_DATA");
            CCLog.i(TAG, "pushCallback type:" + type + "--data:" + data);
            if (EmptyUtils.isNotEmpty(type)) {
                switch (type) {
                    case "DEVICE_STATUS":
                        if (EmptyUtils.isNotEmpty(data)) {
                            notifyDeviceItemChanged(data);
                        }
                        break;
                    case "DEVICE_LIST":
                        if (EmptyUtils.isNotEmpty(data)) {
                            onDeviceList(JSONObject.parseArray(data, DeviceBean.class));
                        }
                        break;
                    default:
                        break;
                }
            }
        }
    }

    private void notifyDeviceItemChanged(String data) {
        try {
            DevcieStatusBean devcieStatusBean = JSONObject.parseObject(data, DevcieStatusBean.class);
            if (devcieStatusBean != null) {
                for (final DeviceItemData itemData : mDeviceList) {
                    if (itemData.device_id.equals(devcieStatusBean.device_id)) {
                        if (EmptyUtils.isNotEmpty(devcieStatusBean.device_name)) {
                            itemData.device_name = devcieStatusBean.device_name;
                        }
                        if (EmptyUtils.isNotEmpty(devcieStatusBean.online_status)) {
                            itemData.online_status = Integer.parseInt(devcieStatusBean.online_status);
                        }
                        if (EmptyUtils.isNotEmpty(devcieStatusBean.device_status_desc)) {
                            itemData.device_status_desc = devcieStatusBean.device_status_desc;
                        }
                        if (EmptyUtils.isNotEmpty(devcieStatusBean.status)) {
                            JSONObject reportStatus = JSONObject.parseObject(itemData.report_status);
                            JSONObject updateStatus = JSONObject.parseObject(devcieStatusBean.status);
                            Object[] updateStatusArr = updateStatus.keySet().toArray();
                            if (EmptyUtils.isNotEmpty(updateStatusArr)) {
                                for (Object key : updateStatusArr) {
                                    reportStatus.put((String) key, updateStatus.getString((String) key));
                                }
                            }
                            itemData.report_status = reportStatus.toJSONString();
                        }
                        CCLog.i(TAG, "notifyDeviceItemChanged:" + data);
                        FuncKt.runOnUiThread(new Function0<Unit>() {
                            @Override
                            public Unit invoke() {
                                mView.notifyDeviceItemChanged(itemData);
                                return Unit.INSTANCE;
                            }
                        });
                        break;
                    }
                }
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onDeviceList(List<DeviceBean> list) {
        if (EmptyUtils.isNotEmpty(list)) {
            mDeviceList.clear();
            CCLog.i(TAG, "onDeviceList: list.size:" + list.size());
            boolean needAdd = false;
            for (DeviceBean bean : list) {
                //过滤本机设备
                if (EmptyUtils.isNotEmpty(mScreenId) && bean.device_id.equals(mScreenId)) {
                    continue;
                }
                if (bean.is_virtual) {
                    needAdd = true;
                }
                DeviceItemData data = new DeviceItemData();
                data.copy(bean);
                mDeviceList.add(data);
            }
            if (needAdd) {
                DeviceItemData data = new DeviceItemData();
                data.itemType = DeviceItemData.ITEM_TYPE_ADD;
                mDeviceList.add(data);
            }
            refreshUI();
        } else {
            CCLog.i(TAG, "onDeviceList: null");
            createAddItemData();
        }
    }

    private void createAddItemData() {
        mDeviceList.clear();
        DeviceItemData data = new DeviceItemData();
        data.itemType = DeviceItemData.ITEM_TYPE_ADD;
        mDeviceList.add(data);
        refreshUI();
    }

    private void refreshUI() {
        FuncKt.runOnUiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.refreshUI(mDeviceList);
                return Unit.INSTANCE;
            }
        });
    }

    public void onDestroy() {
        IAIOTDataModel.INSTANCE.unRegisterAiotCallback(this);
        PluginAccount.getInstance().unregisterUserChangeListener(mUserChangeListener);
        SalImpl.getSAL(mContext).removeNetListener(iNetworkListener);
    }
}
