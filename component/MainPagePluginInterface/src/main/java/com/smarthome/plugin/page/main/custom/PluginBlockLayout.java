package com.smarthome.plugin.page.main.custom;

import android.content.Context;
import android.content.Intent;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ImageView;

import com.coocaa.uisdk.model.BlockV8;
import com.coocaa.uisdk.model.ContainerV8;
import com.skyworth.smarthome_tv.pluginmanager.IPluginViewLoadListener;
import com.skyworth.smarthome_tv.pluginmanager.PluginManager;
import com.skyworth.smarthome_tv.pluginmanager.boundarycallback.IBoundaryCallback;
import com.skyworth.smarthome_tv.pluginmanager.lifecyclecallback.ILifeCycleCallback;
import com.skyworth.smarthome_tv.pluginmanager.type.IPluginViewType;
import com.skyworth.ui.api.widget.SimpleFocusDrawable;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.EmptyUtils;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/8/24
 */
public class PluginBlockLayout extends BaseBlockLayout {

    private final static String TAG = "PluginBlockLayout";
    private IBoundaryCallback mBoundaryCallback;
    private View mPosterView;
    private SimpleFocusDrawable mPosterFocusBg;
    private String mPosterUrl;
    private String mCurrentPluginPkg;
    private final static int ITEM_CORNER = Util.Div(16);

    public PluginBlockLayout(Context context, IBoundaryCallback boundaryCallback) {
        super(context);
        mBoundaryCallback = boundaryCallback;
        createPosterView();
    }

    private void createPosterView() {
        mPosterFocusBg = new SimpleFocusDrawable(getContext()).setRadius(Util.Div(16));
        mPosterView = ImageLoader.getLoader().getView(getContext());
        mPosterView.setBackground(mPosterFocusBg);
        LayoutParams params = new LayoutParams(Util.Div(410 + 10), Util.Div(540 + 10));
        params.gravity = Gravity.CENTER;
        addView(mPosterView, params);
        setPosterFocusable(true);
        mPosterView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (EmptyUtils.isEmpty(mCurrentPluginPkg)) {
                    return;
                }
                try {
                    Intent intent = getContext().getPackageManager().getLaunchIntentForPackage(mCurrentPluginPkg);
                    getContext().startActivity(intent);
                } catch (Exception e) {
                    e.printStackTrace();
                    try {
                        Intent appstore = new Intent("coocaa.intent.action.APP_STORE_DETAIL");
                        appstore.putExtra("id", mCurrentPluginPkg);
                        getContext().startActivity(appstore);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }
        });
        mPosterView.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View view, boolean b) {
                mPosterFocusBg.setFocus(b);
            }
        });
        mPosterView.setOnKeyListener(new OnKeyListener() {
            @Override
            public boolean onKey(View view, int keyCode, KeyEvent event) {
                if (event.getAction() == KeyEvent.ACTION_DOWN) {
                    switch (keyCode) {
                        case KeyEvent.KEYCODE_DPAD_UP:
                            return mBoundaryCallback.onTop(view);
                        case KeyEvent.KEYCODE_DPAD_DOWN:
                            return mBoundaryCallback.onDown(view);
                        case KeyEvent.KEYCODE_DPAD_LEFT:
                            return mBoundaryCallback.onLeft(view);
                        case KeyEvent.KEYCODE_DPAD_RIGHT:
                            return mBoundaryCallback.onRight(view);
                        case KeyEvent.KEYCODE_ESCAPE:
                        case KeyEvent.KEYCODE_BACK:
                            return mBoundaryCallback.onBackKey(view);
                        default:
                            break;
                    }
                }
                return false;
            }
        });
    }

    @Override
    public void setBlockData(ContainerV8 container) {
        CCLog.i(TAG, "setBlockData: " + container.toString());
        parsePosterUrl(container);
        PluginBlockData data = container.parsePluginParams(PluginBlockData.class);
        mCurrentPluginPkg = data.load_pkg;
        if (EmptyUtils.isNotEmpty(data) && EmptyUtils.isNotEmpty(data.load_pkg)) {
            //加载插件View
            loadPluginView(data);
        } else {
            loadPoster();
        }
    }

    private void parsePosterUrl(ContainerV8 container) {
        try {
            Object contentObject = container.contentObject;
            if (contentObject instanceof BlockV8) {
                BlockV8.BlockContent blockContent = ((BlockV8) contentObject).block_content;
                if (EmptyUtils.isNotEmpty(blockContent.imgs) && EmptyUtils.isNotEmpty(blockContent.imgs.poster) && EmptyUtils.isNotEmpty(blockContent.imgs.poster.images)) {
                    mPosterUrl = blockContent.imgs.poster.images.get(0);
                    CCLog.i(TAG, "parsePosterUrl  mPosterUrl:" + mPosterUrl);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void loadPluginView(PluginBlockData data) {
        PluginManager.getInstance().getView(data.load_pkg, data.view_type, new IPluginViewLoadListener() {
            @Override
            public void onLoadSuccess(String packageName, IPluginViewType type, View view, ILifeCycleCallback cycleCallback) {
                CCLog.i(TAG, "load plugin success:" + packageName);
                removeAllViews();
                addView(view);
            }

            @Override
            public void onLoadFail(String packageName, IPluginViewType type, String failReason) {
                CCLog.i(TAG, "load plugin fail:" + packageName + "---failReason:" + failReason);
                loadPoster();
            }
        }, mBoundaryCallback);
    }

    private void setPosterFocusable(boolean focusable) {
        mPosterView.setFocusable(focusable);
        mPosterView.setFocusableInTouchMode(focusable);
    }

    private void loadPoster() {
        if (mPosterView == null || mPosterView.getParent() == null) {
            createPosterView();
        }
        if (EmptyUtils.isNotEmpty(mPosterUrl)) {
            ImageLoader.getLoader().with(getContext()).load(mPosterUrl).resize(Util.Div(410), Util.Div(540)).setScaleType(ImageView.ScaleType.CENTER)
                    .setLeftTopCorner(ITEM_CORNER).setLeftBottomCorner(ITEM_CORNER).setRightTopCorner(ITEM_CORNER).setRightBottomCorner(ITEM_CORNER).into(mPosterView);
        }
    }

}
