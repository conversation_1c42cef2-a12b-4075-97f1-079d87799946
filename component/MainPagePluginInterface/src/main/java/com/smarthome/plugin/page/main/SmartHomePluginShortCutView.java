package com.smarthome.plugin.page.main;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ccos.tvlauncher.sdk.IDirection;
import com.ccos.tvlauncher.sdk.ITvLauncherPlugin;
import com.ccos.tvlauncher.sdk.TvLauncherPluginBoundaryCallback;
import com.skyworth.smarthome_tv.pluginmanager.IPluginViewLoadListener;
import com.skyworth.smarthome_tv.pluginmanager.PluginManager;
import com.skyworth.smarthome_tv.pluginmanager.boundarycallback.IBoundaryCallback;
import com.skyworth.smarthome_tv.pluginmanager.lifecyclecallback.ILifeCycleCallback;
import com.skyworth.smarthome_tv.pluginmanager.type.IPluginViewType;
import com.skyworth.ui.api.HorizontalScrollView;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.ui.api.widget.SimpleFocusDrawable;
import com.skyworth.util.Util;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.Constants;
import com.smarthome.common.utils.callback.LifecycleInterface;
import com.smarthome.plugin.R;
import com.smarthome.plugin.model.OperationCardBean;
import com.smarthome.plugin.page.base.DeviceItemData;
import com.smarthome.plugin.page.main.carditem.BaseCardItemView;
import com.smarthome.plugin.page.main.carditem.DeviceCardItemView;
import com.smarthome.plugin.page.main.carditem.PluginCardItemView;
import com.swaiot.aiotlib.common.util.ThreadManager;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:主页插件快捷入口
 * @Author: AwenZegn
 * @CreateDate: 2020/8/22
 */
public class SmartHomePluginShortCutView extends LinearLayout implements LifecycleInterface, View.OnClickListener, View.OnKeyListener {
    private FrameLayout mPluginView;
    private ImageView mLeftItemView, mCenterItemView, mRightItemView;
    private TvLauncherPluginBoundaryCallback mBoundaryCallback;
    private ILifeCycleCallback mLifeCycleCallback;
    private ITvLauncherPlugin mITvLauncherPlugin;
    private CCFocusDrawable mPluginFocusDrawable;
    private CCFocusDrawable mLeftFocusDrawable;
    private CCFocusDrawable mCenterFocusDrawable;
    private CCFocusDrawable mRightFocusDrawable;

    public SmartHomePluginShortCutView(Context context) {
        super(context);
        setClipChildren(false);
        setClipToPadding(false);
        setClickable(true);
        setFocusable(true);
        initData();
        initTittle();
        initPluginView();
        initMoreItem();
        initListener();
    }

    private void initData() {
        setOrientation(VERTICAL);
        mPluginFocusDrawable = new CCFocusDrawable(getContext()).setRadius(Util.Div(18)).setBorderVisible(false).setSolidColor(Color.TRANSPARENT);
        mLeftFocusDrawable = new CCFocusDrawable(getContext()).setRadius(Util.Div(18)).setBorderVisible(false).setSolidColor(Color.TRANSPARENT);
        mCenterFocusDrawable = new CCFocusDrawable(getContext()).setRadius(Util.Div(18)).setBorderVisible(false).setSolidColor(Color.TRANSPARENT);
        mRightFocusDrawable = new CCFocusDrawable(getContext()).setRadius(Util.Div(18)).setBorderVisible(false).setSolidColor(Color.TRANSPARENT);
    }

    private void initTittle() {
        TextView titleTv = new TextView(getContext());
        titleTv.setText("智慧屏");
        titleTv.setTextSize(Util.Dpi(48));
        titleTv.setTextColor(Color.parseColor("#FFFFFF"));
        LayoutParams layoutParams = new LayoutParams(LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = Util.Div(60);
        layoutParams.leftMargin = Util.Div(80);
        addView(titleTv, layoutParams);
    }


    private void initMoreItem() {
        LinearLayout contentLayout = new LinearLayout(getContext());
        contentLayout.setOrientation(HORIZONTAL);
        LayoutParams layoutParams = new LayoutParams(LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = Util.Div(40);
        layoutParams.leftMargin = Util.Div(80);
        layoutParams.rightMargin = Util.Div(80);
        addView(contentLayout, layoutParams);

        mLeftItemView = new ImageView(getContext());
        mLeftItemView.setScaleType(ImageView.ScaleType.CENTER_CROP);
        mLeftItemView.setTag("left");
        mLeftItemView.setImageResource(R.drawable.video_communicate_bg);
        mLeftItemView.setBackground(mLeftFocusDrawable);
        mLeftItemView.setPadding(Util.Div(6), Util.Div(10), Util.Div(6), Util.Div(10));
        layoutParams = new LayoutParams(Util.Div(562), Util.Div(269));
        contentLayout.addView(mLeftItemView, layoutParams);

        mCenterItemView = new ImageView(getContext());
        mCenterItemView.setTag("center");
        mCenterItemView.setScaleType(ImageView.ScaleType.CENTER_CROP);
        mCenterItemView.setImageResource(R.drawable.leave_message_bg);
        mCenterItemView.setBackground(mCenterFocusDrawable);
        mCenterItemView.setPadding(Util.Div(6), Util.Div(10), Util.Div(6), Util.Div(10));
        layoutParams = new LayoutParams(Util.Div(562), Util.Div(269));
        layoutParams.leftMargin = Util.Div(40);
        contentLayout.addView(mCenterItemView, layoutParams);

        mRightItemView = new ImageView(getContext());
        mRightItemView.setTag("right");
        mRightItemView.setScaleType(ImageView.ScaleType.CENTER_CROP);
        mRightItemView.setImageResource(R.drawable.smart_devicelist_bg);
        mRightItemView.setBackground(mRightFocusDrawable);
        mRightItemView.setPadding(Util.Div(6), Util.Div(10), Util.Div(6), Util.Div(10));
        layoutParams = new LayoutParams(Util.Div(562), Util.Div(269));
        layoutParams.leftMargin = Util.Div(40);
        contentLayout.addView(mRightItemView, layoutParams);
        mLeftItemView.setFocusable(true);
        mCenterItemView.setFocusable(true);
        mRightItemView.setFocusable(true);
    }

    private void initPluginView() {
        mPluginView = new FrameLayout(getContext());
        mPluginView.setBackground(mPluginFocusDrawable);
        mPluginView.setTag("plugin");
        mPluginView.setClipChildren(false);
        mPluginView.setClipToPadding(false);
        LayoutParams layoutParams = new LayoutParams(Util.Div(1760 + 14), Util.Div(550 + 14));
        layoutParams.topMargin = Util.Div(40);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mPluginView, layoutParams);
        PluginManager.getInstance().getView("swaiotos.channel.iot", "CARD_VIEW", new IPluginViewLoadListener() {
            @Override
            public void onLoadSuccess(String packageName, IPluginViewType type, View view, ILifeCycleCallback callback) {
                CCLog.i("PluginCardItemView", "load plugin success:" + packageName);
                mLifeCycleCallback = callback;
                mPluginView.addView(view);
            }

            @Override
            public void onLoadFail(String packageName, IPluginViewType type, String failReason) {
                CCLog.i("PluginCardItemView", "load plugin fail:" + packageName + "---failReason:" + failReason);
                setDefaultPluginView();
            }

        }, new IBoundaryCallback() {
            @Override
            public boolean onTop(View leaveView) {
                return mBoundaryCallback.onPluginShortcutBoundary(mITvLauncherPlugin, IDirection.BACK);
            }

            @Override
            public boolean onLeft(View leaveView) {
                Util.instance().nope_X(leaveView).start();
                return true;
            }

            @Override
            public boolean onRight(View leaveView) {
                Util.instance().nope_X(leaveView).start();
                return true;
            }

            @Override
            public boolean onDown(View leaveView) {
                mLeftItemView.requestFocus();
                return true;
            }

            @Override
            public boolean onBackKey(View leaveView) {
                return mBoundaryCallback.onPluginShortcutBoundary(mITvLauncherPlugin, IDirection.BACK);
            }
        });
        mPluginView.setFocusable(true);
    }

    private void initListener() {
        mLeftItemView.setOnClickListener(this);
        mCenterItemView.setOnClickListener(this);
        mRightItemView.setOnClickListener(this);
        mPluginView.setOnClickListener(this);
        mLeftItemView.setOnKeyListener(this);
        mCenterItemView.setOnKeyListener(this);
        mRightItemView.setOnKeyListener(this);
        mPluginView.setOnKeyListener(new OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if (event.getAction() == KeyEvent.ACTION_DOWN) {
                    switch (keyCode) {
                        case KeyEvent.KEYCODE_DPAD_UP:
                        case KeyEvent.KEYCODE_BACK:
                            return mBoundaryCallback.onPluginShortcutBoundary(mITvLauncherPlugin, IDirection.BACK);
                        case KeyEvent.KEYCODE_DPAD_DOWN:
                            mLeftItemView.requestFocus();
                            return true;
                        case KeyEvent.KEYCODE_DPAD_LEFT:
                        case KeyEvent.KEYCODE_DPAD_RIGHT:
                            Util.instance().nope_X(v).start();
                            return true;
                        default:
                            break;
                    }
                }
                return false;
            }
        });
        mPluginView.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                mPluginFocusDrawable.setBorderVisible(hasFocus).setSolidColor(getResources().getColor(R.color.translucent));
                Util.forceFocusAnim(v, hasFocus);
            }
        });
        mLeftItemView.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                mLeftFocusDrawable.setBorderVisible(hasFocus).setSolidColor(getResources().getColor(R.color.translucent));
                Util.forceFocusAnim(v, hasFocus);
            }
        });
        mCenterItemView.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                mCenterFocusDrawable.setBorderVisible(hasFocus).setSolidColor(getResources().getColor(R.color.translucent));
                Util.forceFocusAnim(v, hasFocus);
            }
        });
        mRightItemView.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                mRightFocusDrawable.setBorderVisible(hasFocus).setSolidColor(getResources().getColor(R.color.translucent));
                Util.forceFocusAnim(v, hasFocus);
            }
        });
    }

    private void setDefaultPluginView() {
        View defaultView = new View(getContext());
        defaultView.setBackgroundResource(R.drawable.plugin_item_bg);
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(1760), Util.Div(550));
        layoutParams.gravity = Gravity.CENTER;
        mPluginView.addView(defaultView, layoutParams);
    }

    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            switch (keyCode) {
                case KeyEvent.KEYCODE_DPAD_DOWN:
                    if (v.getTag().equals("left") || v.getTag().equals("center") || v.getTag().equals("right")) {
                        Util.instance().nope_Y(v).start();
                    }
                    return false;
                case KeyEvent.KEYCODE_DPAD_LEFT:
                    if (v.getTag().equals("left")) {
                        Util.instance().nope_X(v).start();
                    }
                    return false;
                case KeyEvent.KEYCODE_DPAD_RIGHT:
                    if (v.getTag().equals("right")) {
                        Util.instance().nope_X(v).start();
                    }
                    return false;
                case KeyEvent.KEYCODE_BACK:
                    return mBoundaryCallback.onPluginShortcutBoundary(mITvLauncherPlugin, IDirection.BACK);
                default:
                    break;
            }
        }
        return false;
    }


    public void setCallback(TvLauncherPluginBoundaryCallback callback, ITvLauncherPlugin tvLauncherPlugin) {
        mBoundaryCallback = callback;
        mITvLauncherPlugin = tvLauncherPlugin;
    }


    public boolean obtainFocus() {
        CCLog.i("shortcutObtainFocus: ");
        return mPluginView.requestFocus();
    }

    @Override
    public void onClick(View v) {
        String packageName = "";
        switch ((String) v.getTag()) {
            case "plugin":
                packageName = "swaiotos.channel.iot";
                break;
            case "left":
                packageName = "com.coocaa.tvpitv";
                break;
            case "center":
                packageName = "com.trensai.msgboard";
                break;
            case "right":
                packageName = "com.skyworth.smarthome_tv";
                Intent intent = new Intent("com.smarthome.action.HOME");
                intent.putExtra(Constants.KEY_DEVICE_ID, "null");
                getContext().startActivity(intent);
                return;
            default:
                break;
        }
        try {
            Intent intent = getContext().getPackageManager().getLaunchIntentForPackage(packageName);
            getContext().startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
            try {
                Intent appstore = new Intent("coocaa.intent.action.APP_STORE_DETAIL");
                appstore.putExtra("id", packageName);
                getContext().startActivity(appstore);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    @Override
    public void onResume() {
        if (mLifeCycleCallback != null) {
            mLifeCycleCallback.onResume();
        }
    }

    @Override
    public void onPause() {
        if (mLifeCycleCallback != null) {
            mLifeCycleCallback.onPause();
        }
    }

    @Override
    public void onStop() {
        if (mLifeCycleCallback != null) {
            mLifeCycleCallback.onStop();
        }
    }

    @Override
    public void onDestroy() {
        if (mLifeCycleCallback != null) {
            mLifeCycleCallback.onDestroy();
        }
    }
}






