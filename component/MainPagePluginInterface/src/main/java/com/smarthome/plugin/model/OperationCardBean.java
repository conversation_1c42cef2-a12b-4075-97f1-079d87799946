package com.smarthome.plugin.model;

import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/22
 */
public class OperationCardBean implements Serializable {

    public List<CardData> cards;

    public static class CardData implements Serializable {
        public CardData() {

        }

        public CardData(String pkg, String cardName, String poster, String viewType) {
            this.pkg = pkg;
            this.cardName = cardName;
            this.poster = poster;
            this.viewType = viewType;
        }

        public String cardName;
        public String pkg;
        public String poster;//默认海报图
        public String viewType;
        public int width;
        public int height;
        public String params;
    }

}
